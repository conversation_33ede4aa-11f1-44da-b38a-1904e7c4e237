import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import styles from '../styles/ViewBookPujaList.styles';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import AppRoutes from '../../../constants/AppRoutes';
import Apis from '../../../services/apiList';
import {getKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';
import {GetServices} from '../../../services/commonApiMethod';
import moment from 'moment';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useFocusEffect} from '@react-navigation/native';

const ViewBookPujaList = ({navigation, route}) => {
  const api = new Apis();
  const {margins, paddings} = metrics;
  const [viewList, setViewList] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const formatDate = date => {
    return moment(date).format('Do-MMM-YYYY');
  };

  const viewBookPujaListRender = ({item, index}) => {
    const formattedDate = formatDate(item.date);

    const imageSource = item.puja_image;
    return (
      <TouchableOpacity 
      onPress={() => {
        const {
          firstname,
          email,
          date,
          language,
          mobile_number,
          alternative_number,
          location,
          puja_name,
          city,
          country,
          state,
          price,
          id,
          payment_status
        } = item;
        navigation.navigate(AppRoutes.VIEW_PUJA_DETAILS, {
          firstname,
          email,
          date,
          language,
          mobile_number,
          alternative_number,
          location,
          puja_name,
          city,
          country,
          state,
          price,
          id,
          payment_status
        });
      }}
      style={styles.renderItemContanier}>
        <Image
          style={styles.renderIcon}
          resizeMode="cover"
          source={{uri: imageSource}}
        />

        <View style={styles.textContainer}>
          <Text style={styles.renderPujaText}>{item.puja_name}</Text>

          {/* Date and state side by side */}
          <View style={styles.dateAndStateContainer}>
            <Text style={styles.subDetailsText}>{formattedDate}, </Text>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={[styles.subDetailsText, {...margins.mB2, ...margins.mL5}]}>
              {item.state}
            </Text>
          </View>

          {/* <Text style={{...margins.mT22, right: 20,...fontSize.f8}}>{item.email}</Text> */}
        </View>

        <View style={styles.buttonStyle} >
          <TouchableOpacity
            onPress={() => {
              const {
                firstname,
                email,
                date,
                language,
                mobile_number,
                alternative_number,
                location,
                puja_name,
                city,
                country,
                state,
                price,
                id,
                payment_status
              } = item;
              navigation.navigate(AppRoutes.VIEW_PUJA_DETAILS, {
                // BookPujaDataDetails: BookPujaData
                firstname,
                email,
                date,
                language,
                mobile_number,
                alternative_number,
                location,
                puja_name,
                city,
                country,
                state,
                price,
                id,
                payment_status
              });
            }}
            style={styles.viewDetailsStyle}>
            <Text style={styles.viewDetailsText}>{item.payment_status === "Paid" ? I18n.t('paid') :I18n.t('view_details')}</Text>
          </TouchableOpacity>
          <View style={[styles.tickcircle, {borderColor: item.payment_status === "Paid" ? colors.muhuratText1 : colors.arrowDown}]}>
            <Ionicons name="checkmark" size={15} color={item.payment_status === "Paid" ? colors.muhuratText1 : colors.arrowDown } />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const fetchviewlist = async () => {
    setLoading(true);
    const userdetail = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    const userid = JSON.parse(userdetail).ID;
    // const num = 4;
    const payload = {
      url: `${api.viewbookpujalist}?user_id=${userid}`,
      // url: `${api.viewbookpujalist}?user_id=${num}`,
    };


    await GetServices(payload)
      .then(async res => {
        setLoading(false);
        
        if (res.data.length > 0) {
          const data = res?.data;
          const pujaimage = res?.data;
          setViewList(data);
        } else {
          console.log('No data recieved');
          setViewList([]);
        }
      })
      .catch(error => {
        setLoading(false);
        console.error('Error fetching data:', error);
      });
  };

  useFocusEffect(
    useCallback(() => {
      fetchviewlist()
    },[])
  )

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('view_book_list')}
          leftIconPress={handleLeftPress}
        />
      </View>

      {/* data-list  */}
      <View style={{...margins.mT16, ...margins.mB50}}>
        {loading ? (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : viewList.length === 0 ? (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>{I18n.t('no_puja_booked')}</Text>
          </View>
        ) : (
          <FlatList
            data={viewList}
            renderItem={viewBookPujaListRender}
            keyExtractor={item => item.id.toString()}
            ItemSeparatorComponent={() => (
              <View style={{height: DeviceUiInfo.moderateScale(10)}} />
            )}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default ViewBookPujaList;
