import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  FlatList,
  Alert,
} from 'react-native';
import styles from '../styles/ViewPujaDetails.styles';
import {colors} from '../../../theme/colors';
import I18n from '../../../utils/language/i18nextConfig';
import AppTitle from '../../../components/common/AppTitle';
import AppRoutes from '../../../constants/AppRoutes';
import AppButton from '../../../components/common/AppButton';
import metrics from '../../../theme/metrics';
import moment from 'moment';
import RazorpayCheckout from 'react-native-razorpay';
import Apis from '../../../services/apiList';
import {
  PostServices,
  PostServicesWithoutToken,
} from '../../../services/commonApiMethod';
import {getKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';

const ViewPujaDetails = ({navigation, route}) => {
  var api = new Apis();
  const {margins, paddings} = metrics;
  // const {BookPujaDataDetails} = route?.params;
  const {
    firstname,
    email,
    mobile_number,
    alternative_number,
    location,
    language,
    puja_name,
    date,
    city,
    country,
    price,
    id,
    payment_status,
  } = route?.params;
  const [priceEnabled, setPriceEnabled] = useState(false);
  const [orderId, setOrderId] = useState(null);
  const [razorOrderId, setRazorOrderId] = useState(null);
  const [razorPaymentId, setRazorPaymentId] = useState(null);

  // const detailsData = [
  //   { id: 1, title: 'Name', value: BookPujaDataDetails.name },
  //   { id: 2, title: 'Email', value: BookPujaDataDetails.email },
  //   { id: 3, title: 'Phone Number', value: BookPujaDataDetails.phonenumber },
  //   { id: 4, title: 'Alternative Number', value: BookPujaDataDetails.alternativeNumber },
  //   { id: 5, title: 'Location', value: BookPujaDataDetails.location },
  //   { id: 6, title: 'Language', value: BookPujaDataDetails.language },
  //   { id: 7, title: 'Puja', value: BookPujaDataDetails.puja },
  //   { id: 8, title: 'Date', value: `${BookPujaDataDetails.day} ${BookPujaDataDetails.month}, ${BookPujaDataDetails.year}` },
  //   { id: 9, title: 'City', value: BookPujaDataDetails.city },
  //   { id: 10, title: 'State', value: BookPujaDataDetails.state },
  //   { id: 11, title: 'Country', value: BookPujaDataDetails.country },
  //   { id: 12, title: 'ZIP Code', value: BookPujaDataDetails.zip_code },
  // ];

  const formatDate = date => {
    return moment(date).format('Do MMMM, YYYY');
  };

  useEffect(() => {

    const pricecheck = () => {
      if (price > 0) {
        setPriceEnabled(true);
      } else {
        setPriceEnabled(false);
      }
    };

    pricecheck();
  }, [price]);


  const detailsData = [
    {id: 1, title: 'Name', value: firstname || 'N/A'},
    {id: 2, title: 'Email', value: email || 'N/A'},
    {id: 3, title: 'Phone Number', value: mobile_number || 'N/A'},
    {id: 4, title: 'Alternative Number', value: alternative_number || 'N/A'},
    {
      id: 5,
      title: 'Location',
      value: city && country ? `${city}, ${country}` : 'N/A',
    },
    {id: 6, title: 'Language', value: language || 'N/A'},
    {id: 7, title: 'Puja', value: puja_name || 'N/A'},
    {id: 8, title: 'Date', value: formatDate(date) || 'N/A'},
    {
      id: 9,
      title: 'Price',
      value: priceEnabled ? price : 'We will update you shortly..' || 'N/A',
    },
  ];

  const paymentAPI = async () => {
    const userdetails = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    const userID = JSON.parse(userdetails).ID;
    const userEmail = JSON.parse(userdetails).email;

    var entryData = new FormData();
    entryData.append('entry_id', Number(id));
    entryData.append('order_id', razorOrderId);
    entryData.append('payment_id', razorPaymentId);
    entryData.append('user_id', userID);
    entryData.append('user_email', userEmail);
    entryData.append('price', price);
    entryData.append('type', 'Book A Pandit');

    const payload = {
      url: api.paymentupdate,
      data: entryData,
      type: 'formData',
    };

    await PostServicesWithoutToken(payload)
      .then(res => {
        console.log('message>>', res);
      })
      .catch(err => {
        console.log('showing catch error', err);
      });
  };

  const orderapi = async () => {
    var orderdata = new FormData();

    orderdata.append('price', price);
    orderdata.append('currency', 'INR');

    const payload = {
      url: api.orderapi,
      data: orderdata,
      type: 'formData',
    };

    await PostServices(payload)
      .then(res => {
        if (res?.data) {
          setOrderId(res?.data?.data?.order_id);
        }
      })
      .catch(err => {
        console.log('Order API error >>>', err);
      });
  };

  useEffect(() => {
    if (priceEnabled) {
      orderapi();
    }
  }, [priceEnabled]);

  //priceenable ? price :
  const handlepay = () => {
    var options = {
      description: 'Credits towards consultation',
      // image: 'https://i.imgur.com/3g7nmJC.png',
      currency: 'INR',
      key: 'rzp_test_u9hNkkwxyYeLTu',
      amount: price * 100,
      order_id: orderId,
      name: 'PanditNearMe',
      prefill: {
        email: '<EMAIL>',
        contact: '9191919191',
        name: 'Razorpay Software',
      },
      theme: {color: '#F37254'},
    };
    RazorpayCheckout.open(options)
      .then(data => {
        const {razorpay_order_id, razorpay_payment_id} = data;
        setRazorOrderId(razorpay_order_id);
        setRazorPaymentId(razorpay_payment_id);

        paymentAPI();
        Alert.alert('Payment', 'Paid Sucessfully');
        navigation.reset({
          index: 1,
          routes: [
            {name: AppRoutes.BOTTOM_NAV_BAR},
            {name: AppRoutes.VIEW_BOOK_PUJA_LIST},
          ],
        });
      })
      .catch(error => {
        Alert.alert('Payment', 'Failed to pay');
      });
  };

  const detailsRender = ({item, index}) => {
    return (
      <View style={{flexDirection: 'row'}}>
        <Text style={styles.renderTitleText}>{item.title}</Text>
        <Text style={[styles.renderTitleText, {...margins.mH5}]}>:</Text>
        <Text style={styles.renderValueText}>{item.value}</Text>
      </View>
    );
  };

  const handleLeftPress = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('view_puja_details')}
          leftIconPress={handleLeftPress}
        />
      </View>

      {/* details-contanier */}
      <View style={styles.detailsContanier}>
        <FlatList
          data={detailsData}
          renderItem={detailsRender}
          keyExtractor={(item, index) => item.id.toString()}
        />
      </View>

      {/* app-button */}
      <View
        style={[
          styles.appbtnContanier,
          !priceEnabled && payment_status === 'Paid' && {opacity: 0.5},
        ]}>
        <AppButton
          text={payment_status === 'Paid' ? I18n.t('paid') : I18n.t('pay_now')}
          onPressed={handlepay}
          // disabled={true}
          disabled={payment_status === 'Paid' || !priceEnabled}
        />
      </View>
    </SafeAreaView>
  );
};

export default ViewPujaDetails;
