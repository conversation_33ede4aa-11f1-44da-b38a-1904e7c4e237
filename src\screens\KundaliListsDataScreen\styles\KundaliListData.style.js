import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH20,
    
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB16,
  },
  tabNavigationContanier: {
    // borderWidth: 1,
    alignItems: 'center',
    shadowColor: colors.lightGrey,
    height:DeviceUiInfo.moderateScale(40)
  },
  activeText: {
    // borderWidth: 1,
    fontSize: fontSize.f14,
    color: colors.background,
    fontFamily: fonts.MontserratSemibold,
  },
  inactiveText: {
    fontSize: fontSize.f14,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratSemibold,
  },
  inactiveTab: {
    ...paddings.pH16,
    ...paddings.pV2,
    elevation: 2,
    borderRadius: DeviceUiInfo.moderateScale(20),
    backgroundColor: colors.white,
  },
  activeTab: {
    ...paddings.pH16,
    ...paddings.pV2,
    elevation: 2,
    borderRadius: DeviceUiInfo.moderateScale(20),
    backgroundColor: colors.primary,
  },
});

export default styles;
