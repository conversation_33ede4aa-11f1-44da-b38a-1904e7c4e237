import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {paddings, margins} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
  },
  textfieldContent: {
    ...margins.mT16,
  },
  appbtnContanier: {
    // ...margins.mB29,
    ...margins.mV16,
    justifyContent: 'flex-end',
    flex: 1,
  },
  dropdownContainer: {
    position: 'absolute',
    top: 65,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ccc',
    // zIndex: 1000,
    maxHeight: 160,
    borderRadius: 10,
    // overflow: 'hidden',
  },
  dropdownItem: {
    ...paddings.p10,
    ...fontSize.f16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },

  countrydropdown: {
    backgroundColor: colors.white,
    maxHeight: DeviceUiInfo.moderateScale(200),
    overflow: 'scroll',
    elevation: 1,
    borderRadius: DeviceUiInfo.moderateScale(10),
  },
});

export default styles;
