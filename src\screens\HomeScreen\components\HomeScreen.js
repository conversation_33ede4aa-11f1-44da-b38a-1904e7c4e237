import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StatusBar,
  Image,
  TouchableOpacity,
  FlatList,
  ImageBackground,
  Alert,
  ActivityIndicator,
  Dimensions,
  Linking,
} from 'react-native';
import styles from '../styles/HomeScreen.style';
import { colors } from '../../../theme/colors';
import { fonts, fontSize } from '../../../theme/fonts';
import { images } from '../../../theme/images';
import Icon from 'react-native-vector-icons/Entypo';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import ServiceSection from '../../../components/common/ServiceSection';
import I18n from '../../../utils/language/i18nextConfig';
import AppRoutes from '../../../constants/AppRoutes';
import moment from 'moment';
import Apis from '../../../services/apiList';
import {
  GetServices,
  PostServicesWithoutToken,
} from '../../../services/commonApiMethod';
import { getKey, saveKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import metrics from '../../../theme/metrics';
import AppInput from '../../../components/common/AppInput';
import {
  check,
  PERMISSIONS,
  request,
  RESULTS,
} from 'react-native-permissions';
import GetLocation from 'react-native-get-location';
import axios from 'axios';
import LoginModal from '../../../components/common/LoginModal';
import { customAppEvents } from '../../../analytics/AppEvents';
import { translateText } from '../../../services/translation';
import BlogScreen from '../../BlogScreen/components/BlogScreen';
import LinearGradient from 'react-native-linear-gradient';

const HomeScreen = ({ navigation }) => {
  const api = new Apis();
  const [selectCategory, setSelectCategory] = useState();
  const [puja, setPuja] = useState([]);
  const [homa, setHoma] = useState([]);
  const [rituals, setRituals] = useState([]);
  const [firstName, setFirstName] = useState('');
  const [loading, setLoading] = useState(true);
  const [askForLogin, setAskForLogin] = useState(false);
  const { margins, paddings } = metrics;
  const [placeholderText, setPlaceholderText] = useState(
    selectedLanguageValue === 'en' ? 'Puja' : 'पूजा',
  );
  const [panchang, setPanchang] = useState([]);
  const [lat, setLat] = useState(null);
  const [long, setLong] = useState(null);
  const [tZone, setTZone] = useState(null);
  const [isLoggedin, setIsLoggedin] = useState(false)

  const URL = 'https://json.astrologyapi.com/';
  const map_Access_Token =
    'pk.eyJ1IjoicGFydGgwMiIsImEiOiJjbTd4NWZ0bzcwMWxkMnFwZXRmeWgxbGZnIn0.y2KRmr1T5TBt938N9PVTHg';
  const { height, width } = Dimensions.get('window');
  const [selectedLanguageValue, setSelectedLanguageValue] = useState(
    I18n.language,
  );
  const [isDropdownVisible, setDropdownVisible] = useState(false);

  // useEffect(() => {
  //   I18n.changeLanguage(selectedLanguageValue);
  // }, [selectedLanguageValue]);


  useEffect(() => {
    const username = async () => {
      const name = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
      if (name) {
        setIsLoggedin(true)
      }
      else {
        setIsLoggedin(false)
      }
      console.log("name>>", name);
      const firstname = JSON.parse(name).first_name;
      if (!firstname) {
        navigation.reset(AppRoutes.PROFILE_SCREEN, {
          message: I18n.t('create_profile_message'),
        });
        return;
      }
      console.log("firstname>>", firstname);

      setFirstName(firstname);
      setLoading(false);
    };

    username();
  }, [firstName]);

  useEffect(() => {
    const placeholderCycle =
      selectedLanguageValue === 'en'
        ? ['Puja', 'Homa', 'Rituals']
        : ['पूजा', 'होम', 'रीतिरिवाज'];
    let index = 0;

    const interval = setInterval(() => {
      setPlaceholderText(placeholderCycle[index]);
      index = (index + 1) % placeholderCycle.length;
    }, 1500);

    return () => clearInterval(interval);
  }, [selectedLanguageValue]);

  // api calling panchang on homescreen
  useEffect(() => {
    const requestPermission = () => {
      check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION).then(async result => {
        if (RESULTS.DENIED === result) {
          request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION).then(
            async result => {
              if (RESULTS.GRANTED === result) {
                fetchLocation();
              }
            },
          );
        } else if (RESULTS.GRANTED === result) {
          fetchLocation();
        }
      });
    };

    const fetchLocation = () => {
      GetLocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 60000,
      })
        .then(async (location) => {
          if (lat !== location.latitude || long !== location.longitude) {
            setLat(location.latitude);
            setLong(location.longitude);
            await saveKey(ASYNC_KEY_CONSTANTS.LOCATIONLAT, JSON.stringify({ lat: location.latitude }))
            await saveKey(ASYNC_KEY_CONSTANTS.LOCATIONLONG, JSON.stringify({ long: location.longitude }))
          }
        })
        .catch(error => {
          const { code, message } = error;
        });
    };
    requestPermission();
  }, []);

  useEffect(() => {
    if (lat && long) {
      const tzoneAPI = async () => {
        var latlondata = new FormData();
        latlondata.append('latitude', lat);
        latlondata.append('longitude', long);

        const payload = {
          url: api.timezone,
          data: latlondata,
          type: 'formData',
        };

        await PostServicesWithoutToken(payload)
          .then(res => {
            if (res) {
              const tzone = res?.data?.data?.gmtOffset;
              setTZone(tzone);
            }
          })
          .catch(err => {
          });
      };

      tzoneAPI();
    }
  }, [lat, long]);

  useEffect(() => {
    if (lat && long && tZone !== null) {
      const panchangAPI = async () => {
        const basicAuthKey =
          'Bearer NjEyNTExOmEzYTAxNGVlMzVmMTljN2M4ZGRmNDJiZDFiNzk3MmJi';
        const locationlat = JSON.parse(await getKey(ASYNC_KEY_CONSTANTS.LOCATIONLAT))
        const locationlong = JSON.parse(await getKey(ASYNC_KEY_CONSTANTS.LOCATIONLONG))
        try {
          const response = await axios.post(
            `${URL}${api.panchang}`,
            {
              day: moment().date(),
              month: moment().month() + 1,
              year: moment().year(),
              hour: moment().hour(),
              min: moment().minute(),
              tzone: tZone,
              lat: locationlat.lat,
              lon: locationlong.long,
            },
            {
              headers: {
                Authorization: `${basicAuthKey}`,
                'Content-Type': 'application/json',
              },
            },
          );
          setPanchang(response?.data);
        } catch (error) {
        }
      };
      panchangAPI();
    }
  }, [lat, long, tZone]);

  //**************************************** */

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const getSavedLanguage = await getKey(
          ASYNC_KEY_CONSTANTS.LANGUAGESTORE,
        );
        if (getSavedLanguage) {
          setSelectedLanguageValue(getSavedLanguage);
          I18n.changeLanguage(getSavedLanguage);
        }
      } catch (error) {
        console.error('Error loading language from AsyncStorage:', error);
      }
    };

    loadLanguage();
  }, []);

  const handleLanguageSelect = async language => {
    console.log("Language select >> ", language);

    setDropdownVisible(false);
    if (language !== selectedLanguageValue) {
      setSelectedLanguageValue(language);
      I18n.changeLanguage(language);
      await saveKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE, language);
    }
  };

  useEffect(() => {
    const check = async () => {
      const checking = await getKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE);
    };
    check();
  }, []);

  const toggleDropdown = () => {
    // setDropdownVisible(!isDropdownVisible);
    setDropdownVisible((prev) => !prev);
  };

  const categories = [
    { id: 1, image: images.Horoscope, title: I18n.t('horoscope') },
    // {id: 2, image: images.Puja, title: I18n.t('puja')},
    // {id: 3, image: images.Homa, title: I18n.t('homa')},
    // {id: 5, image: images.Kundali, title: I18n.t('kundali')},
    { id: 4, image: images.Rituals, title: I18n.t('muhurat') },
    { id: 6, image: images.panchanghome, title: I18n.t('panchang') }
  ];

  const categoryClick = async (item) => {

    const titleMapping = {
      Horoscope: 'Horoscope',
      राशिफल: 'Horoscope',
      Kundali: 'Kundali',
      कुंडली: 'Kundali',
      Muhurat: 'Muhurat',
      मुहूर्त: 'Muhurat',
      Panchang: 'Panchang',
      पंचांग: 'Panchang',

      // Rituals: 'ritual',
      // 'रीति-रिवाज': 'ritual',
      // Puja: 'puja',
      // पूजा: 'puja',
      // Homa: 'homa',
      // होम: 'homa',
    };

    const originalTitle = titleMapping[item?.title] || item?.title;

    // changed to navigating based on the originalTitle
    if (originalTitle === 'Horoscope') {
      await customAppEvents("looking_horoscope")
      navigation.navigate(AppRoutes.HOROSCOPE_SCREEN, {
        language: selectedLanguageValue,
      });
    } else if (originalTitle === 'Kundali') {
      await customAppEvents("looking_kundali")
      // navigation.navigate(AppRoutes.KUNDALI_SCREEN);
      Alert.alert('Note', 'Coming Soon');
    } else if (originalTitle === 'Muhurat') {
      await customAppEvents("looking_muhurat")
      navigation.navigate(AppRoutes.MUHURAT_SCREEN, {
        language: selectedLanguageValue,
      });
    } else if (originalTitle === 'Panchang') {
      await customAppEvents("looking_panchang")
      navigation.navigate(AppRoutes.PANCHANG, {
        language: selectedLanguageValue,
      });
    }

    //----------------prev puja/homa/ritual flow code ------------------
    // else if (originalTitle === 'ritual') {
    //   navigation.navigate(AppRoutes.SERVICE_LIST_SCREEN, {
    //     title: 'ritual',
    //     titleInHindi: 'रीति-रिवाज',
    //     language: selectedLanguageValue,
    //   });
    // } else if (originalTitle === 'puja') {
    //   navigation.navigate(AppRoutes.SERVICE_LIST_SCREEN, {
    //     title: 'puja',
    //     titleInHindi: 'पूजा',
    //     language: selectedLanguageValue,
    //   });
    // } else if (originalTitle === 'homa') {
    //   navigation.navigate(AppRoutes.SERVICE_LIST_SCREEN, {
    //     title: 'homa',
    //     titleInHindi: 'होम',
    //     language: selectedLanguageValue,
    //   });
    // }
    // --------------------------------------------------------------------
  };

  const categoriesRenderItem = ({ item, index }) => {
    return (
      <View style={styles.categoryContanier} key={item.title}>
        <TouchableOpacity
          onPress={() => {
            categoryClick(item);
          }}
          style={styles.categoryIconContanier}>
          <Image source={item.image} style={styles.categoryIcon} />
        </TouchableOpacity>
        <Text
          style={[
            styles.categoryName,
            {
              color:
                selectCategory === index
                  ? colors.primary
                  : colors.poweredByTextColor,
              fontFamily:
                selectCategory === index
                  ? fonts.MontserratSemibold
                  : fonts.MontserratMedium,
            },
          ]}>
          {item.title}
        </Text>
      </View>
    );
  };

  useEffect(() => {
    fetchPujaData();
    fetchHomaData();
    fetchRitualsData();
  }, []);

  const fetchPujaData = async () => {
    const payload = {
      url: `${api.getdata}puja`,
    };

    setLoading(true);
    await GetServices(payload)
      .then(async (res) => {
        if (res) {
          const dataLimit = 4;
          const limitedpujaset = res?.data.slice(0, dataLimit);

          setPuja(limitedpujaset)


        } else {
          Alert.alert(
            'Error',
            'Didnot fetched data from api puja from homescreen',
          );
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => setLoading(false));
  };

  const fetchHomaData = async () => {
    const payload = {
      url: `${api.getdata}homa`,
    };
    setLoading(true);
    await GetServices(payload)
      .then(res => {
        if (res) {
          const dataLimit = 4;
          const limitedhomadata = res?.data?.slice(0, dataLimit);
          setHoma(limitedhomadata);
          setLoading(false);
        } else {
          Alert.alert(
            'Error',
            'Didnot fetched data from api homa from homecreen',
          );
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => setLoading(false));
  };

  const fetchRitualsData = async () => {
    const payload = {
      url: `${api.getdata}ritual`,
    };
    setLoading(true);
    await GetServices(payload)
      .then(res => {
        if (res) {
          const dataLimit = 4;
          const limitedritualsdata = res?.data?.slice(0, dataLimit);
          setRituals(limitedritualsdata);
          setLoading(false);
        } else {
          Alert.alert('Error', 'Didnot fetched data from api');
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => setLoading(false));
  };

  // const navigatemuharat = () => {
  //   navigation.navigate(AppRoutes.MUHURAT_SCREEN, {
  //     language: selectedLanguageValue,
  //   });
  // };

  //to display the user name on homescreen dynamically

  {
    loading && <ActivityIndicator size="large" color={colors.primary} />;
  }

  //......Whatsapp feature.........
  const handlewhatsapp = async () => {

    const phoneNumber = '+919724676277';
    const message = 'Hello, I Have A Query';
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          Linking.openURL(url);
        } else {
          console.log('WhatsApp is not installed or the URL is not supported.');
        }
      })
      .catch(err => console.error('An error occurred', err));
  };

  const handleBecomePandit = async () => {
    await customAppEvents("join_as_pandit");

    navigation.navigate(AppRoutes.BECOME_PANDITS, {
      language: selectedLanguageValue,
    });
  }

  return (
    <View style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* home-header */}
      <View style={styles.homeHeaderContanier}>
        <Text style={styles.useNameText}>
          {I18n.t('hello')} {firstName}!
        </Text>
        <View style={styles.headerIconContanier}>
          <TouchableOpacity
            style={styles.languageContanier}
            onPress={toggleDropdown}>
            <Image
              source={
                selectedLanguageValue === 'en' ? images.EnglishFlag : images.indiaFlag
              }
              style={styles.headerIconStyle}
            />
            <Text style={styles.languageText}>
              {selectedLanguageValue === 'en' ? 'EN' : 'HI'}
            </Text>
            <Icon
              name="chevron-down"
              size={DeviceUiInfo.moderateScale(12)}
              color={colors.arrowDown}
            />
          </TouchableOpacity>

          {
            isLoggedin ?
              null
              :
              <TouchableOpacity style={styles.btnContainer}
                onPress={() => navigation.navigate(AppRoutes.REGISTER_SCREEN)}>
                <Text style={{ fontFamily: fonts.MontserratBold, fontWeight: 'bold', fontSize: fontSize.f16, color: colors.primary }}>{I18n.t("login")}</Text>
              </TouchableOpacity>
          }


          {/* <TouchableOpacity
            style={{...margins.mR10}}
            onPress={() =>
              navigation.navigate(AppRoutes.SEARCH_SCREEN, {
                language: selectedLanguageValue,
              })
            }>
            <Image source={images.SearchIcon} style={styles.headerIconStyle} />
          </TouchableOpacity> */}

          {/* <TouchableOpacity onPress={() => console.log('notification-click')}>
            <Image
              source={images.NotificationBell}
              style={styles.headerIconStyle}
            />
            <View style={styles.headerNotificationIconStatusBar}></View>
          </TouchableOpacity> */}
        </View>
      </View>

      {/* {selectedLanguageValue && (
        <View style={styles.languageDropdown}>
          <TouchableOpacity onPress={() => handleLanguageSelect('en')}>
            <Text>English</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleLanguageSelect('hi')}>
            <Text>Hindi</Text>
          </TouchableOpacity>
        </View>
      )} */}


      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false} stickyHeaderIndices={[1]}>
          {/* date-panchang */}
          <View style={styles.datePanchangContanier}>
            <View style={{ flexDirection: 'column' }}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    ...styles.datePanchangText,
                    fontFamily: fonts.MontserratSemibold,
                  }}>
                  {moment().format('Do MMM YYYY')}
                </Text>
                <Text
                  style={{
                    ...styles.datePanchangText,
                    fontFamily: fonts.MontserratMedium,
                  }}>
                  {' '}
                  {panchang.day}
                </Text>
              </View>
              <Text style={styles.datePanchangSubtitleText}>
                {panchang?.hindu_maah?.purnimanta} {panchang?.paksha},{' '}
                {panchang?.tithi?.details?.tithi_name?.split(' ')[1]}
              </Text>
            </View>
          </View>

          {/* search bar */}
          <View style={styles.searchBarContainer}>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate(AppRoutes.SEARCH_SCREEN, {
                language: selectedLanguageValue,
              })
            }>
            <AppInput
              placeholderText={
                selectedLanguageValue === 'en'
                  ? `${I18n.t('search')} for ${placeholderText}`
                  : `${I18n.t('search')} ${placeholderText}`
              }
              // placeholder={(language === 'en' ? `${I18n.t('search')} for ${placeholderText}` : `${I18n.t('search')} ${placeholderText}` )}
              containerStyle={{ ...margins.mT8 }}
              isEditable={false}
              leftIcon={
                <Image
                  source={images.SearchIcon}
                  style={[
                    styles.headerIconStyle,
                    { right: 3, marginHorizontal: 15 },
                  ]}
                />
              }
            />
          </TouchableOpacity>
          </View>
          {/* get-your-kundali */}
          {/* <View style={styles.getYourKundaliContanier}>
            <Image
              source={images.getYourKundaliBG}
              style={styles.getYourKundaliBGImageStyle}
            />
            <View style={styles.kundaliContanier}>
              <Text style={styles.kundaliText}>
                {I18n.t('get_your_kundali')}
              </Text>
              <Text style={styles.kundaliSubtitleText}>
                {I18n.t('get_your_kundali_subtitle')}
              </Text>

              <TouchableOpacity
                // onPress={() => navigation.navigate(AppRoutes.KUNDALI_SCREEN)}
                onPress={() => Alert.alert('Note', 'Coming Soon')}
                style={styles.createKundaliBtn}>
                <Text style={styles.createKundaliText}>
                  {I18n.t('create_kundali')}
                </Text>
              </TouchableOpacity>
            </View>
          </View> */}

          {/* category-list */}
          <FlatList
            showsHorizontalScrollIndicator={false}
            data={categories}
            renderItem={categoriesRenderItem}
            ItemSeparatorComponent={() => (
              <View style={{ width: DeviceUiInfo.moderateScale(6) }} />
            )}
            // keyExtractor={(i, index) => index}
            keyExtractor={(index, item) => index.id.toString()}
            horizontal={true}
            contentContainerStyle={{ marginHorizontal: 'auto' }}
          />

          {/* become-a-pandit */}
          <ImageBackground
            resizeMode="cover"
            source={images.JoinAsPanditBanner}
            style={styles.becomePanditsBGStyle}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginHorizontal: 20,
                paddingVertical: 20
              }}>
              <View style={{ flex: 1, position: 'absolute', left: 170, top: -35, width: width * 0.30 }}>
                <Text style={styles.becomePanditsText}>
                  {isLoggedin ? "Consult With" : "Join As"}
                </Text>
                <Text style={[styles.becomePanditsText, { color: colors.lightOrange, marginBottom: 6 }]}>
                  Pandit Ji
                </Text>
                <Text style={styles.becomePanditsSubText}>
                  {isLoggedin ? "Tired of Love, Career, Financial problem ? " : "And get more verified service bookings"}
                </Text>
              </View>
              <TouchableOpacity
                style={{ flex: 1, alignItems: 'flex-end', position: 'absolute', left: 170, bottom: -35 }}
                onPress={() => handleBecomePandit()}>
                <LinearGradient
                  colors={['#F96B1E', '#FF9500']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.becomePanditApplyBG}>
                  <Text style={styles.applynowText}>{isLoggedin ? "Consult Free" : "Join Now"}</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </ImageBackground>

          {/* puja */}
          {puja && puja.length > 0 && (
            <View style={styles.contentContanier}>
              <ServiceSection
                navigation={navigation}
                title={I18n.t('puja')}
                data={puja}
                language={selectedLanguageValue}
              />
            </View>
          )}

          {/* homa */}
          {homa && homa.length > 0 && (
            <View style={styles.contentContanier}>
              <ServiceSection
                navigation={navigation}
                title={I18n.t('homa')}
                data={homa}
                language={selectedLanguageValue}
              />
            </View>
          )}



          {/* rituals */}
          {rituals && rituals.length > 0 && (
            <View style={styles.contentContanier}>
              <ServiceSection
                navigation={navigation}
                title={I18n.t('rituals')}
                data={rituals}
                language={selectedLanguageValue}
              />
            </View>
          )}

          {/* blogs */}
          <View style={styles.blogContanier}>
            <Text style={styles.blogsText}>{I18n.t('our_blogs')}</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate(AppRoutes.BLOGSCREEN)}>
              <Text style={styles.seeAllText}>{I18n.t('see_all')}</Text>
            </TouchableOpacity>
          </View>
          <BlogScreen isHorizontal={true} isShowTitle={false} />

        </ScrollView>
      )}

      {/* whatsapp-icon */}
      <TouchableOpacity
        onPress={handlewhatsapp}
        style={styles.whatsappIconContanier}>
        <Image source={images.whatsappIcon} style={styles.whatsappIconStyle} />
      </TouchableOpacity>

      {/* Language Dropdown - Conditional Rendering */}
      {isDropdownVisible && (
        <View style={styles.languageDropdown}>
          <TouchableOpacity style={{ flexDirection: 'row' }} onPress={() => handleLanguageSelect('en')}>
            <Image source={images.EnglishFlag} style={styles.flagStyle}></Image>
            <Text style={styles.languageDropdownItem}>English</Text>
          </TouchableOpacity>
          <TouchableOpacity style={{ flexDirection: 'row' }} onPress={() => handleLanguageSelect('hi')}>
            <Image source={images.indiaFlag} style={styles.flagStyle}></Image>
            <Text style={styles.languageDropdownItem}>Hindi</Text>
          </TouchableOpacity>
        </View>
      )}

      <LoginModal
        visible={askForLogin}
        onClose={() => setAskForLogin(false)}
        navigation={navigation}
      />

    </View>
  );
};

export default HomeScreen;
