import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    color: colors.white,
  },
  title: {
    fontSize: fontSize.f10,
    color: colors.black,
    fontFamily: fonts.MontserratBold,
  },

  //   ADD MARGINGS AND PADDINGS AS BELLOW EXAMPLE
  example: {
    ...margins.mT10, // Margin Top
    ...margins.mB10, // Margin Bottm
    ...margins.mH10, // Margin Horizontal
    ...margins.mV10, // Margin Vertical
    ...margins.mL10, // Margin Left
    ...margins.mR10, // Margin Right
    ...paddings.pV10, // Padding Vertical
    ...paddings.pH10, // Padding Horizontal
    ...paddings.pL10, // Padding Left
    ...paddings.pR10, // Padding Right
    ...paddings.pB10, // Padding Bottom
    ...paddings.pT10, // Padding Top
  },
});

export default styles;
