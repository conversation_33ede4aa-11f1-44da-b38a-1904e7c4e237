import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import { Dimensions } from 'react-native';

const {margins, paddings} = metrics;
const {height,width} = Dimensions.get('window');

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH10,
    ...margins.mB30,
  },
  seeAllText: {
    color: colors.arrowDown,
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratMedium,
    ...margins.mB10,
  },
  blogsText: {
    color: colors.dividerBlack,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
    ...margins.mT10,
  },
  blogContanier: {
    ...margins.mT10,
    ...margins.mB10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: width > 600 ? 17 : 16,
    flex: 1,

  },
  flagStyle:{
    width:DeviceUiInfo.moderateScale(22),
    height:DeviceUiInfo.moderateScale(20),
    marginVertical:5,
    marginRight :5,
    marginLeft:10
  },
  loaderContainer:{
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100, 
  },
  homeHeaderContanier: {
    ...margins.mT17,
    flexDirection: 'row',
    backgroundColor: colors.background,
  },
  useNameText: {
    color: colors.poweredByTextColor,
    fontSize: fontSize.f18,
    fontFamily: fonts.MontserratMedium,
  },
  blogStyle:{
    height:DeviceUiInfo.moderateScale(30),
    width:DeviceUiInfo.moderateScale(30)
  },
  headerIconContanier: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flex: 1,
  },
  headerIconStyle: {
    height: DeviceUiInfo.moderateScale(22),
    width: DeviceUiInfo.moderateScale(22),
  },
  headerNotificationIconStatusBar: {
    height: DeviceUiInfo.moderateScale(5),
    width: DeviceUiInfo.moderateScale(5),
    borderRadius: 100,
    backgroundColor: colors.NotificationAvailable,
    position: 'absolute',
    right: DeviceUiInfo.moderateScale(6),
    top: DeviceUiInfo.moderateScale(2),
  },
  languageContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'center',
    // borderWidth: 1,
    ...margins.mR10,
  },
  languageText: {
    fontSize: fontSize.f12,
    color: colors.primary,
    fontFamily: fonts.MontserratMedium,
    ...margins.mR2,
    ...margins.mL5,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
   languageDropdown: {
    position: 'absolute',
    top: height * 0.066,  
    right: width * 0.21, 
    backgroundColor: '#fff',
    borderRadius: 5,
    width: 100,  
    elevation: 5,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2.5,
    zIndex: 999, 
  },
  languageDropdownItem: {
    fontSize: 14,
    fontFamily: fonts.MontserratMedium,
    color: '#333',
    marginVertical:5,
    textAlign: 'center',
  },
  datePanchangContanier: {
    ...margins.mT25,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderWidth: 1,

  },
  datePanchangText: {
    color: colors.dividerBlack,
    // fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  datePanchangSubtitleText: {
    fontSize: fontSize.f14,
    color: colors.arrowDown,
    fontFamily: fonts.MontserratMedium,
  },
  muhurtContanier: {
    height: DeviceUiInfo.moderateScale(32),
    width: DeviceUiInfo.moderateScale(80),
    backgroundColor: colors.lightOrange,
    borderRadius: DeviceUiInfo.moderateScale(68),
    elevation: 10,
    shadowColor: colors.lightOrange,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    alignItems: 'center',
    justifyContent: 'center',
    ...margins.mB5,

  },
  muhurtText: {
    fontSize: fontSize.f10,
    fontFamily: fonts.MontserratBold,
    color: colors.white,
  },
  getYourKundaliContanier: {
    ...margins.mT8,
    flex:1,
    height: height * 0.22
  },
  getYourKundaliBGImageStyle: {
    // height: DeviceUiInfo.moderateScale(140),
    // width: DeviceUiInfo.moderateScale(327),
    // borderWidth: 3,
    ...margins.mT4,
    position: 'absolute',
    alignSelf: 'center',
    // height: DeviceUiInfo.moderateScale(155),
    // height: height > 800 ? DeviceUiInfo.moderateScale(200) : DeviceUiInfo.moderateScale(155),
    // height : height * 0.22,
    // width: width * 0.95,
    // width: width > 400 ? DeviceUiInfo.moderateScale(480) : DeviceUiInfo.moderateScale(360),
    height: width > 700 ? height * 0.24 : height * 0.21,
    width: width * 0.945,
    borderRadius : width > 400 ? 30 : 0,
    resizeMode: width > 400 ? 'cover' : 'contain', 
  },
  kundaliContanier: {
    flex:1,
    ...margins.mL17,
    flexDirection: 'row', // added on 8-3-25 for not getting responsive create kundali button
    flexWrap: 'wrap', // added on 8-3-25 for not getting responsive create kundali button
    // borderWidth: 1,
    // borderColor: 'red',
    ...margins.mT20,
    height: DeviceUiInfo.moderateScale(100),
    width: width > 700 ? DeviceUiInfo.moderateScale(192) : DeviceUiInfo.moderateScale(162),
  },
  kundaliText: {
    color: colors.background,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratBold,
    overflow: 'hidden', // added on 8-3-25 for not getting responsive create kundali button
    textOverflow: 'ellipsis', // added on 8-3-25 for not getting responsive create kundali button
  },
  kundaliSubtitleText: {
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f12,
    color: colors.background,
    opacity: 0.7,
    ...margins.mT8,
  },
  createKundaliBtn: {
    backgroundColor: colors.lightOrange,
    height: DeviceUiInfo.moderateScale(26),
    width: DeviceUiInfo.moderateScale(98),
    borderRadius: DeviceUiInfo.moderateScale(68.38),
    marginTop: width > 700 ? 60 : 14,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex:1
  },
  createKundaliText: {
    fontSize: fontSize.f10,
    color: colors.white,
    fontFamily: fonts.MontserratSemibold,
  },
  categoryContanier: {
    // ...margins.mR5,
    // ...margins.mT50,
    marginTop: width > 700 ? height * 0.04 : height * 0.02,  
    // marginRight:
    // width > 700
    //   ? DeviceUiInfo.moderateScale(18)
    //   : width < 375
    //   ? DeviceUiInfo.moderateScale(8)
    //   : DeviceUiInfo.moderateScale(10),
    // height: DeviceUiInfo.moderateScale(78),
    // width: DeviceUiInfo.moderateScale(70),
    height: height * 0.118,
    width: width * 0.26, //0.21 prev
    // borderWidth: 1,
    alignItems: 'center',
    // justifyContent: 'space-between',
    // marginHorizontal: width > 700 ? DeviceUiInfo.moderateScale(14) : width < 375 ? DeviceUiInfo.moderateScale(1.5) : DeviceUiInfo.moderateScale(14)

  },

  categoryIconContanier: {
    backgroundColor: colors.white,
    elevation: 5,
    // height: DeviceUiInfo.moderateScale(49),
    height: 60, 
    width: 60,
    // width: DeviceUiInfo.moderateScale(49),
    borderRadius: 22.5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryIcon: {
    height: DeviceUiInfo.moderateScale(38),
    width: DeviceUiInfo.moderateScale(38),
  },
  categoryName: {
    ...margins.mT8,
    fontSize: width > 700 ? fontSize.f10 : fontSize.f12,
  },
  contentContanier: {
    // borderWidth: 1,
    ...margins.mT10,
    // ...margins.mL17,
    marginLeft: width > 600 ? 17 : 16,
    flex: 1,
    // justifyContent: 'center', // Center items vertically
    // alignItems: 'center', 
    // marginHorizontal: 
    // width < 375 ? width * 0.02 :  // For small phones, 2% of the screen width
    // (width <= 600 ? width * 0.04 :  // For medium screens, 4% of the screen width
    // (width <= 900 ? width * 0.05 : width * 0.05)), 
  },
  becomePanditsContanier: {
    ...margins.mT24,
    flexDirection: 'row',
    alignItems: 'center',
    height: DeviceUiInfo.moderateScale(48),
    // borderRadius: DeviceUiInfo.moderateScale(100),
    // borderWidth: 1,
  },
  becomePanditsBGStyle: {
    ...margins.mT24,
    ...paddings.p14,
    marginHorizontal: width > 700 ? 80 : 0
 },
  becomePanditsText: {
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratBold,
    color: colors.dividerBlack,
  },
  becomePanditApplyBG: {
    ...paddings.pV7,
    ...paddings.pH10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: DeviceUiInfo.moderateScale(68),
  },
  applynowText: {
    fontSize: fontSize.f10,
    fontFamily: fonts.MontserratBold,
    color: colors.primary,
  },
  whatsappIconContanier: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(12),
    right: DeviceUiInfo.moderateScale(20),
  },
  whatsappIconStyle: {
    height: DeviceUiInfo.moderateScale(64),
    width: DeviceUiInfo.moderateScale(64),
  },
  btnContainer:{
    // height: DeviceUiInfo.moderateScale(32),
    paddingHorizontal:20,
    paddingVertical:5,
    backgroundColor: colors.lightOrange,
    flexDirection:'row',
    borderRadius: DeviceUiInfo.moderateScale(68),
    elevation: 10,
    shadowColor: colors.lightOrange,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    alignItems: 'center',
    justifyContent: 'center',
    ...margins.mB5,
    alignSelf:'center'
  }
});

export default styles;
