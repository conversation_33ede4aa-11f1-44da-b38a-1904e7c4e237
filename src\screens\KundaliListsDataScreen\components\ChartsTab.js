import React, { useEffect,useState } from 'react';
import {View, Text, SafeAreaView, FlatList, Image, WebView, Dimensions} from 'react-native';
import styles from '../styles/ChartsTab.styles';
import I18n from '../../../utils/language/i18nextConfig';
import {images} from '../../../theme/images';
import {SvgXml} from 'react-native-svg';


const ChartsTab = ({navigation, apidata}) => {
  const [chartsData, setChartsData] = useState([]);
  
  useEffect(() => {
    if(apidata && apidata.charts){
      const formattedChartsData = [
        {
          id: 1,
          titleName: I18n.t('lagna_chart'),
          chartSvg: apidata.charts.lagna_chart?.svg || '',
          error: apidata.charts.lagna_chart?.status === false ? apidata.charts.lagna_chart?.msg : null
        },
        {
          id: 2,
          titleName: I18n.t('navamansha_chart'),
          chartSvg: apidata.charts.navamansha_chart?.svg || '',
          error: apidata.charts.navamansha_chart?.status === false ? apidata.charts.navamansha_chart?.msg : null,
        },
      ]
      setChartsData(formattedChartsData);
    }
  },[apidata]);


  const chartDataRender = ({item, index}) => {
    console.log("item",item);
    return (
      <View style={styles.chartContanier}>
        <Text style={styles.titleText}>{item.titleName}</Text>
        {/* <Image
          resizeMode="contain"
          style={styles.chartImgStyle}
          source={item.chartImg}
        /> */}
          {item.error ? (
          <Text style={styles.errorText}>{item.error}</Text>
        ) : (
          <View style={{}}>
          <SvgXml xml={item.chartSvg} />
          </View>
          // <WebView
          //   originWhitelist={['*']}
          //   source={{ html: item.chartSvg }} // Display SVG chart using WebView
          //   style={styles.chartImgStyle}
          // />
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <FlatList
        data={chartsData}
        renderItem={chartDataRender}
        keyExtractor={(item, index) => item.id.toString()}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default ChartsTab;











//APi integration code but need to confirm

// import React, { useState, useEffect } from 'react';
// import { View, Text, SafeAreaView, FlatList, Image } from 'react-native';
// import styles from '../styles/ChartsTab.styles';
// import I18n from '../../../utils/language/i18nextConfig';
// import { images } from '../../../theme/images';

// const ChartsTab = ({ navigation, apidata }) => {
//   const [chartsData, setChartsData] = useState([]);

//   useEffect(() => {
//     // Assuming `apidata.charts` has lagna_chart and navamansha_chart
//     if (apidata && apidata.charts) {
//       const formattedChartsData = [
//         {
//           id: 1,
//           titleName: I18n.t('lagna_chart'),
//           chartImg: apidata.charts.lagna_chart?.status
//             ? { uri: apidata.charts.lagna_chart?.url }
//             : images.laganChart, // Use API image or fallback to default
//           error: apidata.charts.lagna_chart?.status === false ? apidata.charts.lagna_chart?.msg : null,
//         },
//         {
//           id: 2,
//           titleName: I18n.t('navamansha_chart'),
//           chartImg: apidata.charts.navamansha_chart?.status
//             ? { uri: apidata.charts.navamansha_chart?.url }
//             : images.navamanshaChart, // Use API image or fallback to default
//           error: apidata.charts.navamansha_chart?.status === false ? apidata.charts.navamansha_chart?.msg : null,
//         },
//       ];

//       setChartsData(formattedChartsData);
//     }
//   }, [apidata]);

//   const chartDataRender = ({ item, index }) => {
//     return (
//       <View style={styles.chartContanier}>
//         <Text style={styles.titleText}>{item.titleName}</Text>
//         {item.error ? (
//           <Text style={styles.errorText}>{item.error}</Text>
//         ) : (
//           <Image
//             resizeMode="contain"
//             style={styles.chartImgStyle}
//             source={item.chartImg}
//           />
//         )}
//       </View>
//     );
//   };

//   return (
//     <SafeAreaView style={styles.contanier}>
//       <FlatList
//         data={chartsData}
//         renderItem={chartDataRender}
//         keyExtractor={(item, index) => item.id.toString()}
//       />
//     </SafeAreaView>
//   );
// };

// export default ChartsTab;
