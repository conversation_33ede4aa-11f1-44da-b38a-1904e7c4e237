import analytics from '@react-native-firebase/analytics';
import firebase from '@react-native-firebase/app';

// Helper function to wait for Firebase initialization
const waitForFirebase = (maxWaitTime = 5000) => {
    return new Promise((resolve) => {
        const checkFirebase = () => {
            if (firebase.apps.length > 0) {
                resolve(true);
            } else if (maxWaitTime <= 0) {
                resolve(false);
            } else {
                setTimeout(() => {
                    maxWaitTime -= 100;
                    checkFirebase();
                }, 100);
            }
        };
        checkFirebase();
    });
};

export const customAppEvents = async(eventName, payLoad) => {
    try {
        // Wait for Firebase to be initialized
        const isFirebaseReady = await waitForFirebase();

        if (!isFirebaseReady) {
            console.log("Firebase not initialized within timeout, skipping analytics event:", eventName);
            return;
        }

        await analytics().logEvent(eventName, payLoad || {});
        console.log("Analytics event logged:", eventName, payLoad);
    }
    catch (err) {
        console.log("Error performing custom events >>> ", err);
    }
}