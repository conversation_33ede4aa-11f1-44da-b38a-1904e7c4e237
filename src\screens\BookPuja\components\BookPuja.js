import React, { useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Image,
  Alert,
  ScrollView,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import styles from '../styles/BookPuja.styles';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import AppInput from '../../../components/common/AppInput';
import PhoneNumberVerify from '../../../components/common/PhoneNumberVerify';
import { images } from '../../../theme/images';
import DropdownTextfield from '../../../components/common/DropdownTextfield';
import metrics from '../../../theme/metrics';
import AppButton from '../../../components/common/AppButton';
import AppRoutes from '../../../constants/AppRoutes';
import { GetServices, PostServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import RadioGroup from 'react-native-radio-buttons-group';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import { Dropdown } from 'react-native-element-dropdown';
import { getKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import { customAppEvents } from '../../../analytics/AppEvents';

const BookPuja = ({ navigation }) => {
  const api = new Apis();
  const { margins, paddings } = metrics;
  const { width, height } = Dimensions.get('window')

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    whatsappNumber: '',
    alternativeNumber: '',
    location: '',
    language: '',
    puja: '',
    pujaId: '',
    // month: '',
    // monthNo: '',
    // day: '',
    // year: '',
    date: '',
    city: '',
    state: '',
    country: '',
  });

  const [pujaOption, setPujaOption] = useState([]);
  const [dropdownOpen, setDropdownOpen] = useState(null);
  const [selectedRadioId, setSelectedRadioId] = useState('1');
  const [pujaDate, setPujaDate] = useState(new Date());
  const [pujaDatePicker, setpujaDatePicker] = useState(false);
  const [selected, setSelected] = useState([]);
  const [countriesWholeData, setcountriesWholeData] = useState([]);
  const [countries, setCountries] = useState([]);
  const [stateWholeData, setStateWholeData] = useState([]);
  const [state, setState] = useState([]);
  const [cityWholeData, setCityWholeData] = useState([]);
  const [cities, setCities] = useState([]);
  const [userId, setUserId] = useState([]);
  const [cominglang, setComingLang] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [callingCode, setCallingCode] = useState('91');
  const [altCallingCode, setAltCallingCode] = useState('91');


  useEffect(() => {
    const langstore = async () => {
      const lang = await getKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE);
      setComingLang(lang);
    };
    langstore();
  }, []);

  const dateFormat = date => {
    // const selectedDate = date || new Date();
    const formatDate = moment(date).format('YYYY-MM-DD');
    setPujaDate(date);
    setFormData(prevState => ({ ...prevState, date: formatDate }));
    setpujaDatePicker(false);
  };

  const handleDropdownToggle = dropdownId => {
    setDropdownOpen(dropdownOpen === dropdownId ? null : dropdownId);
  };

  const handleInputChange = (field, value) => {

    setFormData(prevState => {
      const newForm = { ...prevState, [field]: value };

      if (field === 'puja') {
        newForm.pujaId = value?.value;
      }
      return newForm;
    });
  };

  useEffect(() => {
    fetchPujaData();
    fetchCountry();
  }, []);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const languageOption = () => ['English', 'Gujarati', 'Hindi', 'Odia', 'Rajhasthani', 'Tamil', 'Kannad', 'Marathi', 'Telugu', 'Bengali'];

  // Validation of fieldss
  const validateForm = () => {
    formData.date = formData.date || moment().format('YYYY-MM-DD');

    if (
      !formData.firstName?.trim() ||
      !formData.lastName?.trim() ||
      !formData.email?.trim() ||
      !formData.whatsappNumber ||
      !formData.alternativeNumber ||
      !formData.location?.trim() ||
      !formData.city ||
      !formData.state ||
      !formData.country ||
      !formData.puja ||
      !formData.language ||
      !formData.date
    ) {
      Alert.alert('Note', 'All fields are required.');
      return false;
    }
    if (
      !/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/.test(formData.email?.trim())
    ) {
      Alert.alert('Invalid email', 'Please enter a valid email address.');
      return false;
    }
    // if (!/^\d{10,15}$/.test(formData.whatsappNumber)) {
    //   Alert.alert('Number must be betweeen 10-15 digits.');
    //   return false;
    // }
    // if (
    //   formData.alternativeNumber &&
    //   !/^\d{10,15}$/.test(formData.alternativeNumber)
    // ) {
    //   Alert.alert('Alternative number must be between 10 and 15 digits.');
    //   return false;
    // }

    if (
      formData.whatsappNumber.length < 7 ||
      formData.whatsappNumber.length > 12
    ) {
      Alert.alert(
        'Invalid Phone Number',
        'Whatsapp number must be between 7 and 12 digits.',
      );
      return;
    }

    if (
      formData.alternativeNumber.length < 7 ||
      formData.alternativeNumber.length > 12
    ) {
      Alert.alert(
        'Invalid Phone Number',
        'Alternative Phone number must be between 7 and 12 digits.',
      );
      return;
    }

    return true;
  };

  const fetchPujaData = async () => {
    const payload = {
      url: `${api.getAllPuja}`,
    };
    await GetServices(payload)
      .then(res => {
        if (res) {
          const pujaNames = res?.data.map(item => {
            return { label: item.text, value: item.id };
          });
          setPujaOption(pujaNames);
        } else {
          Alert.alert(
            'Error',
            'Did not fetched data from api puja from homescreen',
          );
        }
      })
      .catch(error => {
        console.log(error);
      });
  };

  const fetchbookpandit = async () => {
    if (isSubmitting) return;
    if (!validateForm()) return;
    setIsSubmitting(true);

    const fullPhone = `+${callingCode}${formData.whatsappNumber}`;
    const fullAltPhone = `+${altCallingCode}${formData.alternativeNumber}`;

    var formdata = new FormData();
    formdata.append('first_name', formData.firstName);
    formdata.append('last_name', formData.lastName);
    formdata.append('email', formData.email);
    formdata.append('phone', fullPhone);
    formdata.append('alternative_number', fullAltPhone);
    formdata.append('address', formData.location);
    formdata.append('city', formData.city);
    formdata.append('state', formData.state);
    formdata.append('country', formData.country);
    formdata.append('puja', formData.pujaId);
    formdata.append('user_ID', userId);
    // formdata.append('date', formData.date);
    formdata.append('date', formData.date || moment().format('YYYY-MM-DD'));
    formdata.append('language', formData.language);

    const payload = {
      url: api.booknow,
      data: formdata,
      type: 'formData',
    };

    await PostServices(payload).then(async (res) => {
      if (res) {
        
        await customAppEvents(`booked_${formData.pujaId}`)
        navigation.replace(AppRoutes.VIEW_BOOK_PUJA_LIST, {
          BookPujaData: formData,
        });
      } else {
        Alert.alert('Note', 'Form Not Submitted');
      }
      setIsSubmitting(false);
    }).catch(err => {
      console.log("err from catch in book pandit form >>", err)
      setIsSubmitting(false);
    })
  };

  const radioButtons = useMemo(
    () => [
      {
        id: '1',
        label: 'Myself',
        value: 'Myself',
        size: 20,
        color: colors.primary,
        labelStyle: {
          color: selectedRadioId === '1' ? colors.primary : 'gray',
        },
      },
      {
        id: '2',
        label: 'Someone Else',
        value: 'Someone Else',
        size: 20,
        color: colors.primary,
        labelStyle: {
          color: selectedRadioId === '2' ? colors.primary : 'gray',
        },
      },
    ],
    [],
  );

  const fetchCountry = async () => {
    const payload = {
      url: api.country,
    };
    await GetServices(payload)
      .then(res => {
        if (res) {
          const countrydata = res?.data;
          const data = countrydata.map(country => country.name);
          setcountriesWholeData(countrydata);
          setCountries(data);
        }
      })
      .catch(err => {
        console.log('Error fetching the name of country', err);
      });
  };

  const handleSelectCountry = selectedCountryName => {
    const selectedCountry = countriesWholeData.find(
      country => country.name === selectedCountryName,
    );
    if (selectedCountry) {
      //id of country storing in state
      setFormData(prevState => ({
        ...prevState,
        country: selectedCountry.name,
      }));

      fetchState(selectedCountry.id);
    }
  };

  const fetchState = async country_id => {
    const payload = {
      url: `${api.state}?country_id=${country_id}`,
    };
    await GetServices(payload)
      .then(res => {
        if (res) {
          const stateData = res?.data;
          const stateNames = stateData.map(state => state.text);
          setStateWholeData(stateData);
          setState(stateNames);
          setFormData(prevState => ({
            ...prevState,
            state: '',
            city: '',
          }));
        }
      })
      .catch(err => {
        console.log('error fetching state', err);
      });
  };

  const handleSelectState = selectedStateName => {
    const selectedState = stateWholeData.find(
      country => country.text === selectedStateName,
    );
    if (selectedState) {
      setFormData(prevState => ({
        ...prevState,
        state: selectedState.text,
      }));

      fetchCity(selectedState.id);
    }
  };

  const fetchCity = async state_id => {
    const payload = {
      url: `${api.city}?state_id=${state_id}`,
    };
    await GetServices(payload)
      .then(res => {
        if (res) {
          const cityData = res?.data;
          const cityNames = cityData.map(city => city.name);
          setCityWholeData(cityData);
          setCities(cityNames);
          setFormData(prevState => ({
            ...prevState,
            city: '',
          }));
        }
      })
      .catch(err => {
        console.log('Error fetching city data', err);
      });
  };

  const fetchUser = async () => {
    try {
      const userstore = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
      const userdetail = JSON.parse(userstore);
      console.log('user store in book now form>>>', userdetail);

      const removeFirstTwoDigits = (phoneNumber) => {
        return phoneNumber.slice(2);
      };

      if (userdetail) {
        setFormData(prevState => ({
          ...prevState,
          firstName: userdetail.first_name || '',
          lastName: userdetail.last_name || '',
          email: userdetail.email || '',
          whatsappNumber: removeFirstTwoDigits(userdetail.phone || ''),
        }));
        setUserId(userdetail.ID);
      }
    } catch (error) {
      console.log('Error retrieving user name from AsyncStorage:', error);
    }
  };

  useEffect(() => {
    if (selectedRadioId === '1') {
      fetchUser();
    }
  }, []);

  const handleRadioChange = async selectedId => {
    setSelectedRadioId(selectedId);
    if (selectedId === '1') {
      fetchUser();
    } else {
      setFormData(prevState => ({
        ...prevState,
        firstName: '',
        lastName: '',
        email: '',
        whatsappNumber: '',
      }));
    }
  };
  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{ marginBottom: 10 }}>
        {/* app-title */}
        <View style={styles.appTitleContanier}>
          <AppTitle
            title={I18n.t('book_puja')}
            leftIconPress={handleLeftPress}
          />
        </View>

        <View style={{ marginBottom: 20 }}>
          <RadioGroup
            radioButtons={radioButtons}
            onPress={handleRadioChange}
            selectedId={selectedRadioId}
            layout="row"
          />
        </View>

        <View style={{ flexDirection: 'row' }}>
          {/* FirstName */}
          <AppInput
            label={I18n.t('first_name')}
            requirement={true}
            value={formData.firstName}
            onChangeText={text => handleInputChange('firstName', text)}
            containerStyle={{ flex: 1, marginRight: 8 }}
          />

          {/* LastName */}
          <AppInput
            label={I18n.t('last_name')}
            requirement={true}
            value={formData.lastName}
            onChangeText={text => handleInputChange('lastName', text)}
            containerStyle={{ flex: 1 }}
          />
        </View>

        {/* email */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('email')}
            requirement={true}
            value={formData.email}
            onChangeText={text => handleInputChange('email', text)}
          />
        </View>

        {/* puja */}
        <View style={styles.textfieldContent}>
          <View
            style={[
              styles.labelContanier,
              { left: cominglang === 'hi' ? width * 0.30 : width * 0.29 },
            ]}>
            {<Text style={styles.requirementText}>*</Text>}
          </View>
          <Text style={styles.labelStyle}>{I18n.t('puja_havan_rituals')}</Text>
          <Dropdown
            style={styles.dropdown}
            search
            data={pujaOption}
            labelField="label"
            valueField="value"
            placeholder={I18n.t('puja_havan_rituals')}
            searchPlaceholder="Search"
            value={selected}
            onChange={item => {
              setSelected(item);
              handleInputChange('puja', item);
            }}
          />
          {/* <DropdownTextfield
            label={I18n.t('puja')}
            options={pujaOption.map(item => item.title)}
            value={formData.puja}
            // onSelect={value => handleInputChange('puja', value)}
            onSelect={value => {
              const selectedPuja = pujaOption.find(
                item => item.title === value,
              );
              if (selectedPuja) {
                handleInputChange('puja', value);
                handleInputChange('pujaId', selectedPuja.id);
              }
            }}
            dropdownContanierStyle={styles.countrydropdown}
            isDropdownOpen={dropdownOpen === 2}
            onDropdownToggle={() => handleDropdownToggle(2)}
          /> */}
        </View>

        {/* Date field */}
        <TouchableOpacity
          style={styles.textfieldContent}
          onPress={() => setpujaDatePicker(true)}>
          <AppInput
            label={I18n.t('puja_date')}
            value={pujaDate ? moment(pujaDate).format('Do MMMM, YYYY') : ''}
            isEditable={false}
            requirement={true}
          />
          <Image
            style={styles.calIconImageStyle}
            resizeMode="contain"
            source={images.calendarBlue}
          />
        </TouchableOpacity>

        {pujaDatePicker && (
          <DatePicker
            modal
            mode="date"
            open={pujaDatePicker}
            date={pujaDate}
            minimumDate={new Date()}
            onConfirm={dateFormat}
            onCancel={() => setpujaDatePicker(false)}
          />
        )}

        {/* language */}
        <View style={styles.textfieldContent}>
          <View
            style={[
              styles.labelContanier,
              { left: cominglang === 'hi' ? width * 0.06 : width * 0.17 },
            ]}>
            {<Text style={styles.requirementText}>*</Text>}
          </View>
          <DropdownTextfield
            label={I18n.t('language')}
            options={languageOption()}
            value={formData.language}
            onSelect={value => handleInputChange('language', value)}
            dropdownContanierStyle={styles.countrydropdown}
            isDropdownOpen={dropdownOpen === 1}
            onDropdownToggle={() => handleDropdownToggle(1)}
            placeholderText={I18n.t('select_your_language')}
          />
        </View>

        {/* whatsapp-number */}
        <View style={styles.textfieldContent}>
          <PhoneNumberVerify
            label={I18n.t('primary_number')}
            requirement={true}
            keyboardType="number-pad"
            value={formData.whatsappNumber}
            onChangeText={text => handleInputChange('whatsappNumber', text)}
            onCountryCodeChange={(code) => {
              setCallingCode(code)
            }}
            isEditable={selectedRadioId !== '1'}
          />
        </View>

        {/* alternative-number */}
        <View style={styles.textfieldContent}>
          <PhoneNumberVerify
            label={I18n.t('alternative_number')}
            keyboardType="number-pad"
            requirement={true}
            value={formData.alternativeNumber}
            onChangeText={text => handleInputChange('alternativeNumber', text)}
            onCountryCodeChange={(altcode) => {
              setAltCallingCode(altcode)
            }}
          />
        </View>

        {/* location */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('address')}
            leftIcon={<Image source={images.location} />}
            value={formData.location}
            onChangeText={text => handleInputChange('location', text)}
            requirement={true}
          />
        </View>

        <View style={{ ...margins.mT16 }}>
          {/* Country */}
          <View style={{ marginBottom: 16 }}>
            <View
              style={[
                styles.labelContanier,
                { left: cominglang === 'hi' ? width * 0.05 : width * 0.13 },
              ]}>
              {<Text style={styles.requirementText}>*</Text>}
            </View>
            <DropdownTextfield
              label={I18n.t('country')}
              options={countries}
              // value={
              //   countriesWholeData.find(
              //     country => country.id === formData.country,
              //   )?.name || ''
              // }
              value={formData.country}
              onSelect={value => handleSelectCountry(value)}
              dropdownContanierStyle={styles.countrydropdown}
              isDropdownOpen={dropdownOpen === 3}
              onDropdownToggle={() => handleDropdownToggle(3)}
              placeholderText={I18n.t('select_country')}
            />
          </View>

          {/* State */}
          <View style={{ marginBottom: 16 }}>
            <View
              style={[
                styles.labelContanier,
                { left: cominglang === 'hi' ? width * 0.06 : width * 0.085 },
              ]}>
              {<Text style={styles.requirementText}>*</Text>}
            </View>
            <DropdownTextfield
              label={I18n.t('state')}
              options={state}
              // value={
              //   stateWholeData.find(state => state.id === formData.state)
              //     ?.text || ''
              // }
              value={formData.state}
              onSelect={value => handleSelectState(value)}
              dropdownContanierStyle={styles.countrydropdown}
              isDropdownOpen={dropdownOpen === 4}
              onDropdownToggle={() => handleDropdownToggle(4)}
              placeholderText={I18n.t('select_state')}
            />
          </View>

          {/* City */}
          <View>
            <View
              style={[
                styles.labelContanier,
                { left: cominglang === 'hi' ? width * 0.06 : width * 0.06 },
              ]}>
              {<Text style={styles.requirementText}>*</Text>}
            </View>
            <DropdownTextfield
              label={I18n.t('city')}
              options={cities}
              value={formData.city}
              onSelect={value => handleInputChange('city', value)}
              dropdownContanierStyle={[styles.countrydropdown]}
              isDropdownOpen={dropdownOpen === 5}
              onDropdownToggle={() => handleDropdownToggle(5)}
              placeholderText={I18n.t('select_city')}
            />
          </View>
        </View>
      </ScrollView>

      {/* app-button */}
      <View style={styles.appbtnContanier}>
        <AppButton
          text={I18n.t('submit')}
          onPressed={fetchbookpandit}
          disabled={isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </SafeAreaView>
  );
};

export default BookPuja;
