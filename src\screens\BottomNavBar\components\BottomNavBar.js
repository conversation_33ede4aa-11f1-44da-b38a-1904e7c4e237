import React, { useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  Animated,
} from 'react-native';
import styles from '../styles/BottomNavBar.style';
import AppRoutes from '../../../constants/AppRoutes';
import { images } from '../../../theme/images';
import { colors } from '../../../theme/colors';
import { fonts, fontSize } from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import I18n from '../../../utils/language/i18nextConfig';
import HomeScreen from '../../HomeScreen/components/HomeScreen';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AccountScreen from '../../AccountScreen/components/AccountScreen';
import ServiceListScreen from '../../ServicesScreen/components/ServiceListScreen';
import UpdateProfileScreen from '../../UpdateProfileScreen/components/UpdateProfileScreen';
import { CurvedBottomBar } from 'react-native-curved-bottom-bar';
import { getKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import LoginModal from '../../../components/common/LoginModal';

const Stack = createNativeStackNavigator();

const BottomNavBar = ({ navigation }) => {
  const [selectedTab, setSelectedTab] = useState('home');
  const [languageChanged, setLanguageChanged] = useState(false);
  const [askForLogin, setAskForLogin] = useState(false);
  const [isLoggedin, setIsLoggedin] = useState(false);

  const checkUserLogin = async () => {
    setAskForLogin(false)
    const user = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);

    if (!user) {
      setAskForLogin(true);
      setIsLoggedin(false);
      return;
    }
    else {
      setIsLoggedin(true);
    }
    navigation.navigate(AppRoutes.ACCOUNT_SCREEN);
  }

  useEffect(() => {
      const username = async () => {
        const name = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
        const firstname = JSON.parse(name).first_name;
        console.log("firstname>>", firstname);
        if (firstname.length <= 0 || firstname === null || firstname === undefined || firstname === '') {
          navigation.replace(AppRoutes.PROFILE_SCREEN, {
            message: I18n.t('create_profile_message'),
          });
        }
      };
      username();
  
    }, []);
  


  useEffect(() => {
    const languageChangeListener = () => {
      setLanguageChanged(prev => !prev);
    };

    I18n.on('languageChanged', languageChangeListener);

    return () => {
      I18n.off('languageChanged', languageChangeListener);
    };
  }, []);

  const renderIcon = routeName => {
    let iconImg = '';

    switch (routeName) {
      case 'home':
        iconImg = (
          <Image
            source={
              routeName === selectedTab
                ? images.homeSelectedIcon
                : images.homeUnselectedIcon
            }
            style={styles.iconStyle}
          />
        );
        break;
      case 'profile':
        iconImg = (
          <Image
            source={
              routeName === selectedTab
                ? images.profileselectedIcon
                : images.profileUnselectedIcon
            }
            style={styles.iconStyle}
          />
        );
        break;
    }

    return <View>{iconImg}</View>;
  };

  const renderTabBar = ({ routeName, selectedTab, navigate }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          setSelectedTab(routeName);
          if (routeName === 'profile') {
            checkUserLogin();
          } else {
            navigate(routeName);
          }
        }}
        style={styles.tabStyle}>
        {renderIcon(routeName, selectedTab)}
        <Text
          style={{
            color: routeName === selectedTab ? colors.primary : colors.black,
            fontFamily:
              routeName === selectedTab
                ? fonts.MontserratSemibold
                : fonts.MontserratMedium,
            ...styles.fontStyle,
          }}>
          {routeName === 'home' ? I18n.t('home') : I18n.t('profile')}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <CurvedBottomBar.Navigator
        style={styles.bottomBar}
        strokeWidth={5}
        height={DeviceUiInfo.moderateScale(85)}
        circleWidth={DeviceUiInfo.moderateScale(100)}
        bgColor={colors.white}
        initialRouteName="home"
        screenOptions={{ headerShown: false }}
        renderCircle={() => (
          <Animated.View>
            <TouchableOpacity style={styles.bookPanditsContanier} onPress={() => {isLoggedin ?
              navigation.navigate(AppRoutes.BOOK_PUJA) : navigation.navigate(AppRoutes.OTP_SCREEN);
            }}>
              <Image source={images.bookPandits} style={styles.bookPanditsImg} />
              <Text style={styles.bookPanditsText} numberOfLines={1}>
                {I18n.t('book_pandit')}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        )}
        tabBar={renderTabBar}>
        <CurvedBottomBar.Screen
          name="home"
          position="LEFT"
          component={HomeScreenStack}
        />
        <CurvedBottomBar.Screen
          name="profile"
          position="RIGHT"
          component={AccountScreenStack}
        />
      </CurvedBottomBar.Navigator>

      <LoginModal 
        visible={askForLogin}
        onClose={()=> setAskForLogin(false)}
        navigation={navigation}
      />
    </>
  );
};

export default BottomNavBar;

const HomeScreenStack = () => {
  return (
    <Stack.Navigator
      initialRouteName={AppRoutes.HOME_SCREEN}
      screenOptions={{ headerShown: false }}>
      <Stack.Screen name={AppRoutes.HOME_SCREEN} component={HomeScreen} />
      <Stack.Screen
        name={AppRoutes.SERVICE_LIST_SCREEN}
        component={ServiceListScreen}
      />
    </Stack.Navigator>
  );
};

const AccountScreenStack = () => {
  return (
    <Stack.Navigator
      initialRouteName={AppRoutes.ACCOUNT_SCREEN}
      screenOptions={{ headerShown: false }}>
      <Stack.Screen name={AppRoutes.ACCOUNT_SCREEN} component={AccountScreen} />
      <Stack.Screen
        name={AppRoutes.UPDATE_PROFILE_SCREEN}
        component={UpdateProfileScreen}
      />
    </Stack.Navigator>
  );
};
