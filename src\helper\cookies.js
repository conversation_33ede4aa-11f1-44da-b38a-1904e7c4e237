import AsyncStorage from '@react-native-async-storage/async-storage';

export const saveKey = async (key, value) => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (error) {
    console.log('saveKey error: ', error);
  }
};

export const getKey = async key => {
  try {
    return await AsyncStorage.getItem(key);
  } catch (error) {
    console.log('getKey error: ', error);
  }
};

export const resetKey = async key => {
  try {
    return await AsyncStorage.removeItem(key);
  } catch (error) {
    console.log('resetKey error: ', error);
  }
};

export const clearKey = async () => {
  try {
    return await AsyncStorage.clear();
  } catch (error) {
    console.log('clearKey error: ', error);
  }
};
