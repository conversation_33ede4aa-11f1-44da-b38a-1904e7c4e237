import {Dimensions, StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;
const {height,width} = Dimensions.get('window')

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  headerContanier: {
    ...margins.mT50,
  },
  otpContanier: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 30,
    alignItems: 'center',
    // width: '80%',
    elevation: 5,
    // height:height * 0.4
  },
  otpTextinput: {
    backgroundColor: colors.white,
    width: DeviceUiInfo.moderateScale(65),
    height: DeviceUiInfo.moderateScale(52),
    borderRadius: 100,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f20,
    color: colors.dividerBlack,
    textAlign: 'center',
    ...paddings.p0,
    shadowColor: colors.lightGrey,
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
  },
  resendContanier: {
    flexDirection: 'row',
    ...margins.mT16,
  },
  resendText: {
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f12,
  },
  appButton: {
    ...margins.mT28,
  },
  sendotpcontainer:{
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'space-between'
  },
  passwordContainer: {
    position: 'relative',
    width: '100%', 
  },
  eyeIconContainer: {
    position: 'absolute',
    right: 10,
    top: '50%', 
    transform: [{ translateY: -12 }],
    padding: 5,
  },
  OTPModalOverlay: {
    position:'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    top:'25%',
    ...paddings.p30
  },
  overlay: {
    backgroundColor: 'rgba(71, 71, 71, 0.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default styles;
