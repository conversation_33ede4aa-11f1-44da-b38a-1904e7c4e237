import React from 'react';
import {View, Text, SafeAreaView, StatusBar, Image} from 'react-native';
import styles from '../styles/CongratulationScreen.style';
import {colors} from '../../../theme/colors';
import {images} from '../../../theme/images';
import AppButton from '../../../components/common/AppButton';
import I18n from '../../../utils/language/i18nextConfig';
import AppRoutes from '../../../constants/AppRoutes';

const CongratulationScreen = ({navigation}) => {
  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
      <Image
        source={images.congratulationThumb}
        style={styles.congratulationThumImg}
      />
      <Text style={styles.congratulationHeaderText}>Congratulation!</Text>
      <Text style={styles.congratulationSubText}>
        Your profile has been created successfully. Press continue to start
        using app.!
      </Text>
      <View style={styles.appBtn}>
        <AppButton
          text={I18n.t('continue')}
          // onPressed={() => navigation.replace(AppRoutes.)}
          onPressed={() => navigation.reset({
                      index: 0,
                      routes: [{name: AppRoutes.BOTTOM_NAV_BAR}],
                    })
                  }
        />
      </View>
    </SafeAreaView>
  );
};

export default CongratulationScreen;
