import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    // flexDirection: 'column',
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  congratulationThumImg: {
    height: DeviceUiInfo.moderateScale(264),
    width: DeviceUiInfo.moderateScale(300),
    alignSelf: 'center',
    ...margins.mT100,
    ...margins.mB32,
  },
  congratulationHeaderText: {
    textAlign: 'center',
    fontSize: fontSize.f32,
    color: colors.primary,
    fontFamily: fonts.MontserratBold,
  },
  congratulationSubText: {
    ...margins.mT10,
    textAlign: 'center',
    fontSize: fontSize.f16,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratRegular,
  },
  appBtn: {
    justifyContent: 'flex-end',
    flex: 1,
    ...margins.mB34,
  },
});

export default styles;
