import DeviceUiInfo from '../utils/DeviceUiInfo';

const metrics = {
  paddings: {
    // Padding
    p0: {padding: DeviceUiInfo.moderateScale(0)},
    p3: {padding: DeviceUiInfo.moderateScale(3)},
    p5: {padding: DeviceUiInfo.moderateScale(5)},
    p8: {padding: DeviceUiInfo.moderateScale(8)},
    p10: {padding: DeviceUiInfo.moderateScale(10)},
    p12: {padding: DeviceUiInfo.moderateScale(12)},
    p14: {padding: DeviceUiInfo.moderateScale(14)},
    p16: {padding: DeviceUiInfo.moderateScale(16)},
    p17: {padding: DeviceUiInfo.moderateScale(17)},
    p18: {padding: DeviceUiInfo.moderateScale(18)},
    p30: {padding: DeviceUiInfo.moderateScale(30)},


    // Padding Top
    pT10: {paddingTop: DeviceUiInfo.moderateScale(10)},

    // Padding Bottom
    pB10: {paddingBottom: DeviceUiInfo.moderateScale(10)},
    pB20: {paddingBottom: DeviceUiInfo.moderateScale(20)},

    // Padding Left
    pL10: {paddingLeft: DeviceUiInfo.moderateScale(10)},
    pL14: {paddingLeft: DeviceUiInfo.moderateScale(14)},
    pL15: {paddingLeft: DeviceUiInfo.moderateScale(15)},
    pL24: {paddingLeft: DeviceUiInfo.moderateScale(24)},
    pL44: {paddingLeft: DeviceUiInfo.moderateScale(44)},

    // Padding Right
    pR10: {paddingRight: DeviceUiInfo.moderateScale(10)},
    pR20: {paddingRight: DeviceUiInfo.moderateScale(20)},
    pR30: {paddingRight: DeviceUiInfo.moderateScale(30)},
    pR40: {paddingRight: DeviceUiInfo.moderateScale(40)},
    pR50: {paddingRight: DeviceUiInfo.moderateScale(50)},
    pR60: {paddingRight: DeviceUiInfo.moderateScale(60)},
    pR65: {paddingRight: DeviceUiInfo.moderateScale(65)},

    // Padding Horizontal
    pH3: {paddingHorizontal: DeviceUiInfo.moderateScale(3)},
    pH4: {paddingHorizontal: DeviceUiInfo.moderateScale(4)},
    pH5: {paddingHorizontal: DeviceUiInfo.moderateScale(5)},
    pH6: {paddingHorizontal: DeviceUiInfo.moderateScale(6)},
    pH7: {paddingHorizontal: DeviceUiInfo.moderateScale(7)},
    pH8: {paddingHorizontal: DeviceUiInfo.moderateScale(8)},
    pH9: {paddingHorizontal: DeviceUiInfo.moderateScale(9)},
    pH10: {paddingHorizontal: DeviceUiInfo.moderateScale(10)},
    pH11: {paddingHorizontal: DeviceUiInfo.moderateScale(11)},
    pH12: {paddingHorizontal: DeviceUiInfo.moderateScale(12)},
    pH14: {paddingHorizontal: DeviceUiInfo.moderateScale(14)},
    pH15: {paddingHorizontal: DeviceUiInfo.moderateScale(15)},
    pH16: {paddingHorizontal: DeviceUiInfo.moderateScale(16)},
    pH17: {paddingHorizontal: DeviceUiInfo.moderateScale(17)},
    pH18: {paddingHorizontal: DeviceUiInfo.moderateScale(18)},
    pH19: {paddingHorizontal: DeviceUiInfo.moderateScale(19)},
    pH195: {paddingHorizontal: DeviceUiInfo.moderateScale(19.7)},
    pH20: {paddingHorizontal: DeviceUiInfo.moderateScale(20)},
    pH22: {paddingHorizontal: DeviceUiInfo.moderateScale(22)},
    pH23: {paddingHorizontal: DeviceUiInfo.moderateScale(23)},
    pH24: {paddingHorizontal: DeviceUiInfo.moderateScale(24)},
    pH28: {paddingHorizontal: DeviceUiInfo.moderateScale(28)},
    pH33: {paddingHorizontal: DeviceUiInfo.moderateScale(33)},

    // Padding Vertical
    pV2: {paddingVertical: DeviceUiInfo.moderateScale(2)},
    pV6: {paddingVertical: DeviceUiInfo.moderateScale(6)},
    pV7: {paddingVertical: DeviceUiInfo.moderateScale(7)},
    pV10: {paddingVertical: DeviceUiInfo.moderateScale(10)},
    pV12: {paddingVertical: DeviceUiInfo.moderateScale(12)},
    pV13: {paddingVertical: DeviceUiInfo.moderateScale(13)},
    pV16: {paddingVertical: DeviceUiInfo.moderateScale(16)},
    pV17: {paddingVertical: DeviceUiInfo.moderateScale(17)},
    pV21: {paddingVertical: DeviceUiInfo.moderateScale(21)},
    pV24: {paddingVertical: DeviceUiInfo.moderateScale(24)},
  },

  margins: {
    //Margin
    m2: {margin: DeviceUiInfo.moderateScale(12)},
    m4: {margin: DeviceUiInfo.moderateScale(4)},
    m5: {margin: DeviceUiInfo.moderateScale(5)},
    m7: {margin: DeviceUiInfo.moderateScale(7)},
    m8: {margin: DeviceUiInfo.moderateScale(8)},
    m12: {margin: DeviceUiInfo.moderateScale(12)},
    m15: {margin: DeviceUiInfo.moderateScale(15)},

    // Margin Top
    mT2: {marginTop: DeviceUiInfo.moderateScale(2)},
    mT4: {marginTop: DeviceUiInfo.moderateScale(4)},
    mT5: {marginTop: DeviceUiInfo.moderateScale(5)},
    mT6: {marginTop: DeviceUiInfo.moderateScale(6)},
    mT8: {marginTop: DeviceUiInfo.moderateScale(8)},
    mT10: {marginTop: DeviceUiInfo.moderateScale(10)},
    mT12: {marginTop: DeviceUiInfo.moderateScale(12)},
    mT14: {marginTop: DeviceUiInfo.moderateScale(14)},
    mT16: {marginTop: DeviceUiInfo.moderateScale(16)},
    mT17: {marginTop: DeviceUiInfo.moderateScale(17)},
    mT18: {marginTop: DeviceUiInfo.moderateScale(18)},
    mT19: {marginTop: DeviceUiInfo.moderateScale(19)},
    mT20: {marginTop: DeviceUiInfo.moderateScale(20)},
    mT22: {marginTop: DeviceUiInfo.moderateScale(22)},
    mT24: {marginTop: DeviceUiInfo.moderateScale(24)},
    mT25: {marginTop: DeviceUiInfo.moderateScale(25)},
    mT28: {marginTop: DeviceUiInfo.moderateScale(28)},
    mT30: {marginTop: DeviceUiInfo.moderateScale(30)},
    mT34: {marginTop: DeviceUiInfo.moderateScale(34)},
    mT36: {marginTop: DeviceUiInfo.moderateScale(36)},
    mT38: {marginTop: DeviceUiInfo.moderateScale(38)},
    mT42: {marginTop: DeviceUiInfo.moderateScale(42)},
    mT44: {marginTop: DeviceUiInfo.moderateScale(44)},
    mT50: {marginTop: DeviceUiInfo.moderateScale(50)},
    mT100: {marginTop: DeviceUiInfo.moderateScale(100)},
    mT110: {marginTop: DeviceUiInfo.moderateScale(110)},
    mT120: {marginTop: DeviceUiInfo.moderateScale(120)},
    mT140: {marginTop: DeviceUiInfo.moderateScale(140)},
    mT160: {marginTop: DeviceUiInfo.moderateScale(160)},
    mT173: {marginTop: DeviceUiInfo.moderateScale(173)},
    mT184: {marginTop: DeviceUiInfo.moderateScale(184)},
    mT200: {marginTop: DeviceUiInfo.moderateScale(200)},

    // Margin Bottom
    mB2: {marginBottom: DeviceUiInfo.moderateScale(2)},
    mB5: {marginBottom: DeviceUiInfo.moderateScale(5)},
    mB8: {marginBottom: DeviceUiInfo.moderateScale(8)},
    mB10: {marginBottom: DeviceUiInfo.moderateScale(10)},
    mB13: {marginBottom: DeviceUiInfo.moderateScale(13)},
    mB14: {marginBottom: DeviceUiInfo.moderateScale(14)},
    mB16: {marginBottom: DeviceUiInfo.moderateScale(16)},
    mB18: {marginBottom: DeviceUiInfo.moderateScale(18)},
    mB20: {marginBottom: DeviceUiInfo.moderateScale(20)},
    mB22: {marginBottom: DeviceUiInfo.moderateScale(22)},
    mB24: {marginBottom: DeviceUiInfo.moderateScale(24)},
    mB26: {marginBottom: DeviceUiInfo.moderateScale(26)},
    mB29: {marginBottom: DeviceUiInfo.moderateScale(29)},
    mB32: {marginBottom: DeviceUiInfo.moderateScale(32)},
    mB34: {marginBottom: DeviceUiInfo.moderateScale(34)},
    mB50: {marginBottom: DeviceUiInfo.moderateScale(50)},
    mB70: {marginBottom: DeviceUiInfo.moderateScale(70)},
    mB4:  {marginBottom: DeviceUiInfo.moderateScale(4)},
    mB3:  {marginBottom: DeviceUiInfo.moderateScale(3)},


    // Margin Left
    mL2: {marginLeft: DeviceUiInfo.moderateScale(2)},
    mL3: {marginLeft: DeviceUiInfo.moderateScale(3)},
    mL4: {marginLeft: DeviceUiInfo.moderateScale(4)},
    mL5: {marginLeft: DeviceUiInfo.moderateScale(5)},
    mL6: {marginLeft: DeviceUiInfo.moderateScale(6)},
    mL10: {marginLeft: DeviceUiInfo.moderateScale(10)},
    mL12: {marginLeft: DeviceUiInfo.moderateScale(12)},
    mL13: {marginLeft: DeviceUiInfo.moderateScale(13)},
    mL14: {marginLeft: DeviceUiInfo.moderateScale(14)},
    mL15: {marginLeft: DeviceUiInfo.moderateScale(15)},
    mL17: {marginLeft: DeviceUiInfo.moderateScale(17)},
    mL18: {marginLeft: DeviceUiInfo.moderateScale(18)},
    mL20: {marginLeft: DeviceUiInfo.moderateScale(20)},
    mL20: {marginLeft: DeviceUiInfo.moderateScale(20)},
    mL24: {marginLeft: DeviceUiInfo.moderateScale(24)},
    mL26: {marginLeft: DeviceUiInfo.moderateScale(26)},
    mL28: {marginLeft: DeviceUiInfo.moderateScale(28)},
    mL30: {marginLeft: DeviceUiInfo.moderateScale(30)},


    // Margin Right
    mR2: {marginRight: DeviceUiInfo.moderateScale(2)},
    mR5: {marginRight: DeviceUiInfo.moderateScale(5)},
    mR10: {marginRight: DeviceUiInfo.moderateScale(10)},
    mR14: {marginRight: DeviceUiInfo.moderateScale(14)},
    mR15: {marginRight: DeviceUiInfo.moderateScale(15)},
    mR20: {marginRight: DeviceUiInfo.moderateScale(20)},
    mR27: {marginRight: DeviceUiInfo.moderateScale(27)},

    // Margin Horizontal
    mH5: {marginHorizontal: DeviceUiInfo.moderateScale(5)},
    mH10: {marginHorizontal: DeviceUiInfo.moderateScale(10)},
    mH12: {marginHorizontal: DeviceUiInfo.moderateScale(12)},
    mH14: {marginHorizontal: DeviceUiInfo.moderateScale(14)},
    mH15: {marginHorizontal: DeviceUiInfo.moderateScale(15)},
    mH16: {marginHorizontal: DeviceUiInfo.moderateScale(16)},
    mH17: {marginHorizontal: DeviceUiInfo.moderateScale(17)},
    mH18: {marginHorizontal: DeviceUiInfo.moderateScale(18)},
    mH19: {marginHorizontal: DeviceUiInfo.moderateScale(19)},
    mH20: {marginHorizontal: DeviceUiInfo.moderateScale(20)},
    mH21: {marginHorizontal: DeviceUiInfo.moderateScale(21)},
    mH22: {marginHorizontal: DeviceUiInfo.moderateScale(22)},
    mH23: {marginHorizontal: DeviceUiInfo.moderateScale(23)},
    mH24: {marginHorizontal: DeviceUiInfo.moderateScale(24)},
    mH25: {marginHorizontal: DeviceUiInfo.moderateScale(25)},
    mH50: {marginHorizontal: DeviceUiInfo.moderateScale(50)},
    mH70: {marginHorizontal: DeviceUiInfo.moderateScale(70)},
    mH80: {marginHorizontal: DeviceUiInfo.moderateScale(80)},




    // Margin Vertical
    mV4: {marginVertical: DeviceUiInfo.moderateScale(4)},
    mV5: {marginVertical: DeviceUiInfo.moderateScale(5)},
    mV6: {marginVertical: DeviceUiInfo.moderateScale(6)},
    mV8: {marginVertical: DeviceUiInfo.moderateScale(8)},
    mV10: {marginVertical: DeviceUiInfo.moderateScale(10)},
    mV12: {marginVertical: DeviceUiInfo.moderateScale(12)},
    mV13: {marginVertical: DeviceUiInfo.moderateScale(13)},
    mV14: {marginVertical: DeviceUiInfo.moderateScale(14)},
    mV15: {marginVertical: DeviceUiInfo.moderateScale(15)},
    mV16: {marginVertical: DeviceUiInfo.moderateScale(16)},
    mV18: {marginVertical: DeviceUiInfo.moderateScale(18)},
    mV20: {marginVertical: DeviceUiInfo.moderateScale(20)},
    mV65: {marginVertical: DeviceUiInfo.moderateScale(65)},
  },
};

export default metrics;
