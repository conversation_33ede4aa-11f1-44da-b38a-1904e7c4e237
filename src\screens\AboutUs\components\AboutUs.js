import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StatusBar,
  Image,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import styles from '../styles/AboutUs.style';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import {images} from '../../../theme/images';
import Apis from '../../../services/apiList';
import {GetServices} from '../../../services/commonApiMethod';

const AboutUs = ({navigation}) => {
  var api = new Apis();
  const [aboutusData, setAboutusData] = useState([]);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  useEffect(() => {
    const aboutus = async () => {
      const payload = {
        url: `${api.terms}about-us`,
      };
      await GetServices(payload).then(res => {
        if (res) {
          setAboutusData(res?.data);
        }
      });
    };

    aboutus();
  }, []);

  const socialData = [
    {
      id: 1,
      icon: images.facebookIcon,
      name: I18n.t('like_facebook'),
    },
    {
      id: 2,
      icon: images.twitterIcon,
      name: I18n.t('follow_twitter'),
    },
    {
      id: 3,
      icon: images.instagramIcon,
      name: I18n.t('follow_instagram'),
    },
    {
      id: 4,
      icon: images.linkedinIcon,
      name: I18n.t('follow_linkedin'),
    },
  ];

  const renderDataSocial = ({item, index}) => {
    return (
      <TouchableOpacity
        style={styles.renderItemContanier}>
        <Image
          style={styles.renderItemImage}
          resizeMode="contain"
          source={item.icon}
        />
        <Text style={styles.renderItemText}>{item.name}</Text>
        <View style={styles.renderItemArrowContanier}>
          <Image
            resizeMode="contain"
            source={images.arrowDown}
            style={styles.renderItemArrow}
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.contanier} showsVerticalScrollIndicator={false}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle title={I18n.t('about_us')} leftIconPress={handleLeftPress} />
      </View>

      {/* pandits-image */}
      <Image
        source={images.aboutusPandits}
        resizeMode="contain"
        style={styles.aboutusPandits}
      />

      {/* version */}
      <Text style={styles.version}>{I18n.t('app_version')} 1.0.1</Text>

      {/* Head text */}

      <View style={{marginBottom:20}}>
        <Text style={styles.headtext}>
        {I18n.t('head_text')}
        </Text>
        <Text style={styles.headsubtext}>
        {I18n.t('head_subtext')}
        </Text>
      </View>

        {/* first box */}
      <View style={styles.aboutbox}>
        <View style={[styles.backgroundContainer, {opacity: 0.1}]}></View>
        <View style={styles.firstline}>
          <Image
            resizeMode="contain"
            style={styles.aboutimg}
            source={images.aboutimg1}
          />
          <Text style={styles.firstlinetext}>{I18n.t('firstlinefirstbox_text')}</Text>
        </View>
        <Text style={styles.secondtext}>{I18n.t('boxsecond_text')}</Text>
      </View>

      {/* Second box */}
      <View style={styles.aboutbox}>
        <View style={[styles.backgroundContainer, {opacity: 0.1}]}></View>
        <View style={styles.firstline}>
          <Image
            resizeMode="contain"
            style={styles.aboutimg}
            source={images.aboutimg2}
          />
          <Text style={styles.firstlinetext}>{I18n.t('firstlinesecondbox_text')}</Text>
        </View>
        <Text style={styles.secondtext}>{I18n.t('boxtwosecond_text')}</Text>
      </View>


      {/* Third box */}
      <View style={styles.aboutbox}>
        <View style={[styles.backgroundContainer, {opacity: 0.1}]}></View>
        <View style={styles.firstline}>
          <Image
            resizeMode="contain"
            style={styles.aboutimg}
            source={images.aboutimg3}
          />
          <Text style={styles.firstlinetext}>{I18n.t('firstlinethirdbox_text')}</Text>
        </View>
        <Text style={[styles.secondtext,{fontSize:15}]}>{I18n.t('boxthirdsecond_text')}</Text>
      </View>

      <View style={styles.missioncontainer}>
        <Text style={styles.mission}>{I18n.t('ourmission')}</Text>
        <Text style={styles.missionsubtext}>{I18n.t('mission_text')}</Text>
      </View>

      <View style={styles.missioncontainer}>
        <Text style={styles.mission}>{I18n.t('ourvision')}</Text>
        <Text style={styles.missionsubtext}>{I18n.t('vision_text')}</Text>
      </View>
      {/* social */}
      {/* <FlatList
        data={socialData}
        renderItem={renderDataSocial}
        keyExtractor={(item, index) => index}
      /> */}

      {/* powered-by */}
      <View style={styles.poweredContanier}>
        <View style={styles.titleContanier}>
          <View style={[styles.LineDimondContanier]}>
            <View style={styles.line} />
            <View style={[styles.Diamond]} />
          </View>

          <View style={styles.textTitleContanier}>
            <Text style={styles.titleText}>{I18n.t('powered_by')}</Text>
          </View>

          <View style={styles.LineDimondContanier}>
            <View style={[styles.Diamond]} />
            <View style={styles.line} />
          </View>
        </View>

        <Image
          resizeMode="contain"
          style={styles.logixLogo}
          source={images.logixLogo}
        />
      </View>

      {/* render html content */}
      {/* {aboutusData?.content ? (
        <RenderHTML
          contentWidth={styles.contanier.width}
          source={{html: aboutusData.content}}
        />
      ) : (
        <View>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )} */}
    </ScrollView>
  );
};

export default AboutUs;
