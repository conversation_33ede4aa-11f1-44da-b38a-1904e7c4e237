import React, {useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import styles from '../styles/KundaliListData.style';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import NewKundali from '../../KundaliScreen/components/NewKundali';
import SavedKundali from '../../KundaliScreen/components/SavedKundali';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import SummaryTab from './SummaryTab';
import IstroDetailsTab from './IstroDetailsTab';
import NakstrVivrunTab from './NakstrVivrunTab';
import ChartsTab from './ChartsTab';
import KundaliDoshTab from './KundaliDoshTab';
import LagnaReport from './LagnaReport';
import RashiReport from './RashiReport';
import Kundli from './Kundli';
import Sampurn from './Sampurn';
import Prem from './Prem';
import Career from './Career';
import Swasthey from './Swasthey';
import Vitar from './Vitar';

const KundaliListDataScreen = ({navigation, route}) => {
  const [selectedTab, setSelectedTab] = useState(I18n.t('saransh'));
  const {userId ,name, createdAt, place, apidata} = route?.params

  const kundaliListComponentData = [
    {
      id: 1,
      pageName: I18n.t('saransh'),
    },
    {
      id: 2,
      pageName: I18n.t('istro_details'),
    },
    {
      id: 3,
      pageName: I18n.t('nakstr_vivrun'),
    },
    {
      id: 4,
      pageName: I18n.t('charts'),
    },
    {
      id: 5,
      pageName: I18n.t('kundali_dosh'),
    },
    {
      id: 6,
      pageName: I18n.t('lagan_report'),
    },
    {
      id: 7,
      pageName: I18n.t('aapke_grah_rashi_report'),
    },
    {
      id: 8,
      pageName: I18n.t('kundali_list_page'),
    },
    {
      id: 9,
      pageName: I18n.t('sampurn'),
    },
    {
      id: 10,
      pageName: I18n.t('prem'),
    },
    {
      id: 11,
      pageName: I18n.t('career'),
    },
    {
      id: 12,
      pageName: I18n.t('vitar'),
    },
    {
      id: 13,
      pageName: I18n.t('swasthey'),
    },
  ];

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handleTabPress = tabName => {
    setSelectedTab(tabName);
  };

  const renderContent = () => {
    if (selectedTab === I18n.t('saransh')) {
      return <SummaryTab navigation={navigation} apidata={apidata}/>;
    } else if (selectedTab === I18n.t('istro_details')) {
      return <IstroDetailsTab navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('nakstr_vivrun')) {
      return <NakstrVivrunTab navigation={navigation} apidata={apidata}/>;
    } else if (selectedTab === I18n.t('charts')) {
      return <ChartsTab navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('kundali_dosh')) {
      return <KundaliDoshTab navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('lagan_report')) {
      return <LagnaReport navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('aapke_grah_rashi_report')) {
      return <RashiReport navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('kundali_list_page')) {
      return <Kundli navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('sampurn')) {
      return <Sampurn navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('prem')) {
      return <Prem navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('career')) {
      return <Career navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('vitar')) {
      return <Vitar navigation={navigation} apidata={apidata} />;
    } else if (selectedTab === I18n.t('swasthey')) {
      return <Swasthey navigation={navigation} apidata={apidata} />;
    }
  };

  const kundaliListComponentRenderItem = ({item, index}) => {
    const isActive = selectedTab === item.pageName;

    return (
      <View style={styles.tabNavigationContanier}>
        <TouchableOpacity
          style={isActive ? styles.activeTab : styles.inactiveTab}
          onPress={() => handleTabPress(item.pageName)}>
          <Text style={isActive ? styles.activeText : styles.inactiveText}>
            {item.pageName}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('kundali')}
          leftIconPress={handleLeftPress}
          isDownloadIcon={true}
          isLanguageIcon={true}
          // LanguageIconPress={}
          // DownloadIconPress={}
        />
      </View>

      {/* tab-navigation-button */}
      <View style={styles.tabNavigationContanier}>
        <FlatList
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          data={kundaliListComponentData}
          renderItem={kundaliListComponentRenderItem}
          keyExtractor={(item, index) => item.id.toString()}
          ItemSeparatorComponent={() => (
            <View style={{width: DeviceUiInfo.moderateScale(6)}} />
          )}
        />
      </View>

      {/* renderContent */}
      {renderContent()}
    </SafeAreaView>
  );
};

export default KundaliListDataScreen;
