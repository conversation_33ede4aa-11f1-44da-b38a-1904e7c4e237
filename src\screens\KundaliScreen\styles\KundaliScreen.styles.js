import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB16,
  },
  tabNavigationContanier: {
    elevation: 2,
    backgroundColor: colors.white,
    height: DeviceUiInfo.moderateScale(42),
    borderRadius: DeviceUiInfo.moderateScale(10),
    shadowColor: colors.lightGrey,
    flexDirection: 'row',
  },
  activeText: {
    fontSize: fontSize.f14,
    color: colors.background,
    fontFamily: fonts.MontserratSemibold,
  },
  inactiveText: {
    fontSize: fontSize.f14,
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
  },
  inactiveTab: {
    // borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: DeviceUiInfo.moderateScale(42),
    width: '50%',
    backgroundColor: colors.white,
    borderRadius: DeviceUiInfo.moderateScale(10),
  },
  activeTab: {
    // borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: DeviceUiInfo.moderateScale(42),
    width: '50%',
    backgroundColor: colors.primary,
    borderRadius: DeviceUiInfo.moderateScale(10),
  },
});

export default styles;
