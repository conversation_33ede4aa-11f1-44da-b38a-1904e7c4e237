import React, {useState,useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import metrics from '../../theme/metrics';
import {fonts, fontSize} from '../../theme/fonts';
import {colors} from '../../theme/colors';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import CountryPicker from 'react-native-country-picker-modal';

const {margins, paddings} = metrics;
const {height, width} = Dimensions.get('window')

const PhoneNumberVerify = ({
  label,
  // phoneNumber,
  // phoneOnChangeText,
  onCountryCodeChange = () => {}, //recent changed in this component 14-04-2025
  keyboardType,
  value,
  onChangeText,
  // name,
  requirement = false,
  isEditable = true,
  maxLength,
  minLength,
}) => {
  const [countryCode, setCountryCode] = useState('IN');
  const [callingCode, setCallingCode] = useState('91');
  const [visible, setVisible] = useState(false);
  const [localPhoneNumber, setLocalPhoneNumber] = useState(value || '');
  // const {handleChange, errors, values} = useFormikContext();
  // const hasError = errors[name];

  const onSelect = country => {
    setCountryCode(country.cca2);
    setCallingCode(country.callingCode[0]);
    onCountryCodeChange(country.callingCode[0]);
  };


  //recent change 14-04-2025 
  useEffect(() => {
    setLocalPhoneNumber(value || '');
  }, [value]);

  return (
    <View style={styles.contanier}>
      <View style={styles.labelContanier}>
        <Text style={styles.lblStyle}>{label}</Text>
        {requirement && <Text style={styles.requirementText}>*</Text>}
      </View>

      <View style={styles.inputContanier}>
        <TouchableOpacity
          style={styles.countryPickerButton}
          onPress={() => setVisible(true)}>
          <CountryPicker
            visible={visible}
            countryCode={countryCode}
            withFilter
            withFlag
            withCallingCode
            withEmoji={false}
            onSelect={onSelect}
            onClose={() => setVisible(false)}
            containerButtonStyle={styles.countryButton}
          />
          <Icon name="keyboard-arrow-down" style={styles.arrowDown} />
        </TouchableOpacity>
        <View style={styles.dividerColumn} />

        <Text style={[styles.input,{bottom: 0.5}]}>(+{callingCode})</Text>

        <TextInput
          style={[styles.input, {width: '60%'}]}
          keyboardType={keyboardType || 'default'}
          value={localPhoneNumber}
          onChangeText={text => {
            // cleanedText = text.replace(/\D/g, '');
            setLocalPhoneNumber(text);
            onChangeText(text);
          }}
          editable={isEditable}
          minLength={minLength}
          maxLength={maxLength}
        />
      </View>
      {/* </View> */}
      {/* {hasError && <Text style={styles.errorText}>{errors[name]}</Text>} */}
    </View>
  );
};

export default PhoneNumberVerify;

const styles = StyleSheet.create({
  contanier: {
    // flex: 1,
  },
  labelContanier: {
    flexDirection: 'row',
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  lblStyle: {
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratMedium,
    color: colors.poweredByTextColor,
    ...margins.mB2,
  },
  inputContanier: {
    // borderWidth: 1,
    borderRadius: 100,
    shadowColor: colors.lightGrey,
    // shadowColor: 'red',
    shadowOpacity: 10,
    elevation: 4,
    backgroundColor: colors.white,
    flexDirection: 'row',
    alignItems: 'center',
    // ...paddings.pV13,
    // paddingVertical : height > 800 ? 2 : 0
  },
  countryPickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // borderWidth: 1,
  },
  countryButton: {
    borderRadius: 100,
    ...margins.mH14,
    height: DeviceUiInfo.moderateScale(18),
    width: DeviceUiInfo.moderateScale(18),
    bottom: height > 800 ? 0 : 2
  },
  arrowDown: {
    color: colors.arrowDown,
    height: DeviceUiInfo.moderateScale(12),
    width: DeviceUiInfo.moderateScale(12),
  },
  dividerColumn: {
    backgroundColor: colors.dividerBlack,
    height: '40%',
    width: 1,
    ...margins.mH12,
  },
  // textinputContanier: {
  //   flexDirection: 'row',
  //   width: DeviceUiInfo.moderateScale(150),
  //   borderWidth: 1,
  //   // paddingHorizontal: 2,
  //   alignItems: 'center',
  // },
  input: {
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: height > 800 ? 15 : 16
  },
  // errorText: {
  //   ...margins.mT8,
  //   fontSize: fontSize.f12,
  //   fontFamily: fonts.LatoRegular,
  //   color: 'red',
  // },
});
