import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Dimensions,
  Modal
} from 'react-native';
import { CountryCodePicker } from 'react-native-country-codes-picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import metrics from '../../theme/metrics';
import { fonts, fontSize } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import DeviceUiInfo from '../../utils/DeviceUiInfo';

const { margins } = metrics;
const { height } = Dimensions.get('window');

const PhoneNumberVerify = ({
  label,
  onCountryCodeChange = () => {},
  keyboardType,
  value,
  onChangeText,
  requirement = false,
  isEditable = true,
  maxLength,
  minLength,
}) => {
  const [countryCode, setCountryCode] = useState('IN');
  const [callingCode, setCallingCode] = useState('91');
  const [visible, setVisible] = useState(false);
  const [localPhoneNumber, setLocalPhoneNumber] = useState(value || '');

  const onSelect = (country) => {
    setCountryCode(country.code);
    setCallingCode(country.dial_code.replace('+', ''));
    onCountryCodeChange(country.dial_code.replace('+', ''));
    setVisible(false);
  };

  useEffect(() => {
    setLocalPhoneNumber(value || '');
  }, [value]);

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.lblStyle}>{label}</Text>
        {requirement && <Text style={styles.requirementText}>*</Text>}
      </View>

      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={styles.countryPickerButton}
          onPress={() => setVisible(true)}
        >
          <Text style={styles.input}>(+{callingCode})</Text>
          <Icon name="keyboard-arrow-down" style={styles.arrowDown} />
        </TouchableOpacity>

        <View style={styles.dividerColumn} />

        <TextInput
          style={[styles.input, { width: '60%' }]}
          keyboardType={keyboardType || 'default'}
          value={localPhoneNumber}
          onChangeText={(text) => {
            setLocalPhoneNumber(text);
            onChangeText(text);
          }}
          maxLength={maxLength}
          minLength={minLength}
          editable={isEditable}
        />
      </View>

      <Modal visible={visible} animationType="slide" transparent>
        <CountryCodePicker
          show={visible}
          pickerButtonOnPress={onSelect}
          onBackdropPress={() => setVisible(false)}
        />
      </Modal>
    </View>
  );
};

export default PhoneNumberVerify;

const styles = StyleSheet.create({
  container: {},
  labelContainer: {
    flexDirection: 'row',
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  lblStyle: {
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratMedium,
    color: colors.poweredByTextColor,
    ...margins.mB2,
  },
  inputContainer: {
    borderRadius: 100,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
    elevation: 4,
    backgroundColor: colors.white,
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryPickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 14,
  },
  arrowDown: {
    color: colors.arrowDown,
    marginLeft: 6,
  },
  dividerColumn: {
    backgroundColor: colors.dividerBlack,
    height: '40%',
    width: 1,
    ...margins.mH12,
  },
  input: {
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: height > 800 ? 15 : 16,
  },
});
