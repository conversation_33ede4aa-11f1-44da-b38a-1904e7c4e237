import {
  Text,
  View,
  SafeAreaView,
  StatusBar,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import React, {useState} from 'react';
import styles from '../styles/forgotPassword.style';
import TitleSubtitleWithLines from '../../../components/common/TitleSubtitleWithLines';
import I18n from '../../../utils/language/i18nextConfig';
import AppInput from '../../../components/common/AppInput';
import AppButton from '../../../components/common/AppButton';
import {colors} from '../../../theme/colors';
import Apis from '../../../services/apiList';
import {PostServicesWithoutToken} from '../../../services/commonApiMethod';
import AppRoutes from '../../../constants/AppRoutes';
import AppTitle from '../../../components/common/AppTitle';

const ForgotPassword = ({navigation}) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  var api = new Apis();

  const handleforgotpassword = async () => {
    if (!email) {
      Alert.alert('Required', 'Please enter a email');
      return false;
    }

    const emailRegex = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address');
      return false;
    }

    setLoading(true);
    const emaildata = new FormData();
    emaildata.append('email_id', email);

    const payload = {
      url: api.forgotpassword,
      data: emaildata,
      type: 'formData',
    };

    await PostServicesWithoutToken(payload)
      .then(res => {
        setLoading(false);
        if (res?.data?.success) {
          Alert.alert('Note', 'Password reset link sent to your email.', [
            {
              text: 'OK', 
              onPress: () => {
                // After the alert is closed, reset the navigation
                navigation.reset({
                  index: 0,
                  routes: [{ name: AppRoutes.REGISTER_SCREEN }],
                });
              },
            },
          ]);
          // Alert.alert('Note', 'Otp has been sent to your registered email Id');
          // navigation.reset({
          //   index: 0,
          //   route: [{name: AppRoutes.REGISTER_SCREEN}],
          // });
        } else {
          Alert.alert('Note', 'Email Id does not exist');
        }
      }).catch(err => {
        console.log("error from catch>>",err)
      })
  };

  const handleLeftPress = () => {
    navigation.goBack();
  };


  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
      <ScrollView showsVerticalScrollIndicator={false} style={{marginBottom: 12}}>
        <View style={styles.appTitleContanier}>
          <AppTitle 
              leftIconPress={handleLeftPress}
          />
        </View>
        <View style={styles.titleContanier}>
          <TitleSubtitleWithLines
            title={I18n.t('forgot_password')}
            subtitle={I18n.t('password_registered_email_id')}
          />
        </View>

        <View style={styles.phoneNumberContanier}>
          <AppInput
            label={I18n.t('email')}
            requirement={true}
            containerStyle={{marginBottom: 16}}
            value={email}
            onChangeText={setEmail}
            placeholderText={I18n.t('enter_your_registered_email_id')}
          />
        </View>

        <View style={styles.appButtonContanier}>
          <AppButton
            text={I18n.t('submit')}
            onPressed={handleforgotpassword}
            loading={loading}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ForgotPassword;
