import {View, Text, SafeAreaView, FlatList} from 'react-native';
import React from 'react';
import styles from '../styles/Kundli.styles';

const Kundli = ({apidata}) => {
  const numerologyReports = apidata?.numerology?.numerology_reports || {};


   // Convert numerology reports object into an array for FlatList rendering
  const numerologyData = Object.keys(numerologyReports).map((key) => {
    const report = numerologyReports[key];
    return {
      title: report.title,
      description: report.description,
    };
  });

  const kundliData = [
    {
      id: 1,
      title: 'आपके लए शुभ देवता',
      details: `धनुरा1श के \यिf आदश.वादी महQवाकां4ी, उQसाही, धाLमक या नाG*तक, दरू के *थान% और सं*कृ6तय% या िवचार% म&76च रखनेवाले,,
शारी,रक 7प सेसि2य, जोखम लेनेवाले, पय.टनशील , सb6चj , बिहम.ुखी, दाश.िनक, कदा6चत kसOांतवादी, पूवा.$ही,सतही ,अlछे
अवसर% कB तलाश म&और अQय6धक सि2य रहनेवालेहोतेह। धनु, रा1श कB छिव आधेमानव आधेजानवर कB हैऔर शायद आप का
*वभाव भी वसै ा है। आप अ6त महQवाकां4ी ह,ै तथािप आपका *वभाव सबसेअिनयिं 5त इlछा कृ6त का हो सकता ह`,
    },
    {
        id: 2,
        title: 'आपके लए शुभ देवता',
        details: `धनुरा1श के \यिf आदश.वादी महQवाकां4ी, उQसाही, धाLमक या नाG*तक, दरू के *थान% और सं*कृ6तय% या िवचार% म&76च रखनेवाले,,
  शारी,रक 7प सेसि2य, जोखम लेनेवाले, पय.टनशील , सb6चj , बिहम.ुखी, दाश.िनक, कदा6चत kसOांतवादी, पूवा.$ही,सतही ,अlछे
  अवसर% कB तलाश म&और अQय6धक सि2य रहनेवालेहोतेह। धनु, रा1श कB छिव आधेमानव आधेजानवर कB हैऔर शायद आप का
  *वभाव भी वसै ा है। आप अ6त महQवाकां4ी ह,ै तथािप आपका *वभाव सबसेअिनयिं 5त इlछा कृ6त का हो सकता ह`,
      },

      {
        id: 3,
        title: 'आपके लए शुभ देवता',
        details: `धनुरा1श के \यिf आदश.वादी महQवाकां4ी, उQसाही, धाLमक या नाG*तक, दरू के *थान% और सं*कृ6तय% या िवचार% म&76च रखनेवाले,,
  शारी,रक 7प सेसि2य, जोखम लेनेवाले, पय.टनशील , सb6चj , बिहम.ुखी, दाश.िनक, कदा6चत kसOांतवादी, पूवा.$ही,सतही ,अlछे
  अवसर% कB तलाश म&और अQय6धक सि2य रहनेवालेहोतेह। धनु, रा1श कB छिव आधेमानव आधेजानवर कB हैऔर शायद आप का
  *वभाव भी वसै ा है। आप अ6त महQवाकां4ी ह,ै तथािप आपका *वभाव सबसेअिनयिं 5त इlछा कृ6त का हो सकता ह`,
      },
  ];

  const kundliRenderItem = ({item, index}) => {
    return (
      <View style={styles.doshaContanier}>
        <Text style={styles.itemTitleStyle}>{item.title}</Text>
        <View style={styles.doshaDivider} />
        {item.description && (
          <Text style={styles.itemDetailsStyle}>{item.description}</Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <Text style={styles.titleText}>Rashi Report For Your Planet</Text>

      <FlatList
        data={numerologyData}
        renderItem={kundliRenderItem}
        keyExtractor={(item, index) => item.title || index.toString()}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default Kundli;
