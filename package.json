{"name": "pandit<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "clean": "cd android && gradlew clean && cd .. && npx react-native run-android", "appAssembleRelease": "cd android && gradlew clean && gradlew app:assembleRelease && cd ..", "bundleRelease": "cd android && gradlew clean && gradlew bundleRelease && cd ..", "test": "jest"}, "dependencies": {"@microsoft/react-native-clarity": "^4.3.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-firebase/analytics": "^22.2.1", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/messaging": "^22.2.1", "@react-navigation/bottom-tabs": "^7.4.1", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.20", "axios": "^1.10.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "i18next": "^24.2.3", "moment": "^2.30.1", "react": "^18.3.1", "react-i18next": "^15.5.3", "react-native": "^0.77.2", "react-native-config": "^1.5.5", "react-native-country-codes-picker": "^2.3.5", "react-native-curved-bottom-bar": "^3.5.1", "react-native-date-picker": "^5.0.13", "react-native-element-dropdown": "^2.12.4", "react-native-fast-image": "^8.6.3", "react-native-get-location": "^5.0.0", "react-native-html-parser": "^0.1.0", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-otp-textinput": "^1.1.7", "react-native-permissions": "^5.4.1", "react-native-radio-buttons-group": "^3.1.0", "react-native-razorpay": "^2.3.0", "react-native-rename": "^3.2.16", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1", "react-native-share": "^12.1.0", "react-native-svg": "^15.12.0", "react-native-table-component": "^1.2.2", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "@react-native-community/cli": "^15.0.1", "@react-native-community/cli-platform-android": "^15.0.1", "@react-native-community/cli-platform-ios": "^15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-dom": "^18.3.1", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}