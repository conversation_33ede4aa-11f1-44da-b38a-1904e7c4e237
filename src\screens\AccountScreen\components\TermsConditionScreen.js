import React, {useEffect, useState} from 'react';
import {View, ScrollView, StatusBar, ActivityIndicator} from 'react-native';
import styles from '../styles/PrivacyRefundTermsScreen.style';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import Apis from '../../../services/apiList';
import { GetServices } from '../../../services/commonApiMethod';
import RenderHTML from 'react-native-render-html';

const TermsConditionScreen = ({navigation}) => {
  const api = new Apis();
  const [data,setData] = useState([])
  const handleLeftPress = () => {
    navigation.goBack();
  };

    useEffect(() => {
      getterms()
    }, []);
  
  const getterms = async () => {
    const payload = {
      url: `${api.terms}terms-and-conditions`
    }
    await GetServices(payload)
    .then(res => {
      if(res) {
        const termsdata = res?.data
        setData(termsdata)
      }
    })
  }

  return (
    <ScrollView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('terms_conditions')}
          leftIconPress={handleLeftPress}
        />
      </View>

      {/* <Text style={styles.subdetailsText}>
        The contents hereof shall constitute Spiritual services Private Limited
        panditsnearme.com privacy policy in relation to the Service(s) offered
        on this Site panditsnearme.com. All references to ‘our’, ‘us’ or ‘we’
        within this Privacy Policy are deemed to refer to the Company.
        Capitalized terms not defined herein shall have the same meaning
        ascribed to them in this Site’s Terms of Use.
      </Text>

      <Text style={styles.headerText}>
        What is included Personal Information?
      </Text>

      <Text style={styles.subdetailsText}>
        “Personal Information” shall mean any information that relates to a
        natural person, which, either directly or indirectly, in combination
        with other information available or likely to be available with the
        Company, is capable of identifying information of such person including
        but not limited to: passwords; financial information such as bank
        accounts, credit and debit card details or other payment instrument
        details; information received by the Company under lawful contract or
        through other public sources; your details as provided at the time of
        registration or thereafter; and call data records
      </Text>

      <Text style={styles.headerText}>
        What is included Personal Information?
      </Text>

      <Text style={styles.subdetailsText}>
        “Personal Information” shall mean any information that relates to a
        natural person, which, either directly or indirectly, in combination
        with other information available or likely to be available with the
        Company, is capable of identifying information of such person including
        but not limited to: passwords; financial information such as bank
        accounts, credit and debit card details or other payment instrument
        details; information received by the Company under lawful contract or
        through other public sources; your details as provided at the time of
        registration or thereafter; and call data records
      </Text> */}

      {/* Render HTML content if data is loaded */}
      {data?.content ? (
        <RenderHTML
          contentWidth={styles.contanier.width} 
          source={{ html: data.content }} 
        />
      ) : (
        <View>
          <ActivityIndicator size= "large" color={colors.primary}/>
        </View>
      )}

    </ScrollView>
  );
};

export default TermsConditionScreen;
