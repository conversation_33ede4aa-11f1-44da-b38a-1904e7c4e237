import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StatusBar,
  Image,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import styles from '../styles/HoroscopeDetailsScreen.style';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import metrics from '../../../theme/metrics';
import moment from 'moment';
import Apis from '../../../services/apiList';
import {PostServices} from '../../../services/commonApiMethod';
import {images} from '../../../theme/images';
import DatePicker from 'react-native-date-picker';

const HoroscopeDetailsScreen = ({navigation, route}) => {
  const api = new Apis();
  const {horoscopeName, horoscopeImage, rashiName, selectedDate, languagefromhoroscope} =
    route?.params;
  const {margins, paddings} = metrics;
  const [rashiDetailsList, setRashiDetailsList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(languagefromhoroscope === 'hi' ? 'Hindi' : 'English');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [calenderVisible, setcalenderVisible] = useState(false);
  const [updatedDate, setUpdatedDate] = useState(selectedDate);

  useEffect(() => {
    setUpdatedDate(new Date(selectedDate));
  }, [selectedDate]);

  useEffect(() => {
    if (languagefromhoroscope === 'hi') {
      setSelectedLanguage('Hindi'); 
    } else {
      setSelectedLanguage('English'); 
    }
  }, [languagefromhoroscope]); 

  
  const handleLanguageSelect = language => {
    if (language === selectedLanguage) return;
    setSelectedLanguage(language);
    fetchHoroscopeData({
      date: updatedDate, 
      language: language === 'EN' ? 'English' : 'Hindi'
    });
  };


  useEffect(() => {
    fetchHoroscopeData({date: updatedDate, language: selectedLanguage});
  }, [selectedLanguage]);


  const dateFormat = date => {
    const formatDate = moment(date).format('YYYY-MM-DD');
    setUpdatedDate(new Date(formatDate));
    setcalenderVisible(false);
    fetchHoroscopeData({
      date: new Date(formatDate),
      language: selectedLanguage,
    });
  };

  const rashiAkshar = {
    Aries: 'अ, ल, इ',
    Taurus: 'ब, व, उ',
    Gemini: 'क, छ, घ',
    Cancer: 'ड, ह',
    Leo: 'म, ट',
    Virgo: 'प, ठ, ण',
    Libra: 'र, त',
    Scorpio: 'न, य',
    Sagittarius: 'भ, ध, फ, ढ',
    Capricorn: 'ख, ज',
    Aquarius: 'ग, स, श',
    Pisces: 'द, च, झ, थ',
  };

  const rashiAksharEnglish = {
    Aries: 'A, L, I',
    Taurus: 'B, V, U',
    Gemini: 'K, Ch, Gh',
    Cancer: 'D, H',
    Leo: 'M, T',
    Virgo: 'P, Th, N',
    Libra: 'R, T',
    Scorpio: 'N, Y',
    Sagittarius: 'Bh, Dh, Ph, Dh',
    Capricorn: 'Kh, J',
    Aquarius: 'G, S, Sh',
    Pisces: 'D, Ch, Jh, Th',
  };
  

  const fetchHoroscopeData = async ({date = selectedDate, language = selectedLanguage}) => {
    setLoading(true);

    const signMapping = {
      Aries: 'मेष',
      Taurus: 'वृषभ',
      Gemini: 'मिथुन',
      Cancer: 'कर्क',
      Leo: 'सिंह',
      Virgo: 'कन्या',
      Libra: 'तुला',
      Scorpio: 'वृश्चिक',
      Sagittarius: 'धनु',
      Capricorn: 'मकर',
      Aquarius: 'कुम्भ',
      Pisces: 'मीन',
    }

    let updatedRashi = rashiName
    if (language === 'Hindi' && signMapping[rashiName]){
      updatedRashi = signMapping[rashiName]
    }
    // var horoscope = {
    //   date: moment(date).format('YYYY-MM-DD'),
    //   // horoscopeperiod: 'today',
    //   language: language === 'English' ? 'English' : 'Hindi',
    //   // type: 'love',
    //   // zodiac_sign: rashiName,
    // };

    var horoscope = new FormData()
    horoscope.append('date', moment(date).format('YYYY-MM-DD'))
    horoscope.append('language',language)
    horoscope.append('sign',rashiName)

    const payload = {
      url: api.getHoroscope,
      data: horoscope,
      type: 'formData',
    };

    await PostServices(payload)
      .then(res => {
        if (res.success) {
          const showData = res?.data?.horoscope?.[0]?.[updatedRashi];
          updateHoroscopeDetails(showData);
        }
      })
      .catch(error => {
        setLoading(false); 
        console.log('Error', error);
      });
  };

  const updateHoroscopeDetails = horoscopeData => {
    const newRashiDetailsList = Object.entries(horoscopeData).map(
      ([key, value]) => ({
        id: key,
        title: key,
        titleDetalis: value.replace(/[\*\*]/g, ''), 
      }),
    );
    setRashiDetailsList(newRashiDetailsList);
    setLoading(false);
  };

  const rashiDetailsRender = ({item}) => {
    const capitalizeFirstLetter = str => {
      if (str && str.length > 0) {
        return str.charAt(0).toUpperCase() + str.slice(1);
      }
      return str;
    };

    return (
      <View style={{...margins.mB16}}>
        <Text style={styles.titleStyle}>
          {capitalizeFirstLetter(item.title)}
        </Text>
        <Text style={styles.titleDetailsStyle}>{item.titleDetalis}</Text>
      </View>
    );
  };

  const handleLeftPress = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.apptitleContanier}>
        <AppTitle
          title={I18n.t('horoscope')}
          leftIconPress={handleLeftPress}
          isLanguageIcon={true}
          isDropdownVisible={dropdownVisible}
          setDropdownVisible={setDropdownVisible}
          selectedLanguage={selectedLanguage}
          onLanguageSelect={handleLanguageSelect}
        />
      </View>

      {/* rashi-icon-name */}
      <View style={styles.aboutTitleContanier}>
        {/* icon */}
        <View style={styles.IconContanier}>
          <Image
            style={styles.rashiIcon}
            resizeMode="contain"
            source={horoscopeImage}
          />
        </View>

        {/* name */}
        <View style={styles.TextContanier}>
          {/* sub=day-date-month-year */}
          <View style={styles.displaydatecal}>
            <Text style={(languagefromhoroscope === 'hi' ? [styles.aboutText1] : [styles.engaboutText1])}>
              {languagefromhoroscope === 'hi' ? updatedDate.toLocaleString('hi-IN', {
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              }) : moment(updatedDate).format('ddd, Do MMM YYYY')}
            </Text>
            <TouchableOpacity onPress={() => setcalenderVisible(true)}>
              <Image
                style={styles.iconImageStylecal}
                resizeMode="contain"
                source={images.calendarBlue}
              />
            </TouchableOpacity>
          </View>

          {calenderVisible && (
            <DatePicker
              modal
              mode="date"
              open={calenderVisible}
              date={new Date(updatedDate)}
              minimumDate={new Date()}
              onConfirm={dateFormat}
              onCancel={() => setcalenderVisible(false)} 
            />
          )}

          {/* rashi */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              zIndex:1
            }}>
            <Text style={styles.aboutText2}>{I18n.t('rashi')}</Text>
            <Text
              style={[styles.aboutText3, {...margins.mL2, ...margins.mR15}]}>
              :
            </Text>
            <Text style={styles.aboutText3}>
              {/* {horoscopeName} ({rashiName}) */}
              {languagefromhoroscope === 'hi' ? `${horoscopeName} (${rashiName})` : horoscopeName}

            </Text>
          </View>

          {/* akshar */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              zIndex:1
            }}>
            <Text style={styles.aboutText2}>{I18n.t('akshar')}</Text>
            <Text
              style={[styles.aboutText3, {...margins.mL2, ...margins.mR15}]}>
              :
            </Text>
            <Text style={styles.aboutText3}>{ languagefromhoroscope === 'hi' ? rashiAkshar[rashiName] : rashiAksharEnglish[rashiName]}</Text>
          </View>
        </View>
      </View>

      {/* divider */}
      <View style={styles.divider} />

      {/* rashi-details */}
      {loading ? (
        <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={rashiDetailsList}
          renderItem={rashiDetailsRender}
          keyExtractor={(item, index) => item.id.toString()}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

export default HoroscopeDetailsScreen;
