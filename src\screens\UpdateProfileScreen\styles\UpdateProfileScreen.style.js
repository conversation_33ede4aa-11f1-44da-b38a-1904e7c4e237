import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
  },
  accountPhotoContanier: {
    // borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center' 
  },
  elevationBackground: {
    justifyContent: 'center',
    // borderWidth: 1,
    height: DeviceUiInfo.moderateScale(142),
    width: DeviceUiInfo.moderateScale(142),
    borderRadius: DeviceUiInfo.moderateScale(71),
  },
  accountPhoto: {
    alignSelf: 'center',
    height: DeviceUiInfo.moderateScale(99),
    width: DeviceUiInfo.moderateScale(101),
    borderRadius: DeviceUiInfo.moderateScale(70),
  },
  accountEditIcon: {
    bottom: DeviceUiInfo.moderateScale(5),
    right: DeviceUiInfo.moderateScale(26),
    position: 'absolute',
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  textfieldContext: {
    ...margins.mT16,
  },
  appbtnContanier: {
    ...margins.mB29,
  },
  //  dropdownContanierStyle: {
  //     // borderWidth: 1,
  //     zIndex: 1,
  //     position: 'absolute',
  //     bottom: '75%',
  //     left: 0,
  //     right: 0,
  //     borderRadius: DeviceUiInfo.moderateScale(25),
  //     elevation: 1,
  //     backgroundColor: colors.white,
  //     maxHeight: DeviceUiInfo.moderateScale(200),
  //   },
  countrydropdown: {
    backgroundColor: colors.white,
    maxHeight: DeviceUiInfo.moderateScale(200),
    overflow: 'scroll',
    elevation: 1,
    borderRadius: DeviceUiInfo.moderateScale(10),
  },
});

export default styles;
