import React, {useState, useEffect} from 'react';
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import styles from '../styles/NewKundali.styles';
import {colors} from '../../../theme/colors';
import AppInput from '../../../components/common/AppInput';
import I18n from '../../../utils/language/i18nextConfig';
import DropdownTextfield from '../../../components/common/DropdownTextfield';
import metrics from '../../../theme/metrics';
import AppButton from '../../../components/common/AppButton';
import DatePicker from 'react-native-date-picker';
import AppRoutes from '../../../constants/AppRoutes';
import moment from 'moment';
import Apis from '../../../services/apiList';
import {
  PostServices,
  PostServicesWithoutToken,
} from '../../../services/commonApiMethod';
import {saveKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';

const NewKundali = ({navigation}) => {
  const api = new Apis();
  const {margins, paddings} = metrics;
  const [formData, setFormData] = useState({
    name: '',
    // birthDay: '',
    // birthMonth: '',
    // birthYear: '',
    birthDate: '',
    birthTime: '',
    birthPlace: '',
    gender: '',
    language: '',
    // date: ''
  });
  const [showDatePickerDate, setShowDatePickerDate] = useState(false);
  const [showDatePickerTime, setShowDatePickerTime] = useState(false);
  const [isGenderDropdownOpen, setIsGenderDropdownOpen] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [IsLocationDropdownOpen, setIsLocationDropdownOpen] = useState(false);
  const [locationSuggestions, setLocationSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lat, setLat] = useState(null);
  const [long, setLong] = useState(null);
  const [timeZone, setTimeZone] = useState('');

  useEffect(() => {}, [formData.birthDate]);

  const handleGenderDropdownToggle = () => {
    setIsGenderDropdownOpen(prevState => !prevState);

    if (isLanguageDropdownOpen) {
      setIsLanguageDropdownOpen(false);
    }
  };

  const handleLanguageDropdownToggle = () => {
    setIsLanguageDropdownOpen(prevState => !prevState);

    if (isGenderDropdownOpen) {
      setIsGenderDropdownOpen(false);
    }
  };

  // Handle location before change
  // const handleLocationChange = async value => {
  //   setBirthPlace(value); // Update the input field's value

  //   if (value.length >= 3) {
  //     // Trigger the API call after at least 3 characters are typed
  //     fetchLocationSuggestions(value); // Fetch location suggestions from the API
  //   } else {
  //     setLocationSuggestions([]); // Clear suggestions if input is too short
  //   }
  // };

  //changed
  // Handle selecting a location from the dropdown

  const handleLocationChange = location => {
    setFormData({...formData, birthPlace: location});
    setLocationSuggestions([]);
    setIsLocationDropdownOpen(false);
    setIsGenderDropdownOpen(false);
    setIsLanguageDropdownOpen(false);
  };

  const handleDateChange = date => {
    const formattedTime = moment(date).format('HH:mm');
    handleInputChange('birthTime', formattedTime);
    setShowDatePickerTime(false);
  };

  const dateFormat = date => {
    const formatDate = moment(date).format('YYYY-MM-DD');
    setFormData({
      ...formData,
      birthDate: formatDate,
    });
    setShowDatePickerDate(false);
  };

  
  const handleInputChange = (field, value) => {
    setFormData({...formData, [field]: value});
  };


  const dropdownOption = () => ['Male', 'Female'];

  const languageOption = () => ['English', 'Gujarati', 'Hindi'];

  
    const locationfetch2 = async (city_name) => {

      setIsGenderDropdownOpen(false);
      setIsLanguageDropdownOpen(false);

      if (!city_name.trim()) {
        setIsLocationDropdownOpen(false);
        setLocationSuggestions([]);
        return
      }

      setFormData({...formData, birthPlace: city_name});

      let data = JSON.stringify({
        city_name: city_name,
      });

      const payload = {
        url: api.getlocation,
        data: data,
      };

      await PostServicesWithoutToken(payload)
        .then(res => {
          if (res) {
            console.log('REsponse gottt>>>>', res);
          } else {
            console.log('response denied by the rn');
          }
        })
        .catch(err => {
          console.log('error by catch', err);
        });
    };

  // }, []);

  const validateForm = () => {
    // Check if all required fields are filled
    if (
      !formData.name ||
      !formData.birthDate ||
      !formData.birthTime ||
      !formData.birthPlace ||
      !formData.gender ||
      !formData.language
    ) {
      Alert.alert('Required', 'Please fill all required fields');
      return false;
    }
    return true;
  };

  const apidata = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    const formattedBirthDate = moment(formData.birthDate).format('DD/MM/YYYY');
   

    const formjsondata = {
      name: formData.name,
      date: formattedBirthDate,
      time: formData.birthTime,
      place: formData.birthPlace,
      language: formData.language,
      lat: lat,
      lon: long,
      tzone: timeZone,
    };

    const payload = {
      url: api.createkundali,
      data: formjsondata,
    };

    await PostServices(payload)
      .then(async res => {
        setIsLoading(false);

        if (res) {
          const {id} = res?.data[0];
          await saveKey(ASYNC_KEY_CONSTANTS.NEW_KUNDLIID, id);

          navigation.navigate(AppRoutes.VIEW_KUNDALI_DETALIS, {
            kundaliData: formData,
          });
        } else {
          Alert.alert('Kundali details are not proper');
        }
      })
      .catch(err => {
        setIsLoading(false);
        console.log('API Data not Responding ===>', err);
      });
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <ScrollView
        style={{flex: 1, ...margins.mB4}}
        showsVerticalScrollIndicator={false}>
        {/* name */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('name')}
            requirement={true}
            value={formData.name}
            onChangeText={text => handleInputChange('name', text)}
          />
        </View>

        {/* birth-date */}
        <View style={{...margins.mT16}}>
          <TouchableOpacity onPress={() => setShowDatePickerDate(true)}>
            <AppInput
              label={I18n.t('birth_date')}
              isEditable={false}
              requirement={true}
              value={
                formData.birthDate
                  ? moment(formData.birthDate).format('D MMMM, YYYY')
                  : ''
              }
            />
          </TouchableOpacity>

          {/* Date Picker Modal for Birth Date */}
          {showDatePickerDate && (
            <DatePicker
              modal
              mode="date"
              open={showDatePickerDate}
              date={new Date()}
              onConfirm={dateFormat}
              onCancel={() => setShowDatePickerDate(false)}
            />
          )}

          {/* <View style={{flex: 2,...margins.mR5}}>
          <DropdownTextfield
            placeholderText={I18n.t('month')}
            label={I18n.t('birth_date')}
            options={monthData()}
            requirement={true}
            value={formData.birthMonth}
            onSelect={value => handleInputChange('birthMonth', value)}
            isDropdownOpen={dropdownOpen === 1}
            onDropdownToggle={() => handleDropdownToggle(1)}
            // nestedScrollEnabled={true} 
          />
        </View>
        <View style={{flex: 1.3, ...margins.mH7,...margins.mR5}}>
          <DropdownTextfield
            placeholderText={I18n.t('day')}
            options={generateDays()}
            value={formData.birthDay}
            onSelect={value => handleInputChange('birthDay', value)}
          />
        </View>
        <View style={{flex: 1.4,...margins.mH7}}>
          <DropdownTextfield
            placeholderText={I18n.t('year')}
            options={generateYear()}
            value={formData.birthYear}
            onSelect={value => handleInputChange('birthYear', value)}
          />
        </View> */}
        </View>

        {/* birth-time */}
        <TouchableOpacity onPress={() => setShowDatePickerTime(true)}>
          <View
            style={{
              ...margins.mT16,
            }}>
            <AppInput
              label={I18n.t('birth_time')}
              requirement={true}
              value={formData.birthTime}
              isEditable={false}
            />
            {/* <Text>Hello</Text> */}
          </View>
        </TouchableOpacity>

        {/* Time picker modal */}
        {showDatePickerTime && (
          <DatePicker
            modal
            mode="time"
            open={showDatePickerTime}
            date={new Date()}
            onConfirm={date => {
              handleDateChange(date);
            }}
            onCancel={() => {
              setShowDatePickerTime(false);
            }}
          />
        )}

        {/* change area */}

        

        {/* location */}
        {/* <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('birth_place')}
            leftIcon={<Image source={images.location} />}
            value={formData.birthPlace}
            onChangeText={locationfetch2}
            requirement={true}
          />
        </View> */}

        {/* change area completed*/}

        {/* gender */}
        <View style={styles.textfieldContent}>
          <DropdownTextfield
            label={I18n.t('gender')}
            requirement={true}
            options={dropdownOption()}
            value={formData.gender}
            onSelect={value => handleInputChange('gender', value)}
            isDropdownOpen={isGenderDropdownOpen}
            onDropdownToggle={handleGenderDropdownToggle}
            dropdownContanierStyle={styles.countrydropdown}
          />
        </View>

        {/* language */}
        <View style={styles.textfieldContent}>
          <DropdownTextfield
            label={I18n.t('language')}
            requirement={true}
            options={languageOption()}
            value={formData.language}
            onSelect={value => handleInputChange('language', value)}
            isDropdownOpen={isLanguageDropdownOpen}
            onDropdownToggle={handleLanguageDropdownToggle}
            dropdownContanierStyle={styles.countrydropdown}
          />
        </View>

        {/* Loader under the submit button */}
        {isLoading ? (
          <>
            <View style={styles.loaderContainer}>
              <ActivityIndicator
                size="large"
                color={colors.primary}
                style={{flex: 1, marginTop: 30}}
              />
            </View>
          </>
        ) : (
          <>
            {/* app-button */}
            <View style={styles.appbtnContanier}>
              <AppButton text={I18n.t('submit')} onPressed={apidata} />
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default NewKundali;
