import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
  },
  istroContanier: {
    ...margins.mT22,
    ...margins.mB10

  },
  titleText: {
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  istroDetailsContanier: {
    // ...paddings.pH18,
    borderRadius: DeviceUiInfo.moderateScale(20),
    backgroundColor: colors.white,
    elevation: 2,
    overflow: 'hidden',
    ...margins.mT14,
  },
  row: {
    flexDirection: 'row',
    // height: '56',
  },
  keyText: {
    textAlign: 'center',
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
    ...paddings.pV16,
    fontSize: fontSize.f14,
  },
  valueText: {
    textAlign: 'center',
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
    ...paddings.pV16,
  },
  keyContanier: {
    width: '50%',
    backgroundColor: colors.kundaliListBackgroundKey,
    justifyContent: 'center',
  },
  valueContanier: {
    width: '50%',
  },
  divider: {
    // width: '80%',
    // ...margins.mH16,
    backgroundColor: colors.dividerLightGrey,
    height: 1,
  },
});

export default styles;
