//bottom-nav-bar style

import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {colors} from '../../../theme/colors';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  bottomBar: {
    position: 'relative',
  },
  tabStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  fontStyle: {
    fontSize: fontSize.f12,
    ...margins.mT8,
  },
  bookPanditsContanier: {
    // borderRadius: 32.5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  bookPanditsImg: {
    width: DeviceUiInfo.moderateScale(62.66),
    height: DeviceUiInfo.moderateScale(63.71),
    bottom: DeviceUiInfo.moderateScale(18),
  },
  bookPanditsText: {
    fontFamily: fonts.MontserratSemibold,
    color: colors.black,
    fontSize: fontSize.f12,
    textAlign: 'center',
    bottom: DeviceUiInfo.moderateScale(8),
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 10,
    padding: 20,
    width: '90%',
    alignItems: 'center',
    textAlign:'center'
  },
  modalText: {
    color: colors.primary,
    fontFamily: fonts.MontserratBold,
    fontSize: fontSize.f16,
    textAlign: 'center',
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: 10,
    gap:10
  },
  modalDescription:{
    color:'grey',
    fontFamily:fonts.MontserratMedium,
    fontSize:fontSize.f14,
    textAlign:'center',
    marginBottom:10
  },
  btnContainer: {
    height: DeviceUiInfo.moderateScale(32),
    paddingLeft:10,
    paddingRight:15,
    backgroundColor: colors.lightOrange,
    flexDirection:'row',
    borderRadius: DeviceUiInfo.moderateScale(68),
    elevation: 10,
    shadowColor: colors.lightOrange,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    alignItems: 'center',
    justifyContent: 'center',
    ...margins.mB5,
    alignSelf:'center'
  },
  closeIconContainer:{
    // height:DeviceUiInfo.moderateScale(24),
    // width:DeviceUiInfo.moderateScale(24),
    backgroundColor:'#fff',
    position:'absolute',
    top:-20,
    right:-10,
    borderRadius:100,
    padding:5,
    elevation: 10,
    shadowColor: colors.lightOrange,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 3.84,

  }
});

export default styles;
