import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StatusBar,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  Linking
} from 'react-native';
import styles from '../style/ServiceListScreen.style';
import AppTitle from '../../../components/common/AppTitle';
import { colors } from '../../../theme/colors';
import { images } from '../../../theme/images';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import AppRoutes from '../../../constants/AppRoutes';
import FastImage from 'react-native-fast-image';
import { GetServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import LinearGradient from 'react-native-linear-gradient';
import LoginModal from '../../../components/common/LoginModal';
import { getKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import { customAppEvents } from '../../../analytics/AppEvents';
import { translateText } from '../../../services/translation';

const { width, height } = Dimensions.get('window')

const ServiceListScreen = ({ navigation, route }) => {
  const { title, titleInHindi, language } = route?.params;
  const [serviceList, setServiceList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [filteredData,setFilteredData] = useState([]);
  const api = new Apis();

  const [askForLogin, setAskForLogin] = useState(false);
  const flatListRef = useRef(null);
  // Capitalize the first letter of txt
  const capitalizeFirstLetter = (str) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  };

  const getTitle = () => {
    if (language === 'hi') {
      return titleInHindi
    }
    else {
      if (title.toLowerCase() === 'ritual') {
        return 'Rituals';
      }
      return capitalizeFirstLetter(title);
    }
  }


  useEffect(() => {
    fetchApiData(currentPage);
  }, [currentPage]);

  const [translatedTitles, setTranslatedTitles] = useState({});

  useEffect(() => {
    const fetchTranslatedTitles = async () => {
      const titles = {};
      for (const item of filteredData) {
        console.log(item);
        
        const translatedTitle = await translateText(item.title, language);
        titles[item.title] = translatedTitle;
      }
      setTranslatedTitles(titles);
    };

    fetchTranslatedTitles();
  }, [filteredData, language]);

  const fetchApiData = async (page) => {
    if (loading || !hasMoreData) return;

    setLoading(true);

    const payload = {
      url: `${api.getdata}${title.toLowerCase()}&page=${page}`,
    };

    await GetServices(payload)
      .then(response => {
        if (response) {
          setLoading(false);

          if (response?.data?.length < 8) {
            setHasMoreData(false);
          }

          setServiceList((prevList) => {
            const newData = response?.data || [];
            const filteredData = newData.filter(
              (item) => !prevList.some((existingItem) => existingItem.title === item.title)
            );
            setFilteredData([...prevList, ...filteredData])
            return [...prevList, ...filteredData];
          });
        }
      })
      .catch(error => {
        setLoading(false);
      });
  };

  useEffect(() => {
  if (flatListRef.current && serviceList.length >= 4) {
    setTimeout(() => {
      flatListRef.current.scrollToIndex({
        index: 2,
        animated: true,
      });
    }, 500); // wait for FlatList to mount
  }
}, [serviceList]);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handleSearchPress = () => {
    navigation.navigate(AppRoutes.SEARCH_SCREEN, { language: language });
  };

  // Function to handle when user scrolls to the bottom
  const handleLoadMore = () => {
    if (!loading && hasMoreData) {
      setCurrentPage((prevPage) => prevPage + 1);
    }
  };


  const serviceListRenderItem = ({ item }) => {
    const translatedTitle = translatedTitles[item.title] || item.title;
    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() =>
          navigation.navigate(AppRoutes.SERVICE_DETAILS_SCREEN, {
            slug: item.title,
            title: title,
            language: language
          })
        }>
        <FastImage
          source={{
            uri: item?.featured_image,
            headers: { Authorization: 'someAuthToken' },
            priority: FastImage.priority.normal,
          }}
          style={styles.serviceImg}
        />
        <TouchableOpacity
          style={styles.serviceRightArrowContanier}
          onPress={() =>
            navigation.navigate(AppRoutes.SERVICE_DETAILS_SCREEN, {
              slug: item.title,
              title: title,
            })
          }>
          <Image
            // resizeMethod="resize"
            source={images.serviceimgRightArrow}
            style={styles.serviceRightArrowStyle}
          />
        </TouchableOpacity>

        <LinearGradient
          colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.7)']}
          style={styles.gradientBackground}>
          <Text style={styles.serviceText}>{translatedTitle}</Text>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const handlewhatsapp = async () => {
    const phoneNumber = '+919724676277';
    const message = 'Hello, I Have A Query';
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          Linking.openURL(url);
        } else {
          console.log('WhatsApp is not installed or the URL is not supported.');
        }
      })
      .catch(err => console.error('An error occurred', err));
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-bar */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          // title={title.toLowerCase() === 'ritual' ? 'Rituals' : title}
          title={getTitle()}
          leftIconPress={handleLeftPress}
          isSearchIcon={true}
          searchIconPress={handleSearchPress}
        />
      </View>

      {loading && currentPage === 1 ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        /* service-data-list */
        <FlatList
          ref={flatListRef}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) => index.toString()}
          style={{ flex: 1 }}
          data={serviceList}
          renderItem={serviceListRenderItem}
          // numColumns={width > 700 ? 3 : 2}
          contentContainerStyle={styles.flatlistContainer}
          ItemSeparatorComponent={() => (
            <View
              style={{
                height: DeviceUiInfo.moderateScale(10),
              }}
            />
          )}
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No {title} available.</Text>
            </View>
          )}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() => {
            if (!loading || !hasMoreData) {
              return null;
            }
            return null;
          }}
        />
      )}

      {/* whatsapp */}
      <TouchableOpacity
        onPress={handlewhatsapp}
        style={styles.whatsappIconContanier}>
        <Image source={images.whatsappIcon} style={styles.whatsappIconStyle} />
      </TouchableOpacity>

      <LoginModal
        visible={askForLogin}
        onClose={() => setAskForLogin(false)}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default ServiceListScreen;
