import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
  },
  chartContanier: {
    ...margins.mT22,
  },
  titleText: {
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  tableContanier: {
    ...margins.mT14,
    backgroundColor: colors.white,
    borderRadius: DeviceUiInfo.moderateScale(20),
  },
  columnDivider: {
    height: '100%',
    backgroundColor: colors.dividerLightGrey,
    width: 1,
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: colors.kundaliListBackgroundKey,
    borderTopLeftRadius: DeviceUiInfo.moderateScale(20),
    borderTopRightRadius: DeviceUiInfo.moderateScale(20),
    justifyContent: 'space-evenly',
  },
  headerCell: {
    ...paddings.pV13,
    flexDirection: 'row',
  },
  headerText: {
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
    fontSize: fontSize.f10,
  },
  tableRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  tableCell: {
    flexDirection: 'row',
    ...margins.mV10,
  },
  cellText: {
    fontFamily: fonts.MontserratRegular,
    fontSize: fontSize.f11,
    color: colors.dividerBlack,
  },
  divider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
  },
});

export default styles;







//Library style code

// import { StyleSheet } from 'react-native';
// import { colors } from '../../../theme/colors';
// import { fonts, fontSize } from '../../../theme/fonts';
// import metrics from '../../../theme/metrics';

// const { margins, paddings } = metrics;

// const styles = StyleSheet.create({
//   contanier: {
//     flex: 1,
//     backgroundColor: colors.background,
//   },
//   chartContanier: {
//     ...margins.mT22,
//   },
//   titleText: {
//     color: colors.primary,
//     fontFamily: fonts.MontserratSemibold,
//     fontSize: fontSize.f16,
//     marginBottom: 10,
//   },
//   tableContanier: {
//     ...margins.mT14,
//     backgroundColor: colors.white,
//     borderRadius: 0,  // No rounded corners
//     padding: 10,
//   },
//   columnDivider: {
//     height: '100%',
//     backgroundColor: colors.dividerLightGrey,
//     width: 1,
//   },
//   headerRow: {
//     flexDirection: 'row',
//     backgroundColor: colors.kundaliListBackgroundKey,
//     justifyContent: 'space-evenly',
//     height: 40,
//     borderTopWidth: 1,
//     borderBottomWidth: 1,
//     borderColor: '#ccc',
//   },
//   headerCell: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//     alignItems: 'center',
//     paddingVertical: 10,
//   },
//   headerText: {
//     fontFamily: fonts.MontserratSemibold,
//     color: colors.primary,
//     fontSize: fontSize.f12,
//     textAlign: 'center',
//   },
//   row: {
//     flexDirection: 'row',
//     justifyContent: 'space-evenly',
//     height: 50,  // Added height to increase row spacing
//     backgroundColor: colors.white,
//     borderBottomWidth: 1,
//     borderColor: '#ccc',
//   },
//   tableCell: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//     alignItems: 'center',
//     paddingVertical: 10,  // Added vertical padding
//   },
//   cellText: {
//     fontFamily: fonts.MontserratRegular,
//     fontSize: fontSize.f12,
//     color: colors.dividerBlack,
//     textAlign: 'center',  // Ensuring text is centered
//   },
//   divider: {
//     backgroundColor: colors.dividerLightGrey,
//     height: 1,
//   },
// });

// export default styles;








// *******************



// import {StyleSheet} from 'react-native';
// import {colors} from '../../../theme/colors';
// import metrics from '../../../theme/metrics';
// import {fonts, fontSize} from '../../../theme/fonts';
// import DeviceUiInfo from '../../../utils/DeviceUiInfo';
// const {margins, paddings} = metrics;

// const styles = StyleSheet.create({
//   contanier: {
//     flex: 1,
//     backgroundColor: colors.background,
//   },
//   chartContanier: {
//     ...margins.mT22,
//   },
//   titleText: {
//     color: colors.primary,
//     fontFamily: fonts.MontserratSemibold,
//     fontSize: fontSize.f16,
//   },
//   tableContanier: {
//     ...margins.mT14,
//     backgroundColor: colors.white,
//     borderRadius: DeviceUiInfo.moderateScale(20),
//   },
//   columnDivider: {
//     height: '100%',
//     backgroundColor: colors.dividerLightGrey,
//     width: 1,
//   },
//   headerRow: {
//     flexDirection: 'row',
//     backgroundColor: colors.kundaliListBackgroundKey,
//     borderTopLeftRadius: DeviceUiInfo.moderateScale(20),
//     borderTopRightRadius: DeviceUiInfo.moderateScale(20),
//     justifyContent: 'space-evenly',
//   },
//   headerCell: {
//     ...paddings.pV13,
//     flexDirection: 'row',
//   },
//   headerText: {
//     fontFamily: fonts.MontserratSemibold,
//     color: colors.primary,
//     fontSize: fontSize.f10,
//   },
//   tableRow: {
//     flexDirection: 'row',
//     justifyContent: 'space-evenly',
//     alignItems: 'center',
//   },
//   tableCell: {
//     flex:1,
//      alignItems: 'center',
//     flexDirection: 'row',
//     ...margins.mV10,
//   },
//   cellText: {
//     fontFamily: fonts.MontserratRegular,
//     fontSize: fontSize.f11,
//     color: colors.dividerBlack,
//     textAlign: 'center',
//   },
//   divider: {
//     backgroundColor: colors.dividerLightGrey,
//     height: 1,
//     width: '100%', 
    
//   },
// });

// export default styles;
