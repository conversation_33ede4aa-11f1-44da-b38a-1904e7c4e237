import { StyleSheet, Text, View, SafeAreaView, FlatList } from 'react-native'
import React from 'react'
import styles from '../styles/Career.styles'

const Career = ({apidata}) => {
    const CareerData = [
        {
          id: 1,
          details: apidata.horoscope_career || '',
        },
      ];
    
      const CareerRenderItem = ({item, index}) => {
        return (
          <View style={styles.doshaContanier}>
            {item.details && (
              <Text style={styles.itemDetailsStyle}>{item.details}</Text>
            )}
          </View>
        );
      };
    
      return (
        <SafeAreaView style={styles.contanier}>
          <Text style={styles.titleText}>Career</Text>
    
          <FlatList
            data={CareerData}
            renderItem={CareerRenderItem}
            keyExtractor={(item, index) => item.id.toString()}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      );
}

export default Career;