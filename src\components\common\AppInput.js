import React from 'react';
import {View, Text, StyleSheet, TextInput} from 'react-native';
import {fonts, fontSize} from '../../theme/fonts';
import {colors} from '../../theme/colors';
import metrics from '../../theme/metrics';
import DeviceUiInfo from '../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const AppInput = ({
  label,
  value,
  name,
  placeholderText,
  placeholderColor,
  onChangeText,
  isEditable,
  containerStyle,
  leftIcon,
  requirement = false,
  secureTextEntry = false, 
  keyboardType = 'default',
  maxLength,
}) => {
  return (
    <View style={[styles.contanier, containerStyle]}>
      {label && (
        <View style={styles.labelContanier}>
          <Text style={styles.labelStyle}>{label}</Text>
          {requirement && <Text style={styles.requirementText}>*</Text>}
        </View>
      )}

      {leftIcon ? (
        <View style={styles.textinputContanier}>
          <View style={styles.iconContanier}>{leftIcon}</View>
          <TextInput
            value={value}
            style={styles.textInputStyle}
            placeholder={placeholderText}
            placeholderTextColor={placeholderColor}
            onChangeText={onChangeText}
            editable={isEditable}
            secureTextEntry={secureTextEntry}
            keyboardType={keyboardType}
          />
        </View>
      ) : (
        <TextInput
          value={value}
          // style={isEditable === 'true' ? styles.textInputStyle : styles.textInputStyleDisabled}
          style={styles.textInputStyle}
          placeholder={placeholderText}
          placeholderTextColor={placeholderColor}
          onChangeText={onChangeText}
          editable={isEditable}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          maxLength={maxLength}
        />
      )}
      {/* {hasError && <Text style={styles.errorText}>{errors[name]}</Text>} */}
    </View>
  );
};

export default AppInput;

const styles = StyleSheet.create({
  contanier: {},
  labelContanier: {
    // borderWidth: 1,
    flexDirection: 'row',
    ...margins.mB2,
  },
  labelStyle: {
    // borderWidth: 1,
    fontSize: fontSize.f12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  textinputContanier: {
    // borderWidth: 1,
    ...paddings.pH14,
    // ...paddings.pL15,
    flexDirection: 'row',
    // ...margins.mT2,
    width: '100%',
    backgroundColor: colors.white,
    borderRadius: 100,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
    elevation: 4,
    alignItems: 'center',
  },
  textInputStyle: {
    // borderWidth: 2,
    ...paddings.pH14,
    width: '100%',
    ...margins.mT2,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    backgroundColor: colors.white,
    borderRadius: 100,
    elevation: 4,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
  },
  textInputStyleDisabled: {
    // borderWidth: 2,
    ...paddings.pH14,
    width: '100%',
    ...margins.mT2,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    backgroundColor:"rgb(207, 207, 207)",
    borderRadius: 100,
    elevation: 4,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
    opacity: 0.5,
  },
  iconContanier: {
    // borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: DeviceUiInfo.moderateScale(17),
    width: DeviceUiInfo.moderateScale(14),
    // ...margins.ml15,
    // ...margins.mH15,
  },
});
