import { View, Text,SafeAreaView, FlatList } from 'react-native'
import React from 'react'
import styles from '../styles/Prem.styles';

const Prem = ({apidata}) => {
    const PremData = [
        {
          id: 1,
          details: apidata.horoscope_love || '' 
        },
      ];
    
      const PremRenderItem = ({item, index}) => {
        return (
          <View style={styles.doshaContanier}>
            {item.details && (
              <Text style={styles.itemDetailsStyle}>{item.details}</Text>
            )}
          </View>
        );
      };
    
      return (
        <SafeAreaView style={styles.contanier}>
          <Text style={styles.titleText}>Love</Text>
    
          <FlatList
            data={PremData}
            renderItem={PremRenderItem}
            keyExtractor={(item, index) => item.id.toString()}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      );
}

export default Prem;