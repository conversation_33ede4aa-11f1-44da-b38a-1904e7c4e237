import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {paddings, margins} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
  },
  titleText: {
    ...margins.mT22,
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  doshaContanier: {
    // borderWidth: 1,
    ...margins.mT16,
    ...paddings.pV16,
    ...paddings.pH16,
    borderRadius: DeviceUiInfo.moderateScale(20),
    elevation: 3,
    shadowColor: colors.lightGrey,
    backgroundColor: colors.white,
  },
  itemTitleStyle: {
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  doshaDivider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mT8,
  },
  itemDetailsStyle: {
    ...margins.mT8,
    color: colors.poweredByTextColor,
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratMedium,
  },
  itemSubTitleStyle: {
    ...margins.mT12,
    // ...margins.mB8,
    color: colors.dividerBlack,
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratSemibold,
  },
  itemSubDetailsContanier: {
    ...margins.mT8,
    borderWidth: 1,
    alignItems: 'center',
    flexDirection: 'row',
  },
  bullet: {
    borderWidth: 1,
    // alignSelf: 'center',
    color: colors.poweredByTextColor,
  },
  itemSubDetailsStyle: {
    // alignItems: 'center',

    color: colors.poweredByTextColor,
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratMedium,
  },
});

export default styles;
