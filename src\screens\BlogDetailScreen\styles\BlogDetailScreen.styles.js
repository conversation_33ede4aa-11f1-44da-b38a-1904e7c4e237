import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import { fontSize, fonts } from '../../../theme/fonts';

const { width } = Dimensions.get('window');
const { margins, paddings } = metrics;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.background,
    },
    apptitle: {
        ...paddings.pH16,
        ...paddings.pV10,
    },
    blogContainer:{
        // padding:20
        marginHorizontal:'auto'
    },
    scrollView: {
        flex: 1,
    },
    blogImage: {
        width: width * 0.9,
        height: width * 0.6,
        borderRadius:15
    },
    contentContainer: {
        ...paddings.p16,
    },
    blogTitle: {
        fontSize: fontSize.f22,
        fontFamily: fonts.MontserratBold,
        color: colors.primary,
        ...margins.mB10,
    },
    blogDate: {
        fontSize: fontSize.f12,
        fontFamily: fonts.MontserratRegular,
        color: colors.textSecondary,
    },
    blogContent: {
        fontSize: fontSize.f14,
        fontFamily: fonts.MontserratRegular,
        color: colors.text,
    },
    blogHeading: {
        fontSize: fontSize.f20,
        fontFamily: fonts.MontserratBold,
        color: colors.primary,
    },
    blogSubHeading: {
        fontSize: fontSize.f16,
        fontFamily: fonts.MontserratSemibold,
        color: colors.primary,
    },
    blogList: {
    },
    blogListItem: {
        fontSize: fontSize.f14,
        fontFamily: fonts.MontserratRegular,
        color: colors.text,
    },
    linkText: {
        color: colors.primary,
        textDecorationLine: 'underline',
        fontSize: fontSize.f16,
        fontFamily: fonts.MontserratMedium,
    },
    boldText: {
        fontFamily: fonts.MontserratBold,
    },
    italicText: {
        fontStyle: 'italic',
    },
    quoteText: {
        borderLeftWidth: 4,
        borderLeftColor: colors.primary,
        fontStyle: 'italic',
    },
});

export default styles; 