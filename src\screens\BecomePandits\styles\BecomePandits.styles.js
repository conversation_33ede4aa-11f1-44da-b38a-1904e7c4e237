import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import { fontSize, fonts } from '../../../theme/fonts';
const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB24,
  },
  textfieldContent: {
    ...margins.mT16,
  },
  labelStyle: {
    // borderWidth: 1,
    fontSize: fontSize.f12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  appbtnContanier: {
    ...margins.mT42,
    ...margins.mB13,
    // ...margins.mV13,
    justifyContent: 'flex-end',
    flex: 1,
  },
  cancelbtn: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'rgba(255, 0, 0, 0.6)',
    borderRadius: 50,
    padding: 5,
    height: 25,
  },
  labelContanier: {
    ...margins.mB2,
    position:'absolute',
    right:0,
    top:0,
    left:56
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
// dropdownContanierStyle: {
//  // borderWidth: 1,
//  // zIndex: 1,
//     position: 'absolute',
//     bottom: '75%',
//     left: 0,
//     right: 0,
//     borderRadius: DeviceUiInfo.moderateScale(10),
//     elevation: 1,
//     backgroundColor: colors.white,
//     maxHeight: DeviceUiInfo.moderateScale(200),
//  },

  countrydropdown: {
    backgroundColor: colors.white,
    maxHeight: DeviceUiInfo.moderateScale(200),
    overflow: 'scroll',
    elevation: 1,
    borderRadius: DeviceUiInfo.moderateScale(10),
  },
  dropdown: {
    backgroundColor: colors.white,
    // borderWidth: 1,
    borderColor: colors.borderColor,
    borderRadius: DeviceUiInfo.moderateScale(100),
    paddingHorizontal: DeviceUiInfo.moderateScale(12),
    paddingVertical: DeviceUiInfo.moderateScale(8),
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratMedium,
    height: 48,
    // ...paddings.pH14,
    // elevation: 1,
  },
  
});

export default styles;
