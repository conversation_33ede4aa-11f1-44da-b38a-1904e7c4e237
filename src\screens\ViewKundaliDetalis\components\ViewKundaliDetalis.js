import React, { useEffect } from 'react';
import {View, Text, SafeAreaView, StatusBar, FlatList} from 'react-native';
import styles from '../styles/ViewKundaliDetalis.styles';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import metrics from '../../../theme/metrics';
import AppButton from '../../../components/common/AppButton';
import moment from 'moment';
import RazorpayCheckout from 'react-native-razorpay'


const ViewKundaliDetalis = ({navigation, route}) => {
  // const api = new Apis();
  const {margins, paddings} = metrics;
  const {kundaliData} = route.params;

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handlepay = () => {
     var options = {
       description: 'Credits towards consultation',
       image: 'https://i.imgur.com/3g7nmJC.png',
       currency: 'INR',
       key: 'rzp_test_u9hNkkwxyYeLTu',
       amount: '5000',
       name: 'foo',
       prefill: {
         email: '<EMAIL>',
         contact: '9191919191',
         name: 'Razorpay Software'
       },
       theme: {color: '#F37254'}
     }
     RazorpayCheckout.open(options).then((data) => {
       Alert.alert(`Success: ${data.razorpay_payment_id}`);
     }).catch((error) => {
       console.error('Razorpay error:', error);
       Alert.alert(`Error: ${error.code} | ${error.description}`);
     });
   }


  const detailsData = [
    {id: 1, title: 'Name', value: kundaliData.name},
    {
      id: 2,
      title: 'Birth Date',
      value: kundaliData.birthDate
                        ? moment(kundaliData.birthDate).format('D MMMM, YYYY')
                        : ''
    },
    {id: 3, title: 'Birth Time', value: kundaliData.birthTime},
    {id: 4, title: 'Birth Place', value: kundaliData.birthPlace},
    {id: 5, title: 'Gender', value: kundaliData.gender},
    {id: 6, title: 'Language', value: kundaliData.language},
  ];

  const detailsRender = ({item, index}) => {
    return (
      <View style={{flexDirection: 'row'}}>
        <Text style={styles.renderTitleText}>{item.title}</Text>
        <Text style={[styles.renderTitleText, {...margins.mH12}]}>:</Text>
        <Text style={styles.renderValueText}>{item.value}</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('view_kundali_details')}
          leftIconPress={handleLeftPress}
        />
      </View>

      {/* details-contanier */}
      <View style={styles.detailsContanier}>
        <FlatList
          data={detailsData}
          renderItem={detailsRender}
          keyExtractor={(item, index) => item.id.toString()}
        />
      </View>

      {/* app-button */}
      <View style={styles.appbtnContanier}>
        <AppButton
          text={`$ 5${I18n.t('pay_now')}`}
          onPressed={initiatePayment} 
        />
      </View>
    </SafeAreaView>
  );
};

export default ViewKundaliDetalis;
