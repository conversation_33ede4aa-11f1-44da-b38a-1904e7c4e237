import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import { fonts, fontSize } from '../../../theme/fonts';
import { Dimensions } from 'react-native';

const {margins, paddings} = metrics;
const {height,width} = Dimensions.get('window')

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB24,
  },
  textfieldContent: {
    ...margins.mT16,
  },
  labelContanier: {
    flex:1,
    ...margins.mB2,
    position:'absolute',
    right:0,
    top:0,
    left:108
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  calIconImageStyle: {
    position:'absolute',
    right:20,
    top: width > 700 ? height * 0.036 : height * 0.0439 ,
    height: DeviceUiInfo.moderateScale(22),
    width: DeviceUiInfo.moderateScale(18),
  },
  appbtnContanier: {
    // ...margins.mV13,
    justifyContent: 'flex-end',
    bottom:4
    // flex: 1,
  },
  // dropdownContanierStyle: {
  //   // borderWidth: 1,
  //   zIndex: 1,
  //   position: 'absolute',
  //   bottom: '75%',
  //   left: 0,
  //   right: 0,
  //   borderRadius: DeviceUiInfo.moderateScale(10),
  //   elevation: 1,
  //   backgroundColor: colors.white,
  //   maxHeight: DeviceUiInfo.moderateScale(200),
  //   overflow: 'scroll'
  // },
  countrydropdown:{
    backgroundColor: colors.white,
    maxHeight: DeviceUiInfo.moderateScale(200),
    overflow: 'scroll',
    elevation: 1,
    borderRadius: DeviceUiInfo.moderateScale(10),
  },
  dropdown: {
    backgroundColor: colors.white,
    // borderWidth: 1,
    borderColor: colors.borderColor,
    borderRadius: DeviceUiInfo.moderateScale(100),
    paddingHorizontal: DeviceUiInfo.moderateScale(12),
    paddingVertical: DeviceUiInfo.moderateScale(8),
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratMedium,
    height: 48,
    // ...paddings.pH14,
    // elevation: 1,
  },
  labelStyle: {
    // borderWidth: 1,
    fontSize: fontSize.f12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  
});

export default styles;
