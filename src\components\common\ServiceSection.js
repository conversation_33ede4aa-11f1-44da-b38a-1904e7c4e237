import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  FlatList,
  Dimensions,
} from 'react-native';
import { colors } from '../../theme/colors';
import { fonts, fontSize } from '../../theme/fonts';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import metrics from '../../theme/metrics';
import { images } from '../../theme/images';
import I18n from '../../utils/language/i18nextConfig';
import AppRoutes from '../../constants/AppRoutes';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { customAppEvents } from '../../analytics/AppEvents';
import { translateText } from '../../services/translation';
import { getKey } from '../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../constants/AppConstants';


const { margins, paddings } = metrics;
const { height, width } = Dimensions.get('window');

const ServiceSection = ({
  title,
  data,
  language,
  showSeeAll = true,
  navigation: propNavigation,
  // horizontal = true,
}) => {
  const navigation = propNavigation || useNavigation();
  const [translatedTitles, setTranslatedTitles] = useState({});
  // const carouselRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);


  useEffect(() => {
    const fetchTranslatedTitles = async () => {
      try {
        const targetLang = await getKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE);
        if (!targetLang || !data?.length) return;

        const translations = await Promise.all(
          data.map(async (item) => {
            const translatedTitle = await translateText(item.title, targetLang);
            return { original: item.title, translated: translatedTitle };
          })
        );

        const titlesMap = {};
        translations.forEach(({ original, translated }) => {
          titlesMap[original] = translated;
        });

        setTranslatedTitles(titlesMap);
        console.log("✅ Final Translated Titles:", titlesMap);
      } catch (err) {
        console.error("❌ Error during translation:", err);
      }
    };

    fetchTranslatedTitles();
  }, [data, language]);


  const handleService = async (item) => {
    const titleMapping = {
      पूजा: 'puja',
      होम: 'homa',
      'रीति-रिवाज': 'ritual',
      Puja: 'puja',
      Home: 'homa',
      Rituals: 'ritual'
    };

    let formattedtitle = title;
    if (titleMapping[title]) {
      formattedtitle = titleMapping[title].toLowerCase();
    }
    const setTitle = item.title

    await customAppEvents(`looking_for_${setTitle.replaceAll(" ", "_")}`)

    navigation.navigate(AppRoutes.SERVICE_DETAILS_SCREEN, {
      slug: item.title,
      // title: title == 'Rituals' ? 'ritual' : title,
      title: formattedtitle,
      titleInHindi: title,
      language: language,
    });
  }

  const handleServiceItem = async (item) => {
    const titleMapping = {
      पूजा: 'puja',
      होम: 'homa',
      'रीति-रिवाज': 'ritual',
    };

    let formattedtitle = title;
    if (titleMapping[title]) {
      formattedtitle = titleMapping[title].toLowerCase();
    }

    const setTitle = item.title

    await customAppEvents(`looking_for_${setTitle.replaceAll(" ", "_")}`)

    navigation.navigate(AppRoutes.SERVICE_DETAILS_SCREEN, {
      slug: item.title,
      title: formattedtitle,
      titleInHindi: title,
      language: language,
    });
  }

  const serviceRenderItem = ({ item }) => {
    const translatedTitle = translatedTitles[item.title] || item.title;
    return (
      <View style={styles.card}>
        {item.title == "see more" ?
          <TouchableOpacity onPress={() => {
            navigation.navigate((AppRoutes.SERVICE_LIST_SCREEN, {
              // // title: title == 'Rituals' ? 'ritual' : title,
              // title: formattedtitle,
              // serviceList: data,
              // titleInHindi: title,
              // language: language,
            }))
          }}>
            <View style={styles.serviceImg}>
              <Text style={styles.serviceText}>See More</Text>
            </View>

          </TouchableOpacity>
          :
          <TouchableOpacity
            onPress={() => handleService(item)}>
            <Image
              source={{ uri: item.featured_image }}
              style={styles.serviceImg}
            />
            <LinearGradient
              colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.7)']}
              style={styles.gradientBackground}>
              <Text style={styles.serviceText}>{translatedTitle}</Text>
            </LinearGradient>
          </TouchableOpacity>}

      </View>
    );
  };

  return (
    <View style={styles.contanier}>
      <View style={styles.sectionHeaderContanier}>
        {title && <Text style={styles.sectionTitle}>{title}</Text>}

        {showSeeAll && (
          <TouchableOpacity
            onPress={() => {
              const titleMapping = {
                पूजा: 'puja',
                होम: 'homa',
                'रीति-रिवाज': 'ritual',
              };

              let formattedtitle = title;
              if (titleMapping[title]) {
                formattedtitle = titleMapping[title].toLowerCase();
              }

              if (formattedtitle === 'Rituals') {
                formattedtitle = 'ritual';
              }
              navigation.navigate(AppRoutes.SERVICE_LIST_SCREEN, {
                // title: title == 'Rituals' ? 'ritual' : title,
                title: formattedtitle,
                serviceList: data,
                titleInHindi: title,
                language: language,
              });
            }}>
            <Text style={styles.seeAllText}>{I18n.t('see_all')}</Text>
          </TouchableOpacity>
        )}
      </View>

      {<FlatList
        keyExtractor={(item, index) => index}
        showsHorizontalScrollIndicator={false}
        data={[...data, { title: 'See More' }]}
        renderItem={serviceRenderItem}
        // nestedScrollEnabled={true}
        // numColumns={2}
        horizontal={true}
        // ItemSeparatorComponent={() => (
        //   <View style={{width: DeviceUiInfo.moderateScale(10)}} />
        // )}
        ItemSeparatorComponent={() => (
          <View
            style={{
              height: DeviceUiInfo.moderateScale(10),
              // width: DeviceUiInfo.moderateScale(10),
            }}
          />
        )}
      />}
      {/* <Carousel
        width={160}
        height={160}
        data={[...data, { title: 'See More' }]}
        scrollAnimationDuration={500}
        onSnapToItem={(index) => setActiveIndex(index)}
        renderItem={({ item }) => (
          <View
            style={{
              backgroundColor: '#fff',
              borderRadius: 10,
              padding: 20,
              marginHorizontal: 5,
              width: 160,
              height: 160,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text>{item.title}</Text>
          </View>
        )}
      /> */}

      <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 10 }}>
        {[...data, { title: 'See More' }].map((_, i) => (
          <View
            key={i}
            style={{
              height: 8,
              width: 8,
              borderRadius: 4,
              backgroundColor: activeIndex === i ? '#000' : '#ccc',
              marginHorizontal: 4,
            }}
          />
        ))}
      </View>

      {/* <Carousel
        ref={carouselRef}
        data={[...data, { title: 'See More' }]}
        renderItem={serviceRenderItem}
        sliderWidth={width}
        itemWidth={DeviceUiInfo.moderateScale(160) + width * 0.059}
        inactiveSlideScale={0.95}
        inactiveSlideOpacity={0.6}
        enableMomentum={true}
        loop={false}
        onSnapToItem={(index) => setActiveSlide(index)}
        horizontal={true}
      />

      {data.length > 1 && (
        <Pagination
          dotsLength={data.length + 1}
          activeDotIndex={activeSlide}
          // containerStyle={{ paddingVertical: 10 }}
          dotStyle={{
            width: 8,
            height: 8,
            borderRadius: 4,
            marginHorizontal: 4,
            backgroundColor: colors.primary,
          }}
          inactiveDotOpacity={0.4}
          inactiveDotScale={0.8}
        />
      )}
 */}

    </View>
  );
};

export default ServiceSection;

const styles = StyleSheet.create({
  contanier: {},
  sectionHeaderContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderWidth: 1,
    // ...margins.mB10,
  },
  sectionTitle: {
    color: colors.primary,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,

    // borderWidth: 1,
    ...margins.mB10,
  },
  seeAllText: {
    color: colors.arrowDown,
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratMedium,
    ...margins.mB10,
  },
  card: {
    height: DeviceUiInfo.moderateScale(160),
    width: DeviceUiInfo.moderateScale(160),
    // ...margins.mR10,
    marginRight: width > 700 ? width * 0.025 : width * 0.059,
  },
  serviceImg: {
    width: '100%',
    height: '100%',
    // overflow: 'hidden',
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  serviceRightArrowContanier: {
    position: 'absolute',
    right: DeviceUiInfo.moderateScale(9),
    top: DeviceUiInfo.moderateScale(9),
  },
  serviceRightArrowStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  gradientBackground: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(0),
    left: 0,
    right: 0,
    paddingVertical: DeviceUiInfo.moderateScale(60),
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  serviceText: {
    color: colors.white,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(6),
    alignSelf: 'center',
    paddingHorizontal: DeviceUiInfo.moderateScale(8),
    zIndex: 1,
    textAlign: 'center',
  },
});
