import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { colors } from '../../theme/colors';
import { fonts, fontSize } from '../../theme/fonts';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import { images } from '../../theme/images';
import I18n from '../../utils/language/i18nextConfig';
import AppRoutes from '../../constants/AppRoutes';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { customAppEvents } from '../../analytics/AppEvents';
import { translateText } from '../../services/translation';
import { getKey } from '../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../constants/AppConstants';
import SwiperFlatList from 'react-native-swiper-flatlist';

const { height, width } = Dimensions.get('window');

const ServiceSection = ({
  title,
  data,
  language,
  showSeeAll = true,
  navigation: propNavigation,
}) => {
  const navigation = propNavigation || useNavigation();
  const [translatedTitles, setTranslatedTitles] = useState({});
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const fetchTranslatedTitles = async () => {
      try {
        const targetLang = await getKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE);
        if (!targetLang || !data?.length) return;

        const translations = await Promise.all(
          data.map(async (item) => {
            const translatedTitle = await translateText(item.title, targetLang);
            return { original: item.title, translated: translatedTitle };
          })
        );

        const titlesMap = {};
        translations.forEach(({ original, translated }) => {
          titlesMap[original] = translated;
        });

        setTranslatedTitles(titlesMap);
      } catch (err) {
        console.error('❌ Error during translation:', err);
      }
    };

    fetchTranslatedTitles();
  }, [data, language]);

   const handleService = (item) => {
    navigation.navigate(AppRoutes.SERVICE_DETAILS_SCREEN, {
      slug: item.title,
      title: title,
      language: language,
    });
  };

  const handleServiceItem = (item) => {
    const titleMapping = {
      पूजा: 'puja',
      होम: 'homa',
      'रीति-रिवाज': 'ritual',
    };

    let formattedTitle = item.title;
    if (titleMapping[item.title]) {
      formattedTitle = titleMapping[item.title].toLowerCase();
    }

    if (formattedTitle === 'Rituals') {
      formattedTitle = 'ritual';
    }
    navigation.navigate(AppRoutes.SERVICE_LIST_SCREEN, {
      title: formattedTitle,
      serviceList: data,
      titleInHindi: item.title,
      language: language,
    });
  };


  const serviceRenderItem = ({ item }) => {
    if (!item) {
      console.error("Item is undefined:", item);
      return null;
    }

    const translatedTitle = translatedTitles[item.title] || item.title;

    return (
      <View style={styles.card}>
        {/* {item.title === 'See More' ? (
          <TouchableOpacity onPress={() => handleServiceItem(item)}>
            <View
              style={[
                styles.serviceImg,
                {
                  backgroundColor: colors.primary,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <Text style={styles.serviceText}>
                {I18n.t('see_more') || 'See More'}
              </Text>
            </View>
          </TouchableOpacity>
        ) : ( */}
          <TouchableOpacity onPress={() => handleService(item)}>
            {item.featured_image ? (
              <Image source={{ uri: item.featured_image }} style={styles.serviceImg} />
            ) : (
              <View style={[styles.serviceImg, { backgroundColor: colors.grey }]}>
                <Text>No Image Available</Text>
              </View>
            )}
            <LinearGradient
              colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.7)']}
              style={styles.gradientBackground}>
              <Text style={styles.serviceText}>{translatedTitle}</Text>
            </LinearGradient>
          </TouchableOpacity>
        {/* )} */}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeaderContainer}>
        {title && <Text style={styles.sectionTitle}>{title}</Text>}
        {showSeeAll && (
          <TouchableOpacity
            onPress={() => {
              const titleMapping = {
                पूजा: 'puja',
                होम: 'homa',
                'रीति-रिवाज': 'ritual',
              };

              let formattedTitle = title;
              if (titleMapping[title]) {
                formattedTitle = titleMapping[title].toLowerCase();
              }

              if (formattedTitle === 'Rituals') {
                formattedTitle = 'ritual';
              }
              navigation.navigate(AppRoutes.SERVICE_LIST_SCREEN, {
                title: formattedTitle,
                serviceList: data,
                titleInHindi: title,
                language: language,
              });
            }}>
            <Text style={styles.seeAllText}>{I18n.t('see_all')}</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Using SwiperFlatList for the carousel */}
      <SwiperFlatList
        data={data}
        autoplay={true}
        autoplayDelay={3000}
        autoplayLoop={true}
        renderItem={serviceRenderItem}
        horizontal
        style={{ marginBottom: 30, marginTop: 10 }}
      />

    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
  },
  seeAllText: {
    fontSize: fontSize.f12,
    color: colors.arrowDown,
  },
  card: {
    height: DeviceUiInfo.moderateScale(160),
    width: DeviceUiInfo.moderateScale(240),
    marginRight: width > 700 ? width * 0.025 : width * 0.059,
  },
  serviceImg: {
    width: '100%',
    height: '100%',
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  gradientBackground: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: DeviceUiInfo.moderateScale(60),
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  serviceText: {
    color: colors.white,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(6),
    alignSelf: 'center',
    zIndex: 1,
    textAlign: 'center',
  },
});

export default ServiceSection;