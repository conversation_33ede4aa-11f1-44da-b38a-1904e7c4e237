import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB20,
  },
  renderContanier: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  renderText: {
    color: colors.dividerBlack,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratMedium,
  },
  divider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV8,
  },
  renderTickContanier: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  renderTickStyle: {
    height: DeviceUiInfo.moderateScale(30),
    width: DeviceUiInfo.moderateScale(30),
  },
  whatsappIconContanier: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(26),
    right: DeviceUiInfo.moderateScale(20),
  },
  whatsappIconStyle: {
    height: DeviceUiInfo.moderateScale(64),
    width: DeviceUiInfo.moderateScale(64),
  },
  
});

export default styles;
