import {StyleSheet, Text, View, SafeAreaView, FlatList} from 'react-native';
import React from 'react';
import styles from '../styles/RashiReport.styles';

const RashiReport = ({apidata}) => {

  const rashiReports = Object.values(apidata?.rashi_report?.rashi_reports || {} );

  const RashiReportData = [
    {
      id: 1,
      title: 'सूय',
      details: ``,
    },

    {
      id: 2,
      title: 'मगं ल',
     details:``    
    },

    {
      id: 3,
      title: 'च0',
      details: ``
    },

    {
      id: 4,
      title: 'शु2',
      details: ``,
    },
  ];

  const RashiRenderItem = ({item, index}) => {
    return (
      <View style={styles.doshaContanier}>
        <Text style={styles.itemTitleStyle}>{item.planet}</Text>
        <View style={styles.doshaDivider} />
        {item.rashi_report && (
          <Text style={styles.itemDetailsStyle}>{item.rashi_report}</Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <Text style={styles.titleText}>Rashi Report For Your Planet</Text>

      <FlatList
        data={rashiReports}
        renderItem={RashiRenderItem}
        keyExtractor={(item, index) => item.planet}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default RashiReport;
