import React, {useState, useEffect} from 'react';
import {View, Text, SafeAreaView, FlatList} from 'react-native';
import styles from '../styles/IstroDetailsTab.styles';
import moment from 'moment';

const IstroDetailsTab = ({navigation, apidata}) => {
  // Map API data to structure similar to your static istroDetailsData
  const [istroDetailsData, setIstroDetailsData] = useState([]);

  useEffect(() => {
    if (apidata) {
      const basic_details = JSON.parse(apidata?.astro_details?.basic_details);

      const formattedData = [
        {
          id: 1,
          titleName: 'Basic Details',
          subDetails: {
            name: apidata.name || 'N/A',
            date_of_birth: apidata.date_of_birth || 'N/A',
            birth_time: apidata.time || 'N/A',
            latitude: basic_details.lon
              ? parseFloat(basic_details.lat).toFixed(2)
              : 'N/A',
            longitude: basic_details.lon
              ? parseFloat(basic_details.lon).toFixed(2)
              : 'N/A',
            // gender: 'N/A',
            place: apidata.place || 'N/A',
          },
        },
        {
          id: 2,
          titleName: 'Astro Details',
          subDetails: {
            gan: apidata.astro_details?.astro_details?.Gan || 'N/A',
            name: apidata.astro_details?.astro_details?.Name || 'N/A',
            paya: apidata.astro_details?.astro_details?.Paya || 'N/A',
            yoni: apidata.astro_details?.astro_details?.Varna || 'N/A',
            varna: apidata.astro_details?.astro_details?.Vashya || 'N/A',
            vashya: apidata.astro_details?.astro_details?.Yoni || 'N/A',
          },
        },
        {
          id: 3,
          titleName: 'Panchang Details',
          subDetails: {
            yog: apidata.astro_details?.Panchang_details?.Yog || 'N/A',
            karan: apidata.astro_details?.Panchang_details?.Karan || 'N/A',
            tithi: apidata.astro_details?.Panchang_details?.Tithi || 'N/A',
            sunset: apidata.astro_details?.Panchang_details?.Sunset || 'N/A',
            sunrise: apidata.astro_details?.Panchang_details?.Sunrise || 'N/A',
            nakshatra: apidata.astro_details?.Panchang_details?.Nakshatra || 'N/A',
          },
        },
      ];
      
      setIstroDetailsData(formattedData);
    }
  }, [apidata]);

  const formatKey = key => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const renderRow = (key, value, isLast) => {
    return (
      <View key={key}>
        <View style={styles.row}>
          <View style={styles.keyContanier}>
            <Text style={styles.keyText}>{formatKey(key)}</Text>
          </View>
          <View style={styles.valueContanier}>
            <Text style={styles.valueText}>{value}</Text>
          </View>
        </View>
        {!isLast && <View style={styles.divider} />}
      </View>
    );
  };

  const istroDetailRender = ({item, index}) => {
    const subDetailsEntries = Object.entries(item.subDetails);

    return (
      <View style={styles.istroContanier}>
        <Text style={styles.titleText}>{item.titleName}</Text>
        <View style={styles.istroDetailsContanier}>
          {subDetailsEntries.map(([key, value], index) =>
            renderRow(key, value, index === subDetailsEntries.length - 1),
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={istroDetailsData}
        renderItem={istroDetailRender}
        keyExtractor={(item, index) => item.id.toString()}
      />
    </SafeAreaView>
  );
};

export default IstroDetailsTab;
