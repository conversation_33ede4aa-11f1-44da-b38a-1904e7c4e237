import { View, Text, SafeAreaView, FlatList } from 'react-native'
import React from 'react'
import styles from '../styles/Swasthey.styles'

const Swasthey = ({apidata}) => {
    const SwastheyData = [
        {
          id: 1,
          details: apidata.horoscope_health || '',
        },
      ];
    
      const SwastheyRenderItem = ({item, index}) => {
        return (
          <View style={styles.doshaContanier}>
            {item.details && (
              <Text style={styles.itemDetailsStyle}>{item.details}</Text>
            )}
          </View>
        );
      };
    
      return (
        <SafeAreaView style={styles.contanier}>
          <Text style={styles.titleText}>Health</Text>
    
          <FlatList
            data={SwastheyData}
            renderItem={SwastheyRenderItem}
            keyExtractor={(item, index) => item.id.toString()}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      );
}

export default Swasthey