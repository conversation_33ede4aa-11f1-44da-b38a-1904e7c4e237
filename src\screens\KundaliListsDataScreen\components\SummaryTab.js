import React from 'react';
import {View, Text, SafeAreaView} from 'react-native';
import styles from '../styles/SummaryTab.styles';

const SummaryTab = ({navigation , apidata }) => {
  return (
    <SafeAreaView style={styles.contanier}>
      <View style={styles.summaryData}>
        <Text style={styles.titleText}>Summary</Text>
        <View style={styles.summarContanier}>
          <Text style={styles.detailsText}>
            {apidata?.astro_details?.summary}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SummaryTab;
