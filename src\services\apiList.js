export default class Apis {
  // jsonUsers is not used in app, just for example component usage.
  // jsonUsers = 'users';

  // sendOTP           = "wp-json/v1/send-otp";
  login             = "wp-json/v1/user_login"
  submitOTP         = "wp-json/v1/send_otp_email"
  verifyOTP         = "wp-json/v1/verify_otp_email";
  userprofile       = "wp-json/v1/user-profile-update";
  getHoroscope      = "wp-json/v1/get-horoscope";
  getdata           = "wp-json/v1/post-pages?page_name="
  getAllPuja        = "wp-json/become-pandit/all-pujas"
  getdetaildata     = "wp-json/v1/page-details-content"
  booknow           = "wp-json/v1/book-pandit"
  getchogadiya      = "wp-json/v1/get-choghadiya"
  // updateprofile     = "wp-json/v1/user-profile/"
  getuser           = "wp-json/v1/user-profile/"
  terms             = "wp-json/v1/page-content?page="
  muharat           = "wp-json/v1/get-choghadiya"
  createkundali     = "wp-json/v1/create-kundali"
  kundlilist        = "wp-json/kundali/all"
  bookpandit        = "wp-json/v1/book-pandit"
  becomepandit      = "wp-json/v1/become-pandit"
  workingarea       = "wp-json/become-pandit/work-area"
  viewbookpujalist  = "wp-json/book-pandit/get-book-pandit-list"
  workingareanotused       = "wp-json/become-pandit/working-area" //NOt used
  getlocation       = "wp-json/v1/get-location"
  country           = "wp-json/v1/country"
  state             = "wp-json/v1/state"
  city              = "wp-json/v1/city"
  degree            = "wp-json/become-pandit/degree-padvi"
  paymentupdate     = "wp-json/v1/update-payment-status"
  forgotpassword    = "wp-json/v1/forgot_password"
  panchang          = "v1/advanced_panchang"
  timezone          = "wp-json/v1/time_zone"
  orderapi          = "wp-json/razorpay/v1/order-id"
  astrodetails      = "wp-json/kundali/astro-details" //havent used this anywhere
  posts             = "wp-json/wp/v2/posts"

  // muhurat = "api/muhurat/getMuhurat"
}
