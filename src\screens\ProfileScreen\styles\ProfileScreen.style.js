import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  titleContanier: {
    ...margins.mT44,
  },
  formContanier: {
    ...margins.mT18,
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...margins.mB16,
  },
  textfieldContent: {
    ...margins.mB16,
  },
  halfWidth: {
    width: '48%',
  },
  appbtnContanier: {
    ...margins.mB34,
  },
});

export default styles;
