import {StyleSheet, Dimensions} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {paddings, margins} = metrics;
const {height, width} = Dimensions.get('window');

const styles = StyleSheet.create({
  indicator: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH20,
  },
  apptitle: {
    ...margins.mV16,
    zIndex: 1000,
  },
  iconImageStyle: {
    height: DeviceUiInfo.moderateScale(17.42),
    width: DeviceUiInfo.moderateScale(18.42),
  },
  dateinput: {
    // ...margins.mH70,
    marginHorizontal: 10,
    flex: 1,
  },
  prevIconImage: {
    height: DeviceUiInfo.moderateScale(15),
    width: DeviceUiInfo.moderateScale(18),
  },
  nextIconImage: {
    height: DeviceUiInfo.moderateScale(9),
    width: DeviceUiInfo.moderateScale(18),
  },
  secondheader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...margins.mT10,
  },
  prevcontainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prevNextText: {
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratSemibold,
  },

  image: {
    width: '100%',
    height: '100%',
    transform: [{scale: 1.3}],
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  blackboxcontainer: {
    height: height * 0.21,
    width: width * 0.9,
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    ...margins.mT16,
    borderRadius: 20,
  },
  contentOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    padding: 12,
  },
  datetext: {
    fontFamily: fonts.MontserratMedium,
    color: colors.white,
  },
  secondtext: {
    fontFamily: fonts.MontserratBold,
    color: colors.lightOrange,
    fontSize: fontSize.f12,
    // fontWeight:'700'
  },
  secondlinecontainer: {
    flexDirection: 'row',
    ...margins.mT2,
    // flexWrap: 'wrap',
    // alignItems: 'center',
    
   },
  firsttextcontent: {
    flexDirection: 'row',
    ...margins.mR2,
    
  },
  dividergray: {
    height: height * 0.001,
    backgroundColor: colors.white,
    opacity: 0.5,
    ...margins.m3,
    ...margins.mT5,
  },
  detaincontainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // padding:20
    ...margins.mT10,
  },
  rowtext: {
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f15,
    color: colors.background,
    ...margins.mT2
  },
  answertext: {
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
    color: colors.panchangtext,
  },

  belowcontent: {
    // backgroundColor:'skyblue',
    flex: 1,
    ...margins.mT14,
  },
  containermargin: {
    // padding:7,
    // borderWidth:1
    ...margins.mT14,
  },
  calendertext: {
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f18,
    color: colors.dividerBlack,
  },
  cardContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 16,
    ...margins.mT10,
    elevation: 2,
  },
  rowContainer: {
    flexDirection: 'row',
    // justifyContent:'space-between',
    alignItems: 'center',
    ...paddings.pV12,
  },
  divider: {
    height: 1,
    backgroundColor: colors.panchangdivider,
  },
  questionText: {
    flex: 1,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f15,
    color: colors.purple,
  },
  answerText: {
    flex: 1,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f13,
    color: colors.dividerBlack,
  },
  rowContainerpanchang: {
    ...paddings.pV12,
  },
});

export default styles;
