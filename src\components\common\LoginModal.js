import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  Modal,
} from 'react-native';
import { images } from '../../theme/images';
import { colors } from '../../theme/colors';
import { fonts, fontSize } from '../../theme/fonts';
import I18n from '../../utils/language/i18nextConfig';
import styles from './LoginModal.style';
import AppRoutes from '../../constants/AppRoutes';

const LoginModal = ({ visible, onClose, navigation }) => {
  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <TouchableOpacity 
            style={styles.closeIconContainer}
            onPress={onClose}>
            <Image source={images.close} style={{height:20,width:20}} />
          </TouchableOpacity>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', width: '90%', marginBottom: 10 }}>
            <Image source={images.left} />
            <Text style={styles.modalText}>{I18n.t('login_required')}</Text>
            <Image source={images.right} />
          </View>
          <Text style={styles.modalDescription}>
            {I18n.t("to_access_this_service_please_log_in_or_register_for_a_seamless_and_personalized_experience")}
          </Text>
          <View style={styles.modalButtonsContainer}>
            <TouchableOpacity 
              style={styles.btnContainer}
              onPress={() => { 
                onClose();
                navigation.navigate(AppRoutes.REGISTER_SCREEN);
              }}>
              <Image source={images.login} />
              <Text style={{ fontFamily: fonts.MontserratBold, fontSize: fontSize.f14, color: colors.primary }}>
                {I18n.t('login')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.btnContainer}
              onPress={() => { 
                onClose();
                navigation.navigate(AppRoutes.OTP_SCREEN);
              }}>
              <Image source={images.register} />
              <Text style={{ fontFamily: fonts.MontserratBold, fontSize: fontSize.f14, color: colors.primary }}>
                {I18n.t('register')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default LoginModal;
