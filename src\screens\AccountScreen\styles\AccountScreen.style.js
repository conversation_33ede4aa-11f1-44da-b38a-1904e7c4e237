import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {paddings, margins} = metrics;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
  },
  accountPhotoContanier: {
    // borderWidth: 1,
    // ...margins.mT8,
    alignItems: 'center',
    justifyContent: 'center' 
  },
  elevationBackground: {
    justifyContent: 'center',
    // borderWidth: 1,
    height: DeviceUiInfo.moderateScale(142),
    width: DeviceUiInfo.moderateScale(142),
    borderRadius: DeviceUiInfo.moderateScale(71),
  },
  accountPhoto: {
    alignSelf: 'center',
    height: DeviceUiInfo.moderateScale(99),
    width: DeviceUiInfo.moderateScale(101),
    borderRadius: DeviceUiInfo.moderateScale(70),
  },
  accountEditIcon: {
    bottom: DeviceUiInfo.moderateScale(5),
    right: DeviceUiInfo.moderateScale(26),
    position: 'absolute',
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  accountName: {
    ...margins.mT6,
    alignSelf: 'center',
    fontSize: fontSize.f16,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
  },
  accountPageRenderContanier: {
    alignItems: 'center',
    flexDirection: 'row',
    // flex: 1,
  },
  renderIconStyle: {
    height: DeviceUiInfo.moderateScale(18),
    width: DeviceUiInfo.moderateScale(20),
  },
  renderPagename: {
    ...margins.mL10,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    // color: colors.dividerBlack,
  },
  arrowViewContanier: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flex: 1,
  },
  renderArrow: {
    height: DeviceUiInfo.moderateScale(18),
    width: DeviceUiInfo.moderateScale(18),
    transform: [{rotate: '270deg'}],
  },
  accountDivider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV14,
  },
  appButtonContanier: {
    ...margins.mT34,
  },
  logoutBackground: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  logoutModalContanier: {
    ...paddings.pV24,
    ...paddings.pH28,
    alignItems: 'center',
    borderRadius: DeviceUiInfo.moderateScale(22),
    backgroundColor: colors.background,
  },
  logoutText: {
    fontSize: fontSize.f20,
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
  },
  logoutSubText: {
    ...margins.mT8,
    fontSize: fontSize.f14,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  cancelContanier: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: DeviceUiInfo.moderateScale(68),
    height: DeviceUiInfo.moderateScale(36),
    width: DeviceUiInfo.moderateScale(130),
    borderWidth: 1,
  },
  cancelText: {
    fontFamily: fonts.MontserratMedium,
    color: colors.arrowDown,
    fontSize: fontSize.f14,
  },
  yesLogoutContanier: {
    ...margins.mL10,
    backgroundColor: colors.lightOrange,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: DeviceUiInfo.moderateScale(68),
    height: DeviceUiInfo.moderateScale(36),
    width: DeviceUiInfo.moderateScale(130),
  },
  yesLogoutText: {
    fontFamily: fonts.MontserratBold,
    color: colors.background,
    fontSize: fontSize.f14,
  },
});

export default styles;
