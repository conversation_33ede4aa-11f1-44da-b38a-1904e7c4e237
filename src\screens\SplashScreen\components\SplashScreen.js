import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StatusBar,
  ImageBackground,
} from 'react-native';
import styles from '../style/splashScreen.style';
import {colors} from '../../../theme/colors';
import AppRoutes from '../../../constants/AppRoutes';
import {CommonActions} from '@react-navigation/native';
import {images} from '../../../theme/images';
import FastImage from 'react-native-fast-image';
import I18n from '../../../utils/language/i18nextConfig';
import {useNavigation} from '@react-navigation/native';
import {getKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';

const SplashScreen = () => {
  const navigation = useNavigation();


  useEffect(() => {
    const usercheck = async () => {
      const usertoken = await getKey(ASYNC_KEY_CONSTANTS.ACCESS_TOEKN);
      const usercheck = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
      const details = JSON.parse(usercheck);

      setTimeout(() => {
      if (usertoken) {
        if (!details.first_name && !details.last_name) {
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              // routes: [{name: AppRoutes.REGISTER_SCREEN}],
              routes: [{name: AppRoutes.BOTTOM_NAV_BAR}],  // changed as per instruction of PR to use app as guest user
            }),
          );
        } else {
          navigation.dispatch(
              CommonActions.reset({
                  index: 0 ,
                  routes: [{name: AppRoutes.BOTTOM_NAV_BAR}]
                })
              )
            }
      } else {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            // routes: [{name: AppRoutes.REGISTER_SCREEN}],
            routes: [{name: AppRoutes.BOTTOM_NAV_BAR}],  // changed as per instruction of PR to use app as guest user
          }),
        );
      }
    }, 1500);
    };
    usercheck();
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      <ImageBackground
        resizeMode="contain"
        source={images.splashBackgroundTopBottom}
        style={styles.backgroundImage}>
        {/* gif-om-logo */}
        <FastImage
          style={styles.gifLogo}
          source={images.splashScreenOMGif}
          resizeMode={FastImage.resizeMode.contain}
        />

        {/* welcome-panditnearme */}
        <View style={styles.welcomeContanier}>
          <Text style={[styles.welcomeText, {color: colors.lightOrange}]}>
            {I18n.t('welcome_to')}
          </Text>
          <Text style={[styles.welcomeText, {color: colors.primary}]}>
            {I18n.t('panditsnearme')}
          </Text>
        </View>

        {/* powered-by */}
        <View style={styles.poweredContanier}>
          <View style={styles.titleContanier}>
            <View style={[styles.LineDimondContanier]}>
              <View style={styles.line} />
              <View style={[styles.Diamond]} />
            </View>

            <View style={styles.textTitleContanier}>
              <Text style={styles.titleText}>{I18n.t('powered_by')}</Text>
            </View>

            <View style={styles.LineDimondContanier}>
              <View style={[styles.Diamond]} />
              <View style={styles.line} />
            </View>
          </View>

          <Text style={styles.subtitleText}>{I18n.t('logix_built_text')}</Text>
        </View>
      </ImageBackground>
    </View>
  );
};

export {SplashScreen};
