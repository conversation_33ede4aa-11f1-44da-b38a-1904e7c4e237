import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';
import { Dimensions } from 'react-native';

const {paddings, margins} = metrics;
const {width, height} = Dimensions.get('window');

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  apptitle: {
    // borderWidth: 1,
    ...margins.mV16,
  },
  iconImageStyle: {
    height: DeviceUiInfo.moderateScale(17.42),
    width: DeviceUiInfo.moderateScale(18.33),
  },
  demon: {
    ...margins.mL6,
    height: DeviceUiInfo.moderateScale(20),
    width: DeviceUiInfo.moderateScale(20),
  },
  dayChoghadiyaContanier: {
    ...margins.mT24,
    flexDirection: 'row',
    ...paddings.p5,
    alignItems: 'center',
  },
  dayChoghaadiyaText: {
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
    fontSize: fontSize.f16,
    ...margins.mT2,
  },
  ToggleContanier: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
    gap: 12,
    // ...margins.mB10,
  },
  toggleButton: {
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    ...paddings.p8,
    ...paddings.pH10,
  },
  selected: {
    backgroundColor: colors.purple,
  },
  unselected: {
    backgroundColor: 'white',
    borderColor: 'black',
  },
  toggleText: {
    fontSize: 14,
  },
  selectedText: {
    color: 'white',
  },
  unselectedText: {
    color: 'black',
  },
  muhuratclock: {
    // ...margins.mL30,
    height: DeviceUiInfo.moderateScale(22),
    width: DeviceUiInfo.moderateScale(22),
  },
  iconImageStyleArrow: {
    height: DeviceUiInfo.moderateScale(11),
    width: DeviceUiInfo.moderateScale(14),
    right: 2,
  },
  iconImageStyleArrowright: {
    height: DeviceUiInfo.moderateScale(11),
    width: DeviceUiInfo.moderateScale(14),
  },
  dayNightText: {
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratSemibold,
    color: colors.poweredByTextColor,
  },
  divider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV10,
  },
  timmingContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  suryaImageStyle: {
    height: DeviceUiInfo.moderateScale(23.85),
    width: DeviceUiInfo.moderateScale(24),
  },
  timeText: {
    ...margins.mL6,
    fontFamily: fonts.MontserratSemibold,
    color: colors.poweredByTextColor,
    fontSize: fontSize.f12,
  },
  muhuratTimmingContanier: {
    elevation: 1,
    ...paddings.p18,
    ...margins.mT10,
    backgroundColor: colors.white,
    borderRadius: DeviceUiInfo.moderateScale(20),
    // height: DeviceUiInfo.moderateScale(364),
    // borderWidth: 1,
  },
  muhuratText: {
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
    fontWeight: '800',
  },
  muhuratRenderTimeContanier: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flex: 1,
  },
  muhuratStartEndTime: {
    fontSize: fontSize.f12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratSemibold,
  },
  muhuratTO: {
    ...margins.mH5,
    fontSize: fontSize.f12,
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
  },
  muhuratInsideDivider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV10,
  },
  colorSymbolContanier: {
    // ...margins.mT5,
    ...margins.mB8,
    flexDirection: 'row',
    height: height * 0.025,
    // backgroundColor:'red',
    // borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  singalSymbolContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // height: DeviceUiInfo.moderateScale(24),
    // width: DeviceUiInfo.moderateScale(84),
    // borderWidth: 1,
    // justifyContent: 'space-between',
  },
  symbolImg: {
    height: DeviceUiInfo.moderateScale(11),
    width: DeviceUiInfo.moderateScale(22),
  },
  symbolText: {
    ...margins.mL2,
    color: colors.primary,
    fontSize: fontSize.f11,
    fontFamily: fonts.MontserratSemibold,
  },
  prevNextContanier: {
    // borderWidth: 1,
    position: 'absolute',
    left: 0,
    right: 1,
    top: height * 0.095,
    ...margins.mH14,
    // ...margins.mT20,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  prevTouchable: {
    alignItems: 'center',
    flexDirection: 'row',
    // borderWidth: 1,
    justifyContent: 'space-between',
    height: DeviceUiInfo.moderateScale(20),
    width: DeviceUiInfo.moderateScale(70),
  },
  prevIconImage: {
    height: DeviceUiInfo.moderateScale(14),
    width: DeviceUiInfo.moderateScale(18),
  },
  prevNextText: {
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratSemibold,
  },
  nextIconImage: {
    height: DeviceUiInfo.moderateScale(9),
    width: DeviceUiInfo.moderateScale(18),
  },

//styles added after discussed in meeting for muhurat day and night card scroll //20-03-2025 
  cardContainer: {
    width: width * 0.85, 
    padding:10
  },
  card: {
    backgroundColor: colors.white,
    padding: 15,
    borderRadius: 10,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  muhuratTimmingContanierscrollview: {
    marginTop: 10,
  },
  muhuratdaynightcontainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
