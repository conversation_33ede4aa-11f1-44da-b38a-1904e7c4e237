import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  TextInput,
  Image,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Linking
} from 'react-native';
import {colors} from '../../../theme/colors';
import styles from '../styles/SearchScreen.style';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import {images} from '../../../theme/images';
import ServiceSection from '../../../components/common/ServiceSection';
import Apis from '../../../services/apiList';
import {GetServices} from '../../../services/commonApiMethod';
import LoginModal from '../../../components/common/LoginModal';
import { getKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import { customAppEvents } from '../../../analytics/AppEvents';

const SearchScreen = ({navigation,route}) => {
  const api = new Apis();
  const {language} = route?.params
  const [puja, setPuja] = useState([]);
  const [homa, setHoma] = useState([]);
  const [rituals, setRituals] = useState([]);
  const [pujaFilter, setPujaFilter] = useState([]);
  const [homaFilter, setHomaFilter] = useState([]);
  const [ritualsFilter, setRitualsFilter] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [noResults, setNoResults] = useState(false);
  const [placeholderText, setPlaceholderText] = useState('Puja');
  const searchInputRef = useRef(null);
  const [isListening, setIsListening] = useState(false);
  const [askForLogin, setAskForLogin] = useState(false);
  
  useEffect(() => {
    setTimeout(() => {
      searchInputRef.current?.focus(); 
    }, 100); 
  }, []);

  const pujaServices = [
    {id: 1, title: I18n.t('rudrabhishek_puja'), image: images.rudrabhishekPuja},
    {id: 3, title: I18n.t('rudrabhishek_puja'), image: images.rudrabhishekPuja},
    {id: 4, title: I18n.t('rudrabhishek_puja'), image: images.rudrabhishekPuja},
    {id: 5, title: I18n.t('rudrabhishek_puja'), image: images.rudrabhishekPuja},
  ];
  const homeServices = [
    {id: 1, title: I18n.t('chandi_homa'), image: images.chandiHoma},
    {id: 2, title: I18n.t('navachandi_homa'), image: images.navachandiHoma},
    {id: 3, title: I18n.t('navachandi_homa'), image: images.navachandiHoma},
    {id: 4, title: I18n.t('navachandi_homa'), image: images.navachandiHoma},
  ];
  const ritualsServices = [
    {
      id: 1,
      title: I18n.t('pitru_paksha_shradh_puja'),
      image: images.pitruPakshaShradhPuja,
    },
    {
      id: 2,
      title: I18n.t('kaal_sarp_dosh_puja'),
      image: images.ritualsKaalSarpDosh,
    },
    {
      id: 3,
      title: I18n.t('kaal_sarp_dosh_puja'),
      image: images.ritualsKaalSarpDosh,
    },
    {
      id: 4,
      title: I18n.t('kaal_sarp_dosh_puja'),
      image: images.ritualsKaalSarpDosh,
    },
  ];

  useEffect(() => {
    const placeholderCycle = language === 'en' ? ['Puja', 'Home', 'Rituals'] : ['पूजा', 'होम', 'रीतिरिवाज'];
    let index = 0;

    const interval = setInterval(() => {
      setPlaceholderText(placeholderCycle[index]);
      index = (index + 1) % placeholderCycle.length; // Cycle through the array
    }, 1500);

    return () => clearInterval(interval);
  }, []);
  
  useEffect(() => {
    const fetchdata = async () => {
      setIsLoading(true);
      await Pujadata();
      await Homadata();
      await Ritualsdata();
      setIsLoading(false);
    };
    fetchdata();
  }, []);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const Pujadata = async () => {
    const payload = {
      url: api.getdata + 'puja',
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          setPujaFilter(res?.data);
          const slicing = res?.data.slice(0, 4);
          setPuja(slicing);
        } else {
          Alert.alert('Error', 'Puja Data are not here');
        }
      })
      .catch(err => {
        Alert.alert('Error', 'Something went wrong');
      });
  };

  const Homadata = async () => {
    const payload = {
      url: api.getdata + 'homa',
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          setHomaFilter(res?.data);
          const homaslicing = res?.data.slice(0, 4);
          setHoma(homaslicing);
          setHomaFilter(res?.data);
        } else {
          Alert.alert('Error', 'Homa Data are not here');
        }
      })
      .catch(err => {
        Alert.alert('Error', 'Something went wrong');
      });
  };

  const Ritualsdata = async () => {
    const payload = {
      url: api.getdata + 'ritual',
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          const ritualslicing = res?.data.slice(0, 4);
          setRituals(ritualslicing);
          setRitualsFilter(res?.data);
        } else {
          Alert.alert('Error', 'Rituals Data are not here');
        }
      })
      .catch(err => {
        Alert.alert('Error', 'Something went wrong');
      });
  };

  const handleSearch = async(query) => {
    setSearchQuery(query);

    await customAppEvents(`search_for_${query}`)

    const filteredPuja = pujaFilter.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()),
    );

    const filteredHoma = homaFilter.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()),
    );

    const filteredRituals = ritualsFilter.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()),
    );

    if (query.trim() === '') {
      setPuja(pujaFilter.slice(0, 4));
      setHoma(homaFilter.slice(0, 4));
      setRituals(ritualsFilter.slice(0, 4));
      // setPuja(puja);
      // setHoma(homa);
      // setRituals(rituals);
    } else {
      if (filteredPuja.length === 0 && filteredHoma.length === 0 && filteredRituals.length === 0) {

        setPuja([]);
        setHoma([]);
        setRituals([]);
        setNoResults(true);

      } else {
        setPuja(filteredPuja);
        setHoma(filteredHoma);
        setRituals(filteredRituals);
        setNoResults(false)
      }
    }
  };

  if (isLoading) {
    <ActivityIndicator
      size="large"
      color={colors.primary}
      style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}
    />;
  }

   const handlewhatsapp = async () => {
      const phoneNumber = '+919724676277';
      const message = 'Hello, I Have A Query';
      const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
  
      Linking.canOpenURL(url)
        .then(supported => {
          if (supported) {
            Linking.openURL(url);
          } else {
            console.log('WhatsApp is not installed or the URL is not supported.');
          }
        })
        .catch(err => console.error('An error occurred', err));
    };
  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.apptitleContanier}>
        <AppTitle title={I18n.t('search')} leftIconPress={handleLeftPress} />
      </View>

      {/* search-bar */}
      <View style={styles.textinputContanier}>
        <Image source={images.SearchIcon} style={styles.iconContanier} />
        <TextInput
          ref={searchInputRef}
          value={searchQuery}
          style={styles.textInputStyle}
          placeholder={(language === 'en' ? `${I18n.t('search')} for ${placeholderText}` : `${I18n.t('search')} ${placeholderText}` )}
          placeholderTextColor={colors.arrowDown}
          onChangeText={text => handleSearch(text)}
        />
        <View style={styles.searchDivider} />
        <Image source={images.searchAudio} style={styles.iconContanier} />
      </View>

      
      {/* Visual Indication when Listening */}
      {isListening && (
        <View style={styles.listeningOverlay}>
          <Text style={styles.listeningText}>Listening... Speak Now</Text>
        </View>
      )}

      {/* top-search-contanier */}
      <Text style={styles.topSearchesContanierText}>
        {I18n.t('top_searches')}
      </Text>

      {isLoading && (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}
        />
      )}

      {noResults && (
        <Text style={styles.noResultsText}>No Result Found</Text>
      )}

      <ScrollView showsVerticalScrollIndicator={false}>
        {puja && puja.length > 0 && (
          <ServiceSection
            title={I18n.t('puja')}
            data={puja}
            navigation={navigation}
            showSeeAll={false}
          />
        )}

        {/* homa-section */}

        {homa && homa.length > 0 && (
          <View style={styles.sectionContanier}>
            <ServiceSection
              title={I18n.t('homa')}
              data={homa}
              navigation={navigation}
              showSeeAll={false}
            />
          </View>
        )}

        {/* rituals-section */}
        {rituals && rituals.length > 0 && (
          <View style={styles.sectionContanier}>
            <ServiceSection
              title={I18n.t('rituals')}
              data={rituals}
              navigation={navigation}
              showSeeAll={false}
            />
          </View>
        )}
      </ScrollView>

      {/* whatsapp */}
      <TouchableOpacity
        onPress={handlewhatsapp}
        style={styles.whatsappIconContanier}>
        <Image source={images.whatsappIcon} style={styles.whatsappIconStyle} />
      </TouchableOpacity>

      <LoginModal 
        visible={askForLogin}
        onClose={() => setAskForLogin(false)}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default SearchScreen;
