import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

import AppRoutes from '../constants/AppRoutes';
import {SplashScreen} from '../screens/SplashScreen/components/SplashScreen';
import RegisterScreen from '../screens/RegisterScreen/components/RegisterScreen';
import OtpScreen from '../screens/OtpScreen/components/OtpScreen';
import ProfileScreen from '../screens/ProfileScreen/components/ProfileScreen';
import CongratulationScreen from '../screens/CongratulationScreen/components/CongratulationScreen';
import BottomNavBar from '../screens/BottomNavBar/components/BottomNavBar';
import HomeScreen from '../screens/HomeScreen/components/HomeScreen';
import ServiceListScreen from '../screens/ServicesScreen/components/ServiceListScreen';
import ServiceDetailsScreen from '../screens/ServiceDetailsScreen/components/ServiceDetailsScreen';
import BookNow from '../screens/BookNow/components/BookNow';
import SearchScreen from '../screens/SearchScreen/components/SearchScreen';
import AccountScreen from '../screens/AccountScreen/components/AccountScreen';
import HoroscopeScreen from '../screens/HoroscopeScreen/components/HoroscopeScreen';
import HoroscopeDetailsScreen from '../screens/HoroscopeDetailsScreen/components/HoroscopeDetailsScreen';
import MuhuratScreen from '../screens/MuhuratScreen/components/MuhuratScreen';
import UpdateProfileScreen from '../screens/UpdateProfileScreen/components/UpdateProfileScreen';
import AboutUs from '../screens/AboutUs/components/AboutUs';
import LanguageSelectScreen from '../screens/LanguageSelectScreen/components/LanguageSelectScreen';
import ContactUs from '../screens/ContactUs/components/ContactUs';
import KundaliScreen from '../screens/KundaliScreen/components/KundaliScreen';
import ViewKundaliDetalis from '../screens/ViewKundaliDetalis/components/ViewKundaliDetalis';
import BecomePandits from '../screens/BecomePandits/components/BecomePandits';
import PrivacyPolicyScreen from '../screens/AccountScreen/components/PrivacyPolicyScreen';
import RefundPolicyScreen from '../screens/AccountScreen/components/RefundPolicyScreen';
import TermsConditionScreen from '../screens/AccountScreen/components/TermsConditionScreen';
import BookPuja from '../screens/BookPuja/components/BookPuja';
import ViewBookPujaList from '../screens/BookPuja/components/ViewBookPujaList';
import ViewPujaDetails from '../screens/BookPuja/components/ViewPujaDetails';
import KundaliListDataScreen from '../screens/KundaliListsDataScreen/components/KundaliListsDataScreen';
import ForgotPassword from '../screens/ForgotPassword/component/forgotPassword';
import Panchang from '../screens/Panchang/component/Panchang';
import BlogScreen from '../screens/BlogScreen/components/BlogScreen';
import BlogDetailScreen from '../screens/BlogDetailScreen/components/BlogDetailScreen';

const Stack = createNativeStackNavigator();

const AppNavigation = () => {
  return (
    <Stack.Navigator
      initialRouteName={AppRoutes.SPLASHSCREEN}
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name={AppRoutes.SPLASHSCREEN} component={SplashScreen} />
      <Stack.Screen name={AppRoutes.PROFILE_SCREEN} component={ProfileScreen} />
      <Stack.Screen
        name={AppRoutes.REGISTER_SCREEN}
        component={RegisterScreen}
      />
      <Stack.Screen name={AppRoutes.OTP_SCREEN} component={OtpScreen} />
      <Stack.Screen
        name={AppRoutes.CONGRATULATION_SCREEN}
        component={CongratulationScreen}
      />
      <Stack.Screen name={AppRoutes.BOTTOM_NAV_BAR} component={BottomNavBar} />
      <Stack.Screen name={AppRoutes.HOME_SCREEN} component={HomeScreen} />
      <Stack.Screen
        name={AppRoutes.SERVICE_LIST_SCREEN}
        component={ServiceListScreen}
      />
      <Stack.Screen
        name={AppRoutes.SERVICE_DETAILS_SCREEN}
        component={ServiceDetailsScreen}
      />
      <Stack.Screen name={AppRoutes.BOOK_NOW} component={BookNow} />
      <Stack.Screen name={AppRoutes.SEARCH_SCREEN} component={SearchScreen} />
      <Stack.Screen name={AppRoutes.ACCOUNT_SCREEN} component={AccountScreen} />
      <Stack.Screen
        name={AppRoutes.HOROSCOPE_SCREEN}
        component={HoroscopeScreen}
      />
      <Stack.Screen
        name={AppRoutes.HOROSCOPE_DETAILS_SCREEN}
        component={HoroscopeDetailsScreen}
      />
      <Stack.Screen name={AppRoutes.MUHURAT_SCREEN} component={MuhuratScreen} />
      <Stack.Screen
        name={AppRoutes.UPDATE_PROFILE_SCREEN}
        component={UpdateProfileScreen}
      />
      <Stack.Screen name={AppRoutes.ABOUT_US} component={AboutUs} />
      <Stack.Screen
        name={AppRoutes.LANGUAGE_SELECT_SCREEN}
        component={LanguageSelectScreen}
      />
      <Stack.Screen name={AppRoutes.CONTACT_US} component={ContactUs} />
      <Stack.Screen name={AppRoutes.KUNDALI_SCREEN} component={KundaliScreen} />
      <Stack.Screen
        name={AppRoutes.VIEW_KUNDALI_DETALIS}
        component={ViewKundaliDetalis}
      />
      <Stack.Screen name={AppRoutes.BECOME_PANDITS} component={BecomePandits} />
      <Stack.Screen
        name={AppRoutes.PRIVACY_POLICY_SCREEN}
        component={PrivacyPolicyScreen}
      />
      <Stack.Screen
        name={AppRoutes.REFUND_POLICY_SCREEN}
        component={RefundPolicyScreen}
      />
      <Stack.Screen
        name={AppRoutes.TERMS_CONDITION_SCREEN}
        component={TermsConditionScreen}
      />
      <Stack.Screen name={AppRoutes.BOOK_PUJA} component={BookPuja} />
      <Stack.Screen
        name={AppRoutes.VIEW_BOOK_PUJA_LIST}
        component={ViewBookPujaList}
      />
      <Stack.Screen
        name={AppRoutes.VIEW_PUJA_DETAILS}
        component={ViewPujaDetails}
      />
      <Stack.Screen
        name={AppRoutes.KUNDALI_LIST_DATA_SCREEN}
        component={KundaliListDataScreen}
      />

      <Stack.Screen
        name={AppRoutes.FORGOTPASSWORD}
        component={ForgotPassword}
      />

      <Stack.Screen
        name={AppRoutes.PANCHANG}
        component={Panchang}
      />

      <Stack.Screen 
        name={AppRoutes.BLOGSCREEN}
        component={BlogScreen}
      />

      <Stack.Screen 
        name={AppRoutes.BLOG_DETAIL_SCREEN}
        component={BlogDetailScreen}
      />
    </Stack.Navigator>
  );
};

export default AppNavigation;
