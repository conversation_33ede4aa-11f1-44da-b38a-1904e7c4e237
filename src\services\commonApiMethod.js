import { get<PERSON><PERSON> } from '../helper/cookies';
import { Api } from './axiosService';
import * as AppKeys from '../constants/AppConstants';

// GET Request
export async function GetServices(payload) {
  const url = payload.url;
  var passToken = false;
  if (url.includes("wp-json/book-pandit/get-book-pandit-list") || url.includes("wp-json/v1/user-profile/")) {
    var token = await getKey(AppKeys.ASYNC_KEY_CONSTANTS.ACCESS_TOEKN);
    passToken = true
  }

  return Api.get(url,
  passToken
  ? {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  :{}
  )
    .then(response => {
      console.log(" res >> ", response);

      if (response?.status === 200) {
        return {
          message: 'Success',
          success: true,
          data: response?.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: response?.data,
        };
      }
    })
    .catch(e => {
      if (e.response) {
        return {
          success: false,
          data: {},
          error: e.response.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: {
            detail: 'Something went wrong, please try again later.',
          },
        };
      }
    });
}

// POST Request
export async function PostServices(payload) {
  if (payload.url == "wp-json/v1/book-pandit") { var token = await getKey(AppKeys.ASYNC_KEY_CONSTANTS.ACCESS_TOEKN); }

  const url = payload.url;
  const data =
    payload.type === 'formData' ? payload.data : JSON.stringify(payload.data);

  const headers = {
    'Content-Type': payload.type === 'formData'
      ? 'multipart/form-data'
      : 'application/json',
  };

  if (payload.url === 'wp-json/v1/book-pandit') {
    headers.Authorization = 'Bearer ' + token;
  }

  return Api.post(url, data, { headers })
    .then(response => {
      console.log(" res >> ", response);
      if (response?.status) {
        return {
          message: 'Success',
          success: true,
          data: response?.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: response?.data,
        };
      }
    })
    .catch(error => {
      if (error.response) {
        return {
          success: false,
          data: {},
          error: error.response.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: {
            detail: 'Something went wrong, please try again later.',
          },
        };
      }
    });
}

//POST Request Without Token
export async function PostServicesWithoutToken(payload) {
  const url = payload.url;
  const data = payload.type === 'formData' ? payload.data : payload.data;

  return Api.post(url, data, {
    headers: {
      'Content-Type':
        payload.type === 'formData'
          ? 'multipart/form-data'
          : 'application/json',
    },
  })
    .then(response => {
      console.log(" res >> ", response);
      if (response?.status === 200) {
        return {
          message: 'Success',
          success: true,
          data: response?.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: e.response.data,
        };
      }
    })
    .catch(e => {
      if (e.response) {
        return {
          success: false,
          data: {},
          error: e.response?.data,
        };
      }
    });
}

// PUT Request
export async function PutServices(payload) {
  var token = await getKey(AppKeys.ASYNC_KEY_CONSTANTS.ACCESS_TOEKN);
  const url = payload.url;
  const data = payload.data;

  return Api.put(url, data, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'multipart/form-data',
    },
  })
    .then(response => {
      console.log(" res >> ", response);
      if (response?.status === 200) {
        return {
          message: 'Success',
          success: true,
          data: response?.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: response?.data,
        };
      }
    })
    .catch(e => {
      if (e.response) {
        return {
          success: false,
          data: {},
          error: e.response.data,
        };
      } else {
        return {
          success: false,
          data: {},
          error: {
            detail: 'Something went wrong, please try again later.',
          },
        };
      }
    });
}


