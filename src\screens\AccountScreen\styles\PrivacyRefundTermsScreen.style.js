import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    backgroundColor: colors.background,
    flex: 1,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB20,
  },
  subdetailsText: {
    fontFamily: fonts.MontserratRegular,
    fontSize: fontSize.f14,
    color: colors.arrowDown,
    // maxHeight: 2,
  },
  headerText: {
    ...margins.mV16,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f18,
  },
});

export default styles;
