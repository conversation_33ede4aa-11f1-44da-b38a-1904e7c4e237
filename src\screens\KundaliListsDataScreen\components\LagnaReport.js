import {StyleSheet, Text, View, SafeAreaView, FlatList} from 'react-native';
import React from 'react';
import styles from '../styles/LagnaReport.styles';

const LagnaReport = ({navigation, apidata}) => {

  const { asc_report } = apidata?.general_ascendant_report?.general_ascendant_report || {};

  const LagnaReportData = [
    {
      id: 1,
      title:asc_report?.ascendant || 'No Ascendant data available',
      details: asc_report?.report || 'No Report Available'
    }
  ];

  const LagnaRenderItem = ({item, index}) => {
    return (
      <View style={styles.doshaContanier}>
        <Text style={styles.itemTitleStyle}>{item.title}</Text>
        <View style={styles.doshaDivider} />
        {item.details && (
          <Text style={styles.itemDetailsStyle}>{item.details}</Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <Text style={styles.titleText}>Ascendant Report</Text>

      <FlatList
        data={LagnaReportData}
        renderItem={LagnaRenderItem}
        keyExtractor={(item, index) => item.id.toString()}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default LagnaReport;
