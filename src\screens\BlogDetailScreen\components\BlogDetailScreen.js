import React from 'react';
import {
    View,
    StatusBar,
    ScrollView,
    Image,
    Text,
    Dimensions,
} from 'react-native';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import styles from '../styles/BlogDetailScreen.styles';
import HTML from 'react-native-render-html';

const { width } = Dimensions.get('window');

const BlogDetailScreen = ({ navigation, route }) => {
    const { blog } = route.params;
    const imageUrl = blog._embedded?.['wp:featuredmedia']?.[0]?.source_url;

    return (
        <View style={styles.container}>
            <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
            <View style={styles.apptitle}>
                <AppTitle
                    title={I18n.t('blog_details')}
                    leftIconPress={() => navigation.goBack()}
                />
            </View>
            
            <ScrollView 
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
            >
                {imageUrl && (
                    <View style={styles.blogContainer}>
                    <Image
                        source={{ uri: imageUrl }}
                        style={styles.blogImage}
                        resizeMode="cover"
                    />
                    </View>
                )}
                
                <View style={styles.contentContainer}>
                    <Text style={styles.blogTitle}>
                        {blog.title?.rendered?.replace(/&#8211;/g, '-')}
                    </Text>
                    
                    <Text style={styles.blogDate}>
                        {new Date(blog.date).toLocaleDateString()}
                    </Text>

                    <HTML
                        source={{ html: blog.content?.rendered }}
                        contentWidth={width - 40}
                        tagsStyles={{
                            p: styles.blogContent,
                            h2: styles.blogHeading,
                            h3: styles.blogSubHeading,
                            ul: styles.blogList,
                            li: styles.blogListItem,
                        }}
                        // enableExperimentalBRCollapsing={true}
                        // enableExperimentalGhostLinesPrevention={true}
                    />
                </View>
            </ScrollView>
        </View>
    );
};

export default BlogDetailScreen; 