import React from 'react';
import {View, Text, SafeAreaView, FlatList} from 'react-native';
import styles from '../styles/KundaliDoshTab.styles';

const KundaliDoshTab = ({navigation, apidata}) => {
  const {horoscope_dosha} = apidata || {};

  const doshaData = [
    {
      id: 1,
      title: 'Kalsarpa Dosha',
      details: horoscope_dosha?.kalsarpa_details?.one_line,
    },
    {
      id: 2,
      title: 'Manglik Dosha',
      details:
        horoscope_dosha?.manglik?.manglik_report || 'No Manglik Dosha present.',
      subTitle: 'Based on House',
      subDetails:
        horoscope_dosha?.manglik?.manglik_present_rule?.based_on_house || [],
      subTitle2: 'Based on Aspect',
      subDetails2:
        horoscope_dosha?.manglik?.manglik_present_rule?.based_on_aspect || [],
    },
    {
      id: 3,
      title: 'Pitra Dosha',
      subTitle: 'What is Pitri Dosha',
      subDetails: [
        horoscope_dosha?.pitra_dosha?.what_is_pitri_dosha ||
          'No details available for Pitri Dosha.',
      ],
      subTitle2: 'Conclusion',
      subDetails2: [
        horoscope_dosha?.pitra_dosha?.conclusion ||
          'No conclusion for Pitri Dosha available.',
      ],
      highlightTitle: 'Remedies',
      highlightTitleDetails: horoscope_dosha?.pitra_dosha?.remedies || [
        'No remedies available.',
      ],
      highlightTitle2: 'Effects',
      highlightTitleDetails2: horoscope_dosha?.pitra_dosha?.effects || [
        'No effects available.',
      ],
    },
    {
      id: 4,
      title: 'Sadhesati',
      subTitle: 'What is Sadhesati',
      subDetails: [
        horoscope_dosha?.sadhesati?.what_is_sadhesati ||
          'No details available for Sadhesati.',
      ],
      subTitle2: 'Status',
      subDetails2: [
        horoscope_dosha?.sadhesati?.is_undergoing_sadhesati ||
          'No Sadhesati effect currently.',
      ],
      moonSign:
        horoscope_dosha?.sadhesati?.moon_sign || 'No Moon Sign data available.',
      saturnSign:
        horoscope_dosha?.sadhesati?.saturn_sign ||
        'No Saturn Sign data available.',
      remedies: horoscope_dosha?.sadhesati?.remedies || [
        'No remedies available.',
      ],
    },
  ];

  const doshaRenderItem = ({item, index}) => {
    return (
      <View style={styles.doshaContanier}>
        <Text style={styles.itemTitleStyle}>{item.title}</Text>
        <View style={styles.doshaDivider} />
        {item.details && (
          <Text style={styles.itemDetailsStyle}>{item.details}</Text>
        )}

        {item.subTitle && (
          <View>
            <Text style={styles.itemSubTitleStyle}>{item.subTitle}</Text>
            {item.subDetails &&
              item.subDetails.map((details, index) => (
                <View key={index} style={styles.itemSubDetailsContanier}>
                  <Text style={styles.bullet}>{'•'} </Text>
                  <Text style={styles.itemSubDetailsStyle}>{details}</Text>
                </View>
              ))}
          </View>
        )}

        {/* Render second subTitle and subDetails2 if available */}
        {item.subTitle2 && (
          <View>
            <Text style={styles.itemSubTitleStyle}>{item.subTitle2}</Text>
            {item.subDetails2 &&
              item.subDetails2.map((details, subIndex) => (
                <View key={subIndex} style={styles.itemSubDetailsContanier}>
                  <Text style={styles.bullet}>{'•'} </Text>
                  <Text style={styles.itemSubDetailsStyle}>{details}</Text>
                </View>
              ))}
          </View>
        )}

        {/* Render highlightTitle and highlightTitleDetails if available */}
        {item.highlightTitle && (
          <View>
            <Text style={styles.itemSubTitleStyle}>{item.highlightTitle}</Text>
            {item.highlightTitleDetails &&
              item.highlightTitleDetails.map((details, subIndex) => (
                <View key={subIndex} style={styles.itemSubDetailsContanier}>
                  <Text style={styles.bullet}>{'•'} </Text>
                  <Text style={styles.itemSubDetailsStyle}>{details}</Text>
                </View>
              ))}
          </View>
        )}

        {/* Render highlightTitle2 and highlightTitleDetails2 if available */}
        {item.highlightTitle2 && (
          <View>
            <Text style={styles.itemSubTitleStyle}>{item.highlightTitle2}</Text>
            {item.highlightTitleDetails2 &&
              item.highlightTitleDetails2.map((details, subIndex) => (
                <View key={subIndex} style={styles.itemSubDetailsContanier}>
                  <Text style={styles.bullet}>{'•'} </Text>
                  <Text style={styles.itemSubDetailsStyle}>{details}</Text>
                </View>
              ))}
          </View>
        )}

        {/* Render Moon Sign and Saturn Sign for Sadhesati */}
        {item.moonSign && (
          <View>
            <Text style={styles.itemSubTitleStyle}>Moon Sign</Text>
            <Text style={styles.itemSubDetailsStyle}>{item.moonSign}</Text>
          </View>
        )}

        {item.saturnSign && (
          <View>
            <Text style={styles.itemSubTitleStyle}>Saturn Sign</Text>
            <Text style={styles.itemSubDetailsStyle}>{item.saturnSign}</Text>
          </View>
        )}

        {/* Render Remedies for Sadhesati */}
        {item.remedies && (
          <View>
            <Text style={styles.itemSubTitleStyle}>Remedies</Text>
            {item.remedies.map((remedy, remedyIndex) => (
              <View key={remedyIndex} style={styles.itemSubDetailsContanier}>
                <Text style={styles.bullet}>{'•'} </Text>
                <Text style={styles.itemSubDetailsStyle}>{remedy}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <Text style={styles.titleText}>Dosha</Text>

      <FlatList
        data={doshaData}
        renderItem={doshaRenderItem}
        keyExtractor={(item, index) => item.id.toString()}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default KundaliDoshTab;
