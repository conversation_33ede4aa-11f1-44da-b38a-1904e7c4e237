import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StatusBar,
  Image,
  FlatList,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import styles from '../styles/HoroscopeScreen.style';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import {images} from '../../../theme/images';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import AppRoutes from '../../../constants/AppRoutes';
import FastImage from 'react-native-fast-image';
import moment from 'moment';
import DatePicker from 'react-native-date-picker';
import AppInput from '../../../components/common/AppInput';
import Apis from '../../../services/apiList';
const HoroscopeScreen = ({navigation,route}) => {
  var api = new Apis()
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [openPicker, setOpenPicker] = useState(false);
  const [placeholderDate, setPlaceholderDate] = useState('');
  const {language} = route?.params 

  const horoscopeList = [
    {id: 1, name: I18n.t('mesh'), image: images.MeshImage, engRashi: 'Aries'},
    {
      id: 2,
      name: I18n.t('vrushbh'),
      image: images.VrushbhImage,
      engRashi: 'Taurus',
    },
    {id: 3, name: I18n.t('shinh'), image: images.ShinhImage, engRashi: 'Leo'},
    {
      id: 4,
      name: I18n.t('mithun'),
      image: images.MithunImage,
      engRashi: 'Gemini',
    },
    {id: 5, name: I18n.t('kark'), image: images.KarkImage, engRashi: 'Cancer'},
    {
      id: 6,
      name: I18n.t('kaniya'),
      image: images.KaniyaImage,
      engRashi: 'Virgo',
    },
    {id: 7, name: I18n.t('tula'), image: images.TulaImage, engRashi: 'Libra'},
    {
      id: 8,
      name: I18n.t('vrushik'),
      image: images.VrushikImage,
      engRashi: 'Scorpio',
    },
    {
      id: 9,
      name: I18n.t('dhanu'),
      image: images.DhanuImage,
      engRashi: 'Sagittarius',
    },
    {
      id: 10,
      name: I18n.t('makar'),
      image: images.MakarImage,
      engRashi: 'Capricorn',
    },
    {
      id: 11,
      name: I18n.t('kumbh'),
      image: images.KumbhImage,
      engRashi: 'Aquarius',
    },
    {id: 12, name: I18n.t('mith'), image: images.MithImage, engRashi: 'Pisces'},
  ];

  const formatDate = date => {
    if (date) {
      return date.toLocaleDateString('hi-IN', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    }
    return 'Select a date';
  };

  useEffect(() => {
    const currentDateInHindi = formatDate(selectedDate);
    setPlaceholderDate(language === 'hi' ? currentDateInHindi :  moment(selectedDate).format('dddd, MMMM Do YYYY'));
  }, [selectedDate]);

  const formatDateForNavigation = date => {
    return moment(date).format('YYYY-MM-DD');
  };

  const dateFormat = date => {
    const formatDate = moment(date).format('YYYY-MM-DD');
    setSelectedDate(new Date(formatDate))
    setOpenPicker(false);
  };

  const horoscopeRenderItem = ({item}) => {
    return (
      <View style={styles.itemviewContanier}>
        <TouchableOpacity
          style={styles.itemimageBackground}
          onPress={() => 
            navigation.navigate(AppRoutes.HOROSCOPE_DETAILS_SCREEN, {
              horoscopeName: item.name,
              horoscopeImage: item.image,
              rashiName: item.engRashi,
              selectedDate: formatDateForNavigation(selectedDate),
              languagefromhoroscope:language
            })
        }>
          <FastImage
            resizeMode="contain"
            style={styles.itemImage}
            source={item.image}
          />
        </TouchableOpacity>
        <Text style={styles.itemTextStyle}>{item.name}</Text>
      </View>
    );
  };

  const handleLeftPress = () => {
    navigation.goBack();
  };

 
  return (
    <View style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.apptitleCotanier}>
        <AppTitle
          title={I18n.t('select_your_sign')}
          leftIconPress={handleLeftPress}
          isCalendarIcon={true}
          // isLanguageIcon={true}
          // CalendarIconPress={}
          // LanguageIconPress={}
        />
      </View>

<ScrollView contentContainerStyle={{ paddingBottom: 10 }}>
      <TouchableOpacity
        style={styles.dateselector}
        onPress={() => setOpenPicker(true)}>
        <AppInput
          leftIcon={
            <Image
              style={styles.iconImageStyle}
              resizeMode="contain"
              source={images.calendarBlue}
            />
          }
          placeholderText={placeholderDate}
          placeholderColor={colors.placeholderTextColor}
          isEditable={false}
        />
      </TouchableOpacity>

      {/* Date Picker Modal */}
      {openPicker && (
        <DatePicker
          modal
          mode="date"
          open={openPicker}
          date={new Date()}
          minimumDate={new Date()}
          onConfirm={dateFormat}
          onCancel={() => setOpenPicker(false)}
        />
      )}

      {/* horoscope */}
      <View style={{alignItems: 'center'}}>
        <FlatList
          data={horoscopeList}
          renderItem={horoscopeRenderItem}
          keyExtractor={item => item.id.toString()}
          numColumns={3}
          ItemSeparatorComponent={() => (
            <View
              style={{
                height: DeviceUiInfo.moderateScale(22),
              }}
            />
          )}
        />
      </View>
      </ScrollView>  
    </View>
  );
};

export default HoroscopeScreen;
