import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import { Dimensions } from 'react-native';

const {margins, paddings} = metrics;
const {height,width } = Dimensions.get('window')
const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
  },
  apptitleCotanier: {
    ...margins.mV16,
    paddingHorizontal:20
    // borderWidth: 1,
  },

  dayTextContanier: {
    color: colors.poweredByTextColor,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
    ...margins.mB10,
    paddingHorizontal:20
  },
  itemviewContanier: {
    alignItems: 'center',
    // borderWidth: 1,
    height: DeviceUiInfo.moderateScale(128),
    // width: DeviceUiInfo.moderateScale(110),
    width: width * 0.32,
    ...margins.mT10
    // ...margins.mR10,
  },
  dateselector:{
    ...margins.m12,
   ...margins.mB16,
   ...margins.mH12
  },
 iconImageStyle: {
    height: DeviceUiInfo.moderateScale(22),
    width: DeviceUiInfo.moderateScale(18),
  },
  itemimageBackground: {
    elevation: 4,
    borderRadius: DeviceUiInfo.moderateScale(23.68),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    height: DeviceUiInfo.moderateScale(100),
    width: DeviceUiInfo.moderateScale(100),
  },
  itemImage: {
    height: DeviceUiInfo.moderateScale(65),
    width: DeviceUiInfo.moderateScale(65),
  },
  itemTextStyle: {
    ...margins.mT5,
    color: colors.primary,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
  },
});

export default styles;
