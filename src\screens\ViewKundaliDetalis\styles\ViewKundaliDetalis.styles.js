import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    backgroundColor: colors.background,
    flex: 1,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB16,
  },
  detailsContanier: {
    elevation: 1,
    ...paddings.pB20,
    ...paddings.pH19,
    borderRadius: DeviceUiInfo.moderateScale(10),
    backgroundColor: colors.white,
    // borderWidth: 1,
  },
  renderTitleText: {
    ...margins.mT20,
    fontSize: fontSize.f16,
    color: colors.arrowDown,
    fontFamily: fonts.MontserratMedium,
  },
  renderValueText: {
    ...margins.mT20,
    fontSize: fontSize.f16,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
  },
  appbtnContanier: {
    // ...margins.mB29,
    ...margins.mV13,
    justifyContent: 'flex-end',
    flex: 1,
  },
});

export default styles;
