import React, {useState, useEffect} from 'react';
import {
  View,
  StatusBar,
  ImageBackground,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import styles from '../styles/UpdateProfileScreen.style';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import {images} from '../../../theme/images';
import AppInput from '../../../components/common/AppInput';
import metrics from '../../../theme/metrics';
import PhoneNumberVerify from '../../../components/common/PhoneNumberVerify';
import DropdownTextfield from '../../../components/common/DropdownTextfield';
import AppButton from '../../../components/common/AppButton';
import AppRoutes from '../../../constants/AppRoutes';
import Apis from '../../../services/apiList';
import {getKey, saveKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';
import {launchImageLibrary} from 'react-native-image-picker';
import Toast from 'react-native-toast-message';
import {
  GetServices,
  PostServices
} from '../../../services/commonApiMethod';

const UpdateProfileScreen = ({navigation}) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState();
  const [location, setLocation] = useState('');
  const [gender, setGender] = useState('');
  const [loading, setLoading] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(null);
  const [profileImage, setProfileImage] = useState(null);
  const [submitting, setSubmitting] = useState(false);


  const api = new Apis();
  const {margins, paddings} = metrics;

   useEffect(() => {
    getupdatedata();
  }, []);

  const pickImage = () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: 1,
      },
      async response => {
        if (response.didCancel) {
          console.log('User cancel image picker');
        } else if (response.errorCode) {
          console.log('Image Picker error:', response.errorMessage);
        } else {
          const selectedImage = response.assets[0].uri;
          const asset = response.assets[0];
          const fileType = asset.type?.toLowerCase();
          if (
            fileType !== 'image/jpeg' &&
            fileType !== 'image/jpg' &&
            fileType !== 'image/png'
          ) {
            Toast.show({
              type: 'error',
              text1: 'Invalid file format',
              text2: 'Please select a JPG, JPEG, or PNG image.',
              position: 'bottom',
            });
            return;
          }

          setProfileImage(selectedImage);

          await saveKey(ASYNC_KEY_CONSTANTS.USER_PROFILE_IMAGE, selectedImage);
        }
      },
    );
  };

  const getProfileImageFromStorage = async () => {
    try {
      const savedUri = await getKey(ASYNC_KEY_CONSTANTS.USER_PROFILE_IMAGE);
      if (savedUri) {
        setProfileImage(savedUri);
      }
    } catch (error) {
      console.error('Error fetching image URI from storage:', error);
    }
  };

  useEffect(() => {
    getProfileImageFromStorage();
  }, []);

  const handleLeftPress = () => {
    navigation.goBack();
  };
  const dropdownOption = () => ['Male', 'Female'];

  const handleupdate = async () => {
    //validation code
    if (!firstName) {
      Alert.alert('Note', 'First name is required!');
      return;
    }
    if (!lastName) {
      Alert.alert('Note', 'Last name is required!');
      return;
    }
    if (!email) {
      Alert.alert('Note', 'Email is required!');
      return;
    }
    if (!phone) {
      Alert.alert('Note', 'Phone number is required!');
      return;
    }
    if (!location) {
      Alert.alert('Note', 'Location is required!');
      return;
    }
    if (!gender) {
      Alert.alert('Note', 'Gender is required!');
      return;
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    if (phone.length < 7 || phone.length > 12) {
      Alert.alert(
        'Invalid Phone Number',
        'Phone number must be between 7 and 12 digits.',
      );
      return;
    }
    //----------
    setSubmitting(true);
    const userdetail = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    const userdetailObj = JSON.parse(userdetail);
   
    const userdetailid = JSON.parse(userdetail).ID;
   
    const formdata = new FormData();
    formdata.append('first_name', firstName);
    formdata.append('last_name', lastName);
    formdata.append('email', email);
    formdata.append('phone', phone);
    formdata.append('location', location);
    formdata.append('gender', gender);

    if (profileImage) {
      const uri = profileImage;
      const fileType = uri.split('.').pop();
      const fileName = `profile_${userdetailid}.${fileType}`;

      formdata.append('profile_image', {
        uri: profileImage,
        name: fileName,
        type: `image/${fileType}`,
      });
    }

    const payload = {
      url: `${api.userprofile}/${userdetailid}`,
      data: formdata,
      type: 'formData',
    };

    await PostServices(payload)
      .then(async res => {
        if (res) {
          userdetailObj.first_name = firstName;
          userdetailObj.last_name = lastName;
          userdetailObj.email = email;
          await saveKey(
            ASYNC_KEY_CONSTANTS.USER_DETAIL,
            JSON.stringify(userdetailObj),
          );
          Alert.alert('Sucess', 'Sucesssfully updated');

          navigation.replace(AppRoutes.BOTTOM_NAV_BAR, {
            updatedFirstName: firstName,
            updatedLastName: lastName,
          });
        }
      })
      .catch(err => {
        console.error('Error in update profile request:', err);
      }).finally(() => {
        setSubmitting(false); 
      });
  };

  const handleDropdownToggle = dropdownId => {
    setDropdownOpen(dropdownOpen === dropdownId ? null : dropdownId);
  };

  const getupdatedata = async () => {
    const userdetail = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    console.log("userDetail >> " , userdetail);
    
    const userdetailid = JSON.parse(userdetail).ID;
    const userdet = JSON.parse(userdetail);

    const payload = {
      url: `${api.getuser}${userdetailid}`,
    };


    await GetServices(payload)
      .then(res => {
        if (res) {
          const userData = res?.data?.data;

          setFirstName(userData?.first_name || '');
          setLastName(userData?.last_name || '');
          setEmail(userData?.email || '');
          setPhone(userData?.phone || '');
          setLocation(userData?.location || '');
          setGender(userData?.gender || '');
          setProfileImage(userData?.profile_image || null);
        }
      })
      .catch(err => {
        console.log('Data not fetched', err);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    console.log('State updated: ', {
      firstName,
      lastName,
      email,
      phone,
      location,
      gender,
    });
  }, [firstName, lastName, email, phone, location, gender]);

  if (loading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <View style={{flex: 1}}>
      <View style={styles.contanier}>
        <StatusBar
          backgroundColor={colors.background}
          barStyle="dark-content"
        />

        {/* app-title */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: 10}}>
          <View style={styles.appTitleContanier}>
            <AppTitle
              title={I18n.t('your_profile')}
              leftIconPress={handleLeftPress}
            />
          </View>

          {/* account-photo */}
          <View style={styles.accountPhotoContanier}>
            <ImageBackground
              style={styles.elevationBackground}
              resizeMode="contain"
              source={images.accountElevationBackground}>
              <Image
                style={styles.accountPhoto}
                resizeMode="cover"
                // source={images.accountProfilePhoto}
                source={
                  profileImage
                    ? {uri: profileImage}
                    : images.accountProfilePhoto
                }
              />
              <TouchableOpacity onPress={pickImage}>
                <Image
                  style={styles.accountEditIcon}
                  resizeMode="contain"
                  source={images.accountEditIcon}
                />
              </TouchableOpacity>
            </ImageBackground>
          </View>

          {/* first-name */}
          <View style={{...margins.mT10}}>
            <AppInput
              label={I18n.t('first_name')}
              value={firstName}
              onChangeText={text => setFirstName(text)}
            />
          </View>

          {/* last-name */}
          <View style={styles.textfieldContext}>
            <AppInput
              label={I18n.t('last_name')}
              value={lastName}
              onChangeText={text => setLastName(text)}
            />
          </View>

          {/* email */}
          <View style={styles.textfieldContext}>
            <AppInput
              label={I18n.t('email')}
              value={email}
              onChangeText={text => setEmail(text)}
              isEditable={false}
            />
          </View>

          {/* phone-number */}
          <View style={styles.textfieldContext}>
            <PhoneNumberVerify
              label={I18n.t('phone_number')}
              keyboardType="number-pad"
              value={phone}
              onChangeText={text => setPhone(text)}
            />
          </View>

          {/* location */}
          <View style={styles.textfieldContext}>
            <AppInput
              label={I18n.t('locations')}
              leftIcon={<Image source={images.location} />}
              value={location}
              onChangeText={text => setLocation(text)}
            />
          </View>

          {/* gender */}
          <View style={styles.textfieldContext}>
            <DropdownTextfield
              dropdownContanierStyle={styles.countrydropdown}
              label={I18n.t('gender')}
              options={dropdownOption()}
              value={gender}
              onSelect={text => setGender(text)}
              isDropdownOpen={dropdownOpen === 1}
              onDropdownToggle={() => handleDropdownToggle(1)}
            />
          </View>
        </ScrollView>
        <View style={styles.appbtnContanier}>
          <AppButton 
          text={I18n.t('update_profile')} 
          onPressed={handleupdate}  
          loading={submitting}
          disabled={submitting}/>
        </View>
      </View>
      <Toast/>
    </View>
  );
};

export default UpdateProfileScreen;
