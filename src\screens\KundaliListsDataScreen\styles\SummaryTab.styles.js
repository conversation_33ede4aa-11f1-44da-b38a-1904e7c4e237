import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import {fonts, fontSize} from '../../../theme/fonts';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    // borderWidth: 1,
  },
  summaryData: {
    ...margins.mT22,
  },
  titleText: {
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  summarContanier: {
    ...margins.mT14,
    borderRadius: DeviceUiInfo.moderateScale(20),
    backgroundColor: colors.white,
    elevation: 1,
    ...paddings.pV16,
    ...paddings.pH16,
  },
  detailsText: {
    color: colors.poweredByTextColor,
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratMedium,
  },
});

export default styles;
