import {View, Text} from 'react-native';
import React, {useEffect} from 'react';
import styles from '../style/jsonPlaceholderUsers.style';
import Icon from 'react-native-vector-icons/FontAwesome5';


{
  /* ---- This component is not included in app, just for Example usage --- */
}

const JsonPlaceholderUsers = () => {
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Log in</Text>
      <Icon name="facebook" color="red" size={50} />
      <Text>JsonPlaceholderUsers total users </Text>
    </View>
  );
};

export default JsonPlaceholderUsers;
