import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {paddings, margins} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    // ...margins.mB14,
    // ...paddings.pH33
  },
  chartContanier: {
    // borderWidth: 1,
    ...margins.mT22,
  },
  titleText: {
    ...margins.mB14,
    color: colors.primary,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  chartImgStyle: {
    height: DeviceUiInfo.moderateScale(300),
    width: DeviceUiInfo.moderateScale(300),
  },
});

export default styles;
