import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import styles from '../styles/BecomePandits.styles';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import AppInput from '../../../components/common/AppInput';
import PhoneNumberVerify from '../../../components/common/PhoneNumberVerify';
import DropdownTextfield from '../../../components/common/DropdownTextfield';
import { MultiSelect } from 'react-native-element-dropdown';
import DotAppField from '../../../components/common/DotAppField';
import AppButton from '../../../components/common/AppButton';
import AppRoutes from '../../../constants/AppRoutes';
import Apis from '../../../services/apiList';
import {
  GetServices,
  PostServices,
  PostServicesWithoutToken,
} from '../../../services/commonApiMethod';
import { launchImageLibrary } from 'react-native-image-picker';
import metrics from '../../../theme/metrics';
import { customAppEvents } from '../../../analytics/AppEvents';

const BecomePandits = ({ navigation, route }) => {
  const api = new Apis();
  const { margins, paddings } = metrics;
  const { width, height } = Dimensions.get('window');
  const { language } = route?.params;
  const [isDropdownOpen, setIsDropdownOpen] = useState({
    workingArea: false,
    canWorkArea: false,
    pujaCanDo: false,
    howUseWhatsapp: false,
    degree: false,
  });

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    addres1: '',
    country: '',
    state: '',
    city: '',
    email: '',
    phonenumber: '',
    puja: [],
    experience: '',
  });

  const [selected, setSelected] = useState([]);
  const [pujas, setPujas] = useState([]);
  const [degree, setDegree] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const [selectedWhatsApp, setSelectedWhatsApp] = useState('');
  const [selectedDegree, setSelectedDegree] = useState([]);
  const [otherDegree, setOtherDegree] = useState('');
  const [passportImage, setPassportImage] = useState([]);
  const [aadharImage, setAadharImage] = useState([]);
  const [panImage, setPanImage] = useState([]);
  const [imageError, setImageError] = useState(null);

  const [countriesWholeData, setcountriesWholeData] = useState([]);
  const [countries, setCountries] = useState([]);
  const [stateWholeData, setStateWholeData] = useState([]);
  const [state, setState] = useState([]);
  const [cityWholeData, setCityWholeData] = useState([]);
  const [cities, setCities] = useState([]);
  const [workAreas, setWorkAreas] = useState([]);
  const [selectedWorkAreas, setSelectedWorkAreas] = useState([]);
  const [selectedCanWorkAreas, setSelectedCanWorkAreas] = useState([]);
  const [callingCode, setCallingCode] = useState('91');


  const handleDropdownToggle = dropdownId => {
    setIsDropdownOpen(isDropdownOpen === dropdownId ? null : dropdownId);
  };

  useEffect(() => {
    console.log('Checking puja ARRAYYY<<', formData.puja);
  }, [formData.puja]);

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  useEffect(() => {
    fetchCountry();
    fetchPujaCanDoList();
    fetchDegree();
  }, []);

  const fetchCountry = async () => {
    const payload = {
      url: api.country,
    };
    await GetServices(payload)
      .then(res => {
        if (res.success) {

          const countrydata = res?.data;
         
          const data = countrydata.map(country => ({
            id: country.id,
            name: country.name,
          }));

          setcountriesWholeData(countrydata);
          setCountries(data.map(country => country.name));
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleSelectCountry = selectCountryName => {
    const selectedCountry = countriesWholeData.find(
      country => country.name === selectCountryName,
    );
    if (selectedCountry) {
      setFormData(prevState => ({
        ...prevState,
        country: selectedCountry.name,
      }));
      fetchState(selectedCountry.id);
      fetchworkarea(selectedCountry.id);
    } else {
      console.log(`Country ${selectCountryName} Not Found .`);
    }
  };

  const fetchState = async country_id => {
    const payload = {
      url: `${api.state}?country_id=${country_id}`,
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          const stateData = res?.data;
          const stateNames = stateData.map(state => state.text);
          setStateWholeData(stateData);
          setState(stateNames);
          setFormData(prevState => ({
            ...prevState,
            state: '',
            city: '',
          }));
        }
      })
      .catch(err => {
        console.log('error fetching state', err);
      });
  };

  const handleStateSelect = state_name => {
    const selectedState = stateWholeData.find(
      state => state.text === state_name,
    );

    if (selectedState) {
      setFormData(prevData => ({
        ...prevData,
        state: selectedState.text,
      }));

      fetchCity(selectedState.id);
    }
  };

  const fetchCity = async state_id => {
    const payload = {
      url: `${api.city}?state_id=${state_id}`,
    };
    await GetServices(payload)
      .then(res => {
        if (res) {
          const cityData = res?.data;
          const cityNames = cityData.map(city => city.name);
          setCityWholeData(cityData);
          setCities(cityNames);
          setFormData(prevState => ({
            ...prevState,
            city: '',
          }));
        }
      })
      .catch(err => {
        console.log('Error fetching city data', err);
      });
  };

  const fetchPujaCanDoList = async () => {
    const payload = {
      url: `${api.getAllPuja}`,
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          const pujaListCanDo = res?.data;
          const pujaList = pujaListCanDo.map(pujaobj => ({
            label: pujaobj.text,
            value: pujaobj.id
          }));
          setPujas(pujaList);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const fetchDegree = async () => {
    const payload = {
      url: api.degree,
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          const degreeData = res?.data;
          const degreeText = degreeData.map(degree => degree.text);

          let tempdegree = [];
          let tmp = {};
          for (i = 1; i < degreeText.length; i++) {
            tmp = {
              label: degreeText[i],
              value: degreeText[i],
            };
            tempdegree.push(tmp);
          }
          setDegree(tempdegree);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const fetchworkarea = async country_id => {
    var workAreaData = new FormData();
    workAreaData.append('countryID', country_id);

    const payload = {
      url: api.workingarea,
      data: workAreaData,
    };
    await PostServices(payload)
      .then(res => {
        if (res && res.data) {
          const mappedWorkAreas = res.data.map(area => ({
            label: area.text,
            value: area.id,
          }));
          setWorkAreas(mappedWorkAreas);
        } else {
          console.log('No valid data received');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleOtherDegreeChange = text => {
    setOtherDegree(text);
  };

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handleImageSelection = field => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        includeBase64: false,
        maxWidth: 1024,
        maxHeight: 1024,
      },
      response => {
        if (response.didCancel) {
          console.log('User Cancelled Image Picker');
        } else if (response.errorCode) {
          console.log('Image picker Error', response.errorMessage);
        } else {
          const selectedImage = response.assets[0];
          const { fileSize, type, uri } = selectedImage;

          if (
            selectedImage.fileSize <= 5 * 1024 * 1024 &&
            (type === 'image/jpeg' || type === 'image/png')
          ) {
            switch (field) {
              case 'passport':
                setPassportImage(prevImages => [...prevImages, selectedImage]);
                break;
              case 'aadhar':
                setAadharImage(prevImages => [...prevImages, selectedImage]);
                break;
              case 'pan':
                setPanImage(prevImages => [...prevImages, selectedImage]);
                break;
              default:
                break;
            }
            setImageError('');
          } else {
            switch (field) {
              case 'passport':
                setPassportImage([]);
                break;
              case 'aadhar':
                setAadharImage([]);
                break;
              case 'pan':
                setPanImage([]);
                break;
              default:
                break;
            }
            setImageError(
              'Please select the image under 5 MB and in jpeg/png format',
            );
          }
        }
      },
    );
  };

  const removeImage = (index, field) => {
    switch (field) {
      case 'passport':
        setPassportImage(prevImages =>
          prevImages.filter((_, imgIndex) => imgIndex !== index),
        );
        break;
      case 'aadhar':
        setAadharImage(prevImages =>
          prevImages.filter((_, imgIndex) => imgIndex !== index),
        );
        break;
      case 'pan':
        setPanImage(prevImages =>
          prevImages.filter((_, imgIndex) => imgIndex !== index),
        );
        break;
      default:
        break;
    }
  };

  const fetchbecomepandit = async () => {

    const fullphone = `${callingCode}${formData.phonenumber}`
    var data = new FormData();
    data.append('first_name', formData.firstName);
    data.append('last_name', formData.lastName);
    data.append('email', formData.email);
    data.append('phone', fullphone);
    data.append('address_1', formData.addres1);
    data.append('city', formData.city);
    data.append('state', formData.state);
    data.append('country', formData.country);
    data.append('puja', formData.puja);
    data.append('experience', formData.experience);
    data.append('knowlege_of_video_puja', selectedWhatsApp);
    selectedWorkAreas.forEach(workareas => {
      data.append('working_area[]', workareas);
    });
    selectedCanWorkAreas.forEach(canworkareas => {
      data.append('can_work_area[]', canworkareas);
    });

    let finalDegrees = [...selectedDegree];
    if (selectedDegree.includes('Other') && otherDegree.trim() !== '') {
      finalDegrees = finalDegrees.filter(item => item !== 'Other');
      finalDegrees.push(otherDegree.trim());
    }

    finalDegrees.forEach(degree => {
      data.append('any_degrees[]', degree);
    });

    passportImage.forEach((image, i) => {
      data.append('passport_photo', {
        uri: image.uri,
        type: image.type,
        name: `passport_${i}.jpg`,
      });
    });

    aadharImage.forEach((image, i) => {
      data.append('aadhar_card', {
        uri: image.uri,
        type: image.type,
        name: `aadhar_${i}.jpg`,
      });
    });

    panImage.forEach((image, i) => {
      data.append('pan_card', {
        uri: image.uri,
        type: image.type,
        name: `pan_${i}.jpg`,
      });
    });

    const payload = {
      url: api.becomepandit,
      data: data,
      type: 'formData',
    };

    await PostServicesWithoutToken(payload)
      .then(async (res) => {
        setIsLoading(false);
        if (res) {
          await customAppEvents("submit_as_pandit");
          Alert.alert(
            'Thank you for contacting us!',
            'We will get in touch with you shortly.',
          );
          navigation.replace(AppRoutes.BOTTOM_NAV_BAR);
        }
      })
      .catch(err => {
        console.log(err);
        setIsLoading(false);

      });
  };

  const handleSubmit = async () => {

    const isAllFieldsEmpty =
      (!formData.firstName || formData.firstName.trim() === '') &&
      (!formData.lastName || formData.lastName.trim() === '') &&
      (!formData.email || formData.email.trim() === '') &&
      (!formData.phonenumber || formData.phonenumber.trim() === '') &&
      (!formData.addres1 || formData.addres1.trim() === '') &&
      (!formData.city || formData.city.trim() === '') &&
      (!formData.state || formData.state.trim() === '') &&
      (!formData.country || formData.country.trim() === '') &&
      (!formData.puja || formData.puja.length === 0) &&
      (!formData.experience || formData.experience.trim() === '') &&
      selectedWorkAreas.length === 0 &&
      selectedCanWorkAreas.length === 0 &&
      selectedDegree.length === 0 &&
      (!otherDegree || otherDegree.trim() === '') &&
      passportImage.length === 0 &&
      aadharImage.length === 0 &&
      panImage.length === 0;

    if (isAllFieldsEmpty) {
      Alert.alert('Error', 'All fields are required.');
      return;
    }


    let isValid = true;
    const errors = [];

    if (!formData.firstName || formData.firstName.trim().length === 0) {
      // Alert.alert('Error', 'First name cannot be empty.');
      errors.push('First Name');
      isValid = false;
    }

    if (!formData.lastName || formData.lastName.trim().length === 0) {
      // Alert.alert('Error', 'Last name cannot be empty.');
      errors.push('Last Name');
      isValid = false;
    }

    const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!formData.email || formData.email.trim().length === 0 || !emailPattern.test(formData.email)) {
      // Alert.alert('Error', 'Please enter a valid email.');
      errors.push('Valid Email');
      isValid = false;
    }

    const phonePattern = /^[0-9]{7,15}$/;
    if (!formData.phonenumber || formData.phonenumber.trim().length === 0 || !phonePattern.test(formData.phonenumber)) {
      // Alert.alert('Error', 'Please enter a valid 7-15 digit phone number.');
      errors.push('Valid 7-15 digit PhoneNumber');
      isValid = false;
    }

    if (!formData.addres1 || formData.addres1.trim().length === 0) {
      // Alert.alert('Error', 'Address cannot be empty.');
      errors.push('Address');
      isValid = false;
    }

    if (!formData.city || formData.city.trim().length === 0) {
      // Alert.alert('Error', 'City cannot be empty.');
      errors.push('City');
      isValid = false;
    }

    if (!formData.state || formData.state.trim().length === 0) {
      // Alert.alert('Error', 'State cannot be empty.');
      errors.push('State');
      isValid = false;
    }

    if (!formData.country || formData.country.trim().length === 0) {
      // Alert.alert('Error', 'Country cannot be empty.');
      errors.push('Country');
      isValid = false;
    }

    if (formData.puja.length === 0) {
      // Alert.alert('Error', 'Puja cannot be empty');
      errors.push('Puja');
      isValid = false;
    }

    if (!formData.experience || formData.experience.trim().length === 0) {
      // Alert.alert('Error', 'Experience cannot be empty.');
      errors.push('Experience');
      isValid = false;
    }

    if (selectedWorkAreas.length === 0) {
      // Alert.alert('Error', 'Please select at least one working area.');
      errors.push('Working Area');
      isValid = false;
    }

    if (selectedCanWorkAreas.length === 0) {
      // Alert.alert('Error', 'Please select at least one can work area.');
      errors.push('Can Work Area');
      isValid = false;
    }

    if (selectedDegree.length === 0) {
      // Alert.alert('Error', 'Please select at least one degree.');
      errors.push('Degree');
      isValid = false;
    }

    if (selectedDegree.includes('Other') && !otherDegree.trim()) {
      // Alert.alert('Error', 'Please specify your degree.');
      errors.push('Other Degree');
      isValid = false;
    }

    if (passportImage.length === 0) {
      // Alert.alert('Error', 'Please upload your passport photo.');
      errors.push('Passport Photo');
      isValid = false;
    }

    if (aadharImage.length === 0) {
      // Alert.alert('Error', 'Please upload your Aadhar card.');
      errors.push('Aadhar Card');
      isValid = false;
    }

    if (panImage.length === 0) {
      // Alert.alert('Error', 'Please upload your PAN card.');
      errors.push('PAN Card');
      isValid = false;
    }

    if (!isValid) {
      if (errors.length <= 3) {
        Alert.alert('Please review required fields', errors.join(', '));
      } else {
        Alert.alert('Note', 'Multiple required fields are missing.');
      }
      return;
    }

    if (isValid) {
      console.log("Validation Passed. Proceeding with API call...");

      setIsLoading(true);
      fetchbecomepandit();
    }
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('become_a_pandit')}
          leftIconPress={handleLeftPress}
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* first-name */}
        <AppInput
          label={I18n.t('first_name')}
          requirement={true}
          placeholderText={I18n.t('first_name')}
          placeholderColor={colors.arrowDown}
          value={formData.firstName}
          onChangeText={text => handleInputChange('firstName', text)}
        />

        {/* last-name */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('last_name')}
            placeholderText={I18n.t('last_name')}
            placeholderColor={colors.arrowDown}
            value={formData.lastName}
            onChangeText={text => handleInputChange('lastName', text)}
            requirement={true}
          />
        </View>

        {/* address-1 */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={`${I18n.t('address')} 1`}
            requirement={true}
            placeholderText={`${I18n.t('address')} 1`}
            placeholderColor={colors.arrowDown}
            value={formData.addres1}
            onChangeText={text => handleInputChange('addres1', text)}
          />
        </View>

        <View style={{ ...margins.mT16 }}>
          {/* Country */}
          <View style={{ marginBottom: 16 }}>
            <View
              style={[
                styles.labelContanier,
                { left: language === 'hi' ? width * 0.05 : width * 0.13 },
              ]}>
              {<Text style={styles.requirementText}>*</Text>}
            </View>
            <DropdownTextfield
              label={I18n.t('country')}
              options={countries}
              value={formData.country}
              onSelect={value => handleSelectCountry(value)}
              dropdownContanierStyle={styles.countrydropdown}
              isDropdownOpen={isDropdownOpen === 1}
              onDropdownToggle={() => handleDropdownToggle(1)}
            />
          </View>

          {/* State */}
          <View style={{ marginBottom: 16 }}>
            <View
              style={[
                styles.labelContanier,
                { left: language === 'hi' ? width * 0.065 : width * 0.09 },
              ]}>
              {<Text style={styles.requirementText}>*</Text>}
            </View>
            <DropdownTextfield
              label={I18n.t('state')}
              options={state}
              // value={
              //   stateWholeData.find(state => state.id === formData.state)
              //     ?.text || ''
              // }
              value={formData.state}
              onSelect={value => handleStateSelect(value)}
              dropdownContanierStyle={styles.countrydropdown}
              isDropdownOpen={isDropdownOpen === 2}
              onDropdownToggle={() => handleDropdownToggle(2)}
            />
          </View>

          {/* City */}
          <View>
            <View
              style={[
                styles.labelContanier,
                { left: language === 'hi' ? width * 0.065 : width * 0.07 },
              ]}>
              {<Text style={styles.requirementText}>*</Text>}
            </View>
            <DropdownTextfield
              label={I18n.t('city')}
              options={cities}
              value={formData.city}
              onSelect={value => handleInputChange('city', value)}
              dropdownContanierStyle={styles.countrydropdown}
              isDropdownOpen={isDropdownOpen === 3}
              onDropdownToggle={() => handleDropdownToggle(3)}
            />
          </View>
        </View>

        {/* email */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('email')}
            requirement={true}
            placeholderText={I18n.t('email')}
            placeholderColor={colors.arrowDown}
            value={formData.email}
            onChangeText={text => handleInputChange('email', text)}
          />
        </View>

        {/* phone-number */}
        <View style={styles.textfieldContent}>
          <PhoneNumberVerify
            label={I18n.t('phone_number')}
            requirement={true}
            keyboardType="number-pad"
            value={formData.phonenumber}
            onChangeText={text => handleInputChange('phonenumber', text)}
            onCountryCodeChange={(code) => {
              setCallingCode(code)
            }}
          />
        </View>

        {/* working_area */}
        <View style={styles.textfieldContent}>
          <Text style={styles.labelStyle}>{I18n.t('working_area')}</Text>

          <View
            style={[
              styles.labelContanier,
              { left: language === 'hi' ? width * 0.22 : width * 0.23 },
            ]}>
            <Text style={styles.requirementText}>*</Text>
          </View>

          <MultiSelect
            style={styles.dropdown}
            search
            data={workAreas}
            labelField="label"
            valueField="value"
            placeholder={I18n.t('select_cities')}
            searchPlaceholder="Searching..."
            value={selectedWorkAreas}
            onChange={item => {
              setSelectedWorkAreas(item);
            }}
          // flatListProps={{
          //   showsVerticalScrollIndicator: true, // Show scroll indicator
          //   scrollEnabled: true, // Enable scroll
          // }}
          />

          {/* <DropdownTextfield
            label={`${I18n.t('working_area')}`}
            requirement={true}
            placeholderText={'First Choice'}
            placeholderColor={colors.arrowDown}
            isDropdownOpen={isDropdownOpen}
            onDropdownToggle={handleDropdownToggle}
            // options={isLoading ? ['Loading...'] : workingAreas.map(item => item.text)}
            options={
              isLoading ? ['Loading...'] : workingAreas.map(item => item.text)
            }
            value={workingAreas} 
            onSelect={value => setWorkingAreas(value)}
          /> */}
        </View>

        {/* can_work_area */}
        <View style={styles.textfieldContent}>
          <Text style={styles.labelStyle}>{I18n.t('can_work_area')}</Text>

          <View
            style={[
              styles.labelContanier,
              { left: language === 'hi' ? width * 0.22 : width * 0.245 },
            ]}>
            <Text style={styles.requirementText}>*</Text>
          </View>

          <MultiSelect
            style={styles.dropdown}
            search
            data={workAreas}
            labelField="label"
            valueField="value"
            placeholder={I18n.t('select_cities')}
            searchPlaceholder="Searching..."
            value={selectedCanWorkAreas}
            onChange={item => {
              setSelectedCanWorkAreas(item);
            }}
          />
        </View>

        {/* puja_can_do */}
        <View style={styles.textfieldContent}>
          {/* ------------------------------------------------ */}
          {/* single value dropdown component removed */}
          {/* <DropdownTextfield
            label={`${I18n.t('puja_can_do')}`}
            requirement={true}
            placeholderText={'Please Enter puja'}
            placeholderColor={colors.arrowDown}
            isDropdownOpen={isDropdownOpen === 6}
            onDropdownToggle={() => handleDropdownToggle(6)}
            dropdownContanierStyle={styles.countrydropdown}
            options={pujas}
            value={form}
            // onSelect={value => }
          /> */}
          {/* ------------------------------------------------ */}
          <Text style={styles.labelStyle}>{I18n.t('puja_can_do')}</Text>
          <View
            style={[
              styles.labelContanier,
              { left: language === 'hi' ? width * 0.22 : width * 0.225 },
            ]}>
            {<Text style={styles.requirementText}>*</Text>}
          </View>
          <MultiSelect
            style={styles.dropdown}
            search
            data={pujas}
            labelField="label"
            valueField="value"
            placeholder={I18n.t('puja_can_do')}
            searchPlaceholder="Search pujas"
            value={selected}
            onChange={item => {
              setSelected(item);
              setFormData(prevData => ({
                ...prevData,
                puja: item,
              }));
            }}
          />
        </View>
        {/* </View> */}

        {/* Know how to use Whatsapp video call/Google meet ? */}
        <View style={[styles.textfieldContent, { ...margins.mB10 }]}>
          <View
            style={[
              styles.labelContanier,
              { left: language === 'hi' ? width * 0.64 : width * 0.85 },
            ]}>
            {<Text style={styles.requirementText}>*</Text>}
          </View>
          <DropdownTextfield
            label={`${I18n.t('how_use_whatsapp_call_google_meet')}`}
            placeholderText={'Yes'}
            placeholderColor={colors.arrowDown}
            isDropdownOpen={isDropdownOpen === 7}
            onDropdownToggle={() => handleDropdownToggle(7)}
            dropdownContanierStyle={styles.countrydropdown}
            options={['Yes', 'No']}
            value={selectedWhatsApp}
            onSelect={value => setSelectedWhatsApp(value)}
          />
        </View>

        {/* Any Degree/Padvi ?  */}
        {/* <View style={styles.textfieldContent}> */}
        <View>
          <Text style={styles.labelStyle}>{I18n.t('any_degree_padvi')}</Text>
          <View
            style={[
              styles.labelContanier,
              { left: language === 'hi' ? width * 0.2 : width * 0.31 },
            ]}>
            {<Text style={styles.requirementText}>*</Text>}
          </View>
          {/* <DropdownTextfield
            // label={I18n.t('any_degree_padvi')}
            // placeholderText={'Select Degree'}
            isDropdownOpen={isDropdownOpen === 8}
            onDropdownToggle={() => handleDropdownToggle(8)}
            dropdownContanierStyle={styles.countrydropdown}
            options={[...degree]}
            value={selectedDegree}
            onSelect={handleDegreeSelect}
            multiSelect={true}
            /> */}

          <MultiSelect
            style={styles.dropdown}
            search
            data={degree}
            labelField="label"
            valueField="value"
            placeholder={I18n.t('any_degree_padvi')}
            searchPlaceholder="Search Degrees"
            value={selectedDegree}
            onChange={item => {
              setSelectedDegree(item);
            }}
            multiSelect={true}
          />
        </View>

        {/* {selectedDegree === 'Other' && ( */}
        {selectedDegree.includes('Other') && (
          <AppInput
            label="Please Specify Your Degree"
            placeholderText="Enter Degree"
            value={otherDegree}
            onChangeText={handleOtherDegreeChange}
            containerStyle={{ ...margins.mT10 }}
          />
        )}
        {/* </View> */}

        {/* Total Experience ?  */}
        <View style={styles.textfieldContent}>
          <AppInput
            label={I18n.t('total_experience')}
            requirement={true}
            placeholderText={'Please Enter Your Experience'}
            placeholderColor={colors.arrowDown}
            keyboardType="numeric"
            value={formData.experience}
            onChangeText={text => handleInputChange('experience', text)}
          />
        </View>

        {/* Passport size photo * */}
        <View style={styles.textfieldContent}>
          <DotAppField
            label={I18n.t('passport_size')}
            requirement={true}
            placeholderText={'Choose File'}
            placeholderColor={colors.arrowDown}
            maxSize={'5 MB'}
            supportFormat={'JPEG, PNG'}
            isEditable={false}
            onPress={() => handleImageSelection('passport')}
          />

          {/* {passportImage.length > 0 &&
            passportImage.map((img, index) => (
              <Image
                key={index}
                source={{uri: img.uri}}
                style={{width: 100, height: 100, marginVertical: 5,flexDirection:'row'}}
              />
            ))} */}

          {/* Display selected images horizontally */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
          // style={{marginVertical: 10}}
          >
            {passportImage.length > 0 &&
              passportImage.map((img, index) => (
                <View
                  key={index}
                  style={{ marginRight: 10, position: 'relative' }}>
                  <Image
                    source={{ uri: img.uri }}
                    style={{ width: 100, height: 100, borderRadius: 5 }}
                  />
                  <TouchableOpacity
                    onPress={() => removeImage(index, 'passport')}
                    style={styles.cancelbtn}>
                    <Text
                      style={{ color: 'white', fontWeight: 'bold', bottom: 2 }}>
                      X
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
          </ScrollView>
        </View>

        {/* Aadhar Card  */}
        <View style={styles.textfieldContent}>
          <DotAppField
            label={I18n.t('aadhar_card')}
            requirement={true}
            placeholderText={'Choose File'}
            placeholderColor={colors.arrowDown}
            maxSize={'5 MB'}
            supportFormat={'JPEG, PNG'}
            isEditable={false}
            onPress={() => handleImageSelection('aadhar')}
          />
          {/* Display selected images horizontally */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
          // style={{marginVertical: 10}}
          >
            {aadharImage.length > 0 &&
              aadharImage.map((img, index) => (
                <View
                  key={index}
                  style={{ marginRight: 10, position: 'relative' }}>
                  <Image
                    source={{ uri: img.uri }}
                    style={{ width: 100, height: 100, borderRadius: 5 }}
                  />
                  <TouchableOpacity
                    onPress={() => removeImage(index, 'aadhar')}
                    style={styles.cancelbtn}>
                    <Text
                      style={{ color: 'white', fontWeight: 'bold', bottom: 2 }}>
                      X
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
          </ScrollView>
        </View>

        {/* Pan Card  */}
        <View style={styles.textfieldContent}>
          <DotAppField
            label={I18n.t('pan_card')}
            requirement={true}
            placeholderText={'Choose File'}
            placeholderColor={colors.arrowDown}
            maxSize={'5 MB'}
            supportFormat={'JPEG, PNG'}
            isEditable={false}
            onPress={() => handleImageSelection('pan')}
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
          // style={{marginVertical: 10}}
          >
            {panImage.length > 0 &&
              panImage.map((img, index) => (
                <View
                  key={index}
                  style={{ marginRight: 10, position: 'relative' }}>
                  <Image
                    source={{ uri: img.uri }}
                    style={{ width: 100, height: 100, borderRadius: 5 }}
                  />
                  <TouchableOpacity
                    onPress={() => removeImage(index, 'pan')}
                    style={styles.cancelbtn}>
                    <Text
                      style={{ color: 'white', fontWeight: 'bold', bottom: 2 }}>
                      X
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
          </ScrollView>
        </View>

        {/* Error message */}
        {imageError ? <Text style={{ color: 'red' }}>{imageError}</Text> : null}

        {/* app-button */}
        <View style={styles.appbtnContanier}>
          {
            isLoading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <AppButton text={I18n.t('submit')} onPressed={handleSubmit} />
            )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default BecomePandits;
