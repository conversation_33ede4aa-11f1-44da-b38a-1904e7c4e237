import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';
import { Dimensions } from 'react-native';

const {margins, paddings} = metrics;
const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  apptitleContanier: {
    ...margins.mT16,
    ...margins.mB24,
  },
  aboutTitleContanier: {
    // borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'center',
  },
  IconContanier: {
    alignItems: 'center',
    justifyContent: 'center',
    // borderWidth: 1,
    height: DeviceUiInfo.moderateScale(121),
    width: DeviceUiInfo.moderateScale(121),
    borderRadius: DeviceUiInfo.moderateScale(18),
    backgroundColor: colors.white,
    elevation: 4,
    zIndex:1
  },
  rashiIcon: {
    height: DeviceUiInfo.moderateScale(80),
    width: DeviceUiInfo.moderateScale(90),
  },
  TextContanier: {
    ...margins.mL20,
    justifyContent: 'space-between',
    flexDirection: 'column',
    // borderWidth: 1,
    height: DeviceUiInfo.moderateScale(99),
    width: DeviceUiInfo.moderateScale(182),
  },
  displaydatecal: {
    flexDirection: 'row',
    zIndex:1
  },
  iconImageStylecal: {
    height: DeviceUiInfo.moderateScale(20),
    width: DeviceUiInfo.moderateScale(26),
    ...margins.mL12
  },
  aboutText1: {
    color: colors.lightOrange,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
  },
  engaboutText1:{
    color: colors.lightOrange,
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratSemibold,
    width: width * 0.40
  },
  aboutText2: {
    fontFamily: fonts.MontserratSemibold,
    color: colors.dividerBlack,
    fontSize: fontSize.f18,
  },
  aboutText3: {
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
    fontSize: fontSize.f20,
  },
  divider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV20,
    zIndex:1
  },
  titleStyle: {
    fontSize: fontSize.f26,
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
    fontWeight: 'bold',
  },
  titleDetailsStyle: {
    fontSize: fontSize.f16,
    fontFamily: fonts.MontserratMedium,
    color: colors.poweredByTextColor,
  },
});

export default styles;
