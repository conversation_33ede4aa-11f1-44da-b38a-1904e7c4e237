import React from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  FlatList,
  TouchableOpacity,
  Image,
  Linking,
  Alert,
} from 'react-native';
import styles from '../styles/ContactUs.style';
import {colors} from '../../../theme/colors';
import I18n from '../../../utils/language/i18nextConfig';
import AppTitle from '../../../components/common/AppTitle';
import {images} from '../../../theme/images';

const ContactUs = ({navigation}) => {
  const contactData = [
    {
      id: 1,
      name: I18n.t('phone_call'),
      subtitle: I18n.t('phone_call_text'),
      contactLink: '(+91) ************',
      type: 'phone',
    },
    {
      id: 2,
      name: I18n.t('email'),
      subtitle: I18n.t('email_text'),
      contactLink: '<EMAIL>',
      type: 'email',
    },
    {
      id: 3,
      name: I18n.t('whatsapp_chat'),
      subtitle: I18n.t('whatsapp_text'),
      contactLink: '(+91) ************',
      type: 'whatsapp',
    },
    {
      id: 4,
      name: I18n.t('facebook'),
      subtitle: I18n.t('fb_text'),
      contactLink: 'https://www.facebook.com/panditsnearme',
      type: 'facebook',
    },
    {
      id: 5,
      name: I18n.t('instagram'),
      subtitle: I18n.t('ig_text'),
      contactLink: 'https://www.instagram.com/panditsnearme',
      type: 'instagram',
    },
    {
      id: 6,
      name: I18n.t('x'),
      subtitle: I18n.t('twitter_text'),
      contactLink: 'https://x.com/panditsnearme',
      type: 'x',
    },
    {
      id: 7,
      name: I18n.t('linked_in'),
      subtitle: I18n.t('linkedin_text'),
      contactLink: 'https://www.linkedin.com/company/pandits-near-me',
      type: 'linked_in',
    },
  ];

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handleContactPress = item => {
    const {type, contactLink} = item;

    if (type === 'phone') {
      const phoneNumber = `tel:${contactLink.replace(/\D/g, '')}`;
      Linking.openURL(phoneNumber);
    } else if (type === 'email') {
      const emailUrl = `mailto:${contactLink}`;
      Linking.openURL(emailUrl);
    } else if (type === 'whatsapp') {
      const whatsappUrl = `whatsapp://send?phone=${contactLink.replace(
        /\D/g,
        '',
      )}`;
      Linking.openURL(whatsappUrl).catch(() => {
        Alert.alert('Note', 'Please Install whatsapp to chat.');
      });
    } else if (type === 'facebook') {
      const facebookURL = `fb://profile/${contactLink.split('/').pop()}`;
      Linking.openURL(facebookURL).catch(() => Linking.openURL(contactLink));
    } else if (type === 'instagram') {
      const instagramUrl = `instagram://user?username=${contactLink
        .split('/')
        .pop()}`;
      Linking.openURL(instagramUrl).catch(() => Linking.openURL(contactLink));
    } else if (type === 'x') {
      const twitterUrl = `twitter://user?screen_name=${contactLink
        .split('/')
        .pop()}`;
      Linking.openURL(twitterUrl).catch(() => Linking.openURL(contactLink));
    } else if (type === 'linked_in') {
      const linkedinUrl = `linkedin://profile/${contactLink.split('/').pop()}`;
      Linking.openURL(linkedinUrl).catch(() => Linking.openURL(contactLink));
    }
  };

  const renderContact = ({item, index}) => {
    return (
      <TouchableOpacity
        onPress={() => handleContactPress(item)}
        style={styles.renderItemContanier}>
        <View style={styles.TextContanier}>
          <Text style={styles.renderNameText}>{item.name}</Text>
          <Text style={styles.renderSubText}>{item.subtitle}</Text>
          <Text style={styles.renderContactText}>{item.contactLink}</Text>
        </View>
        <View style={styles.renderArrowContanier}>
          <Image
            resizeMode="contain"
            source={images.arrowDown}
            style={styles.renderArrow}
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle
          title={I18n.t('contact_us')}
          leftIconPress={handleLeftPress}
        />
      </View>

      {/* how-we-can-help */}
      <View style={styles.howCanHelpContanier}>
        <Text style={styles.howCanHelpTitleText}>
          {I18n.t('tell_us_how_we_can_help')}
        </Text>
        <Text style={styles.howCanHelpSubtitleText}>
          {I18n.t('contact_us_line')}
        </Text>
      </View>

      <FlatList
        data={contactData}
        renderItem={renderContact}
        keyExtractor={(item, index) => index}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default ContactUs;
