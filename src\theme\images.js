// All images are go here

//Icons imports
// import ArrowDown from '../assets/images/icons/arrowDown.svg';

const images = {
  //images
  // splashscreen_image1: require('../assets/images/Layer_1.png'),
  // splashscreen_image2: require('../assets/images/Layer_2.png'),
  // om: require('../assets/images/om.gif'),



  //gif
  splashScreenOMGif: require('../assets/images/gif/splashscreen_om_json.gif'),

  //json-file
  splashScreenOM: require('../assets/images/json_files/splashscreen_om_json.json'),

  //icons
  Blog:require('../assets/images/icons/Blog.png'),
  location: require('../assets/images/icons/location.png'),
  homeSelectedIcon: require('../assets/images/icons/homeSelected.png'),
  homeUnselectedIcon: require('../assets/images/icons/homeUnselected.png'),
  profileUnselectedIcon: require('../assets/images/icons/profileUnselected.png'),
  profileselectedIcon: require('../assets/images/icons/profileSelected.png'),
  bookPandits: require('../assets/images/icons/bookPandits.png'),
  LanguageSelectIcon: require('../assets/images/icons/language_select_icon.png'),
  SearchIcon: require('../assets/images/icons/search_icon.png'),
  NotificationBell: require('../assets/images/icons/notification_bell.png'),
  Horoscope: require('../assets/images/icons/horoscope.png'),
  Puja: require('../assets/images/icons/puja.png'),
  Rituals: require('../assets/images/icons/rituals.png'),
  Homa: require('../assets/images/icons/homa.png'),
  Kundali: require('../assets/images/icons/kundali.png'),
  serviceimgRightArrow: require('../assets/images/icons/serviceimg_right_arrow.png'),
  whatsappIcon: require('../assets/images/icons/whatsapp_icon.png'),
  backArrow: require('../assets/images/icons/back_arrow.png'),
  starRating: require('../assets/images/icons/star_rating.png'),
  arrowDown: require('../assets/images/icons/arrow_down.png'),
  searchAudio: require('../assets/images/icons/search-audio.png'),
  locationBlue: require('../assets/images/icons/locationBlue.png'),
  calendarBlue: require('../assets/images/icons/calendarBlue.png'),
  sunToggle: require('../assets/images/icons/sunToggle.png'),
  moonToggle: require('../assets/images/icons/moonToggle.png'),
  surya: require('../assets/images/icons/surya.png'),
  prevDay: require('../assets/images/icons/prevDay.png'),
  nextDay: require('../assets/images/icons/nextDay.png'),
  LanguageSelectIconGrey: require('../assets/images/icons/language_select_icon_grey.png'),
  Calendar: require('../assets/images/app_images/Calendar.png'),
  locationyellow: require('../assets/images/app_images/locationyellow.png'),
  CalendarIconGrey: require('../assets/images/icons/calendar_icon_grey.png'),
  rahuKalaIcon: require('../assets/images/icons/rahu_kala_icon.png'),
  inauspiciousColor: require('../assets/images/icons/inauspicious_color.png'),
  normalColor: require('../assets/images/icons/normal_color.png'),
  auspiciousColor: require('../assets/images/icons/auspicious_color.png'),
  accountEditIcon: require('../assets/images/icons/account_edit_icon.png'),
  myPujaIcon: require('../assets/images/icons/my_puja_icon.png'),
  privacyPolicyIcon: require('../assets/images/icons/privacy_policy_icon.png'),
  termsConditionIcon: require('../assets/images/icons/terms_condition_icon.png'),
  refundPolicyIcon: require('../assets/images/icons/refund_policy_icon.png'),
  shareAppIcon: require('../assets/images/icons/share_app_icon.png'),
  accountLogOutIcon: require('../assets/images/icons/account_log_out_icon.png'),
  rateUsIcon: require('../assets/images/icons/rate_us_icon.png'),
  contanctUsIcon: require('../assets/images/icons/contact_us_icon.png'),
  aboutUs: require('../assets/images/icons/about_as.png'),
  facebookIcon: require('../assets/images/icons/facebook_icon.png'),
  twitterIcon: require('../assets/images/icons/twitter_icon.png'),
  instagramIcon: require('../assets/images/icons/instagram_icon.png'),
  linkedinIcon: require('../assets/images/icons/linkedin_icon.png'),
  languageNonselectTick: require('../assets/images/icons/language_nonselect_tick.png'),
  languageSelectTick: require('../assets/images/icons/language_select_tick.png'),
  savedKundaliAvtarIcon: require('../assets/images/icons/saved_kundali_avtar_icon.png'),
  threeDotIcon: require('../assets/images/icons/three_dot_icon.png'),
  viewBookPujaListIcon: require('../assets/images/icons/view_book_puja_list_icon.png'),
  squareIcon: require('../assets/images/icons/square_icon.png'),
  chooseFilePin: require('../assets/images/icons/choose_file_pin.png'),
  editIcon: require('../assets/images/icons/edit_icon.png'),
  downloadIcon: require('../assets/images/icons/download_icon.png'),
  backArrowBlack: require('../assets/images/icons/back_arrow_black.png'),
  right_arrow_black : require('../assets/images/icons/right_arrow_black.png'),
  right : require('../assets/images/icons/right.png'),
  left : require('../assets/images/icons/left.png'),
  login : require('../assets/images/icons/login.png'),
  register : require('../assets/images/icons/register.png'),
  close : require('../assets/images/icons/close.png'),

  //app_images
  ic_launcher: require('../assets/images/app_images/ic_launcher.png'),
  congratulationThumb: require('../assets/images/app_images/congratulation_thumb.png'),
  getYourKundaliBG: require('../assets/images/app_images/get_kundali_bg.png'),
  ritualsKaalSarpDosh: require('../assets/images/app_images/rituals_kaal_sarp_dosh.png'),
  pitruPakshaShradhPuja: require('../assets/images/app_images/pitru_paksha_shradh_puja.png'),
  navachandiHoma: require('../assets/images/app_images/navachandi_homa.png'),
  chandiHoma: require('../assets/images/app_images/chandi_homa.png'),
  pujaKaalSarpDosh: require('../assets/images/app_images/puja_kaal_sarp_dosh.png'),
  rudrabhishekPuja: require('../assets/images/app_images/rudrabhishek_puja.png'),
  becomePanditBG: require('../assets/images/app_images/become_panditBG.png'),
  JoinAsPanditBanner: require('../assets/images/app_images/JoinAsPanditBanner.png'),
  splashBackgroundDesign: require('../assets/images/app_images/splash_bg_design.png'),
  MeshImage: require('../assets/images/app_images/mesh.png'),
  VrushbhImage: require('../assets/images/app_images/vrushbh.png'),
  ShinhImage: require('../assets/images/app_images/shinh.png'),
  MithunImage: require('../assets/images/app_images/mithun.png'),
  KarkImage: require('../assets/images/app_images/kark.png'),
  KaniyaImage: require('../assets/images/app_images/kaniya.png'),
  TulaImage: require('../assets/images/app_images/tula.png'),
  VrushikImage: require('../assets/images/app_images/vrushik.png'),
  DhanuImage: require('../assets/images/app_images/dhanu.png'),
  MakarImage: require('../assets/images/app_images/makar.png'),
  KumbhImage: require('../assets/images/app_images/kumbh.png'),
  MithImage: require('../assets/images/app_images/mith.png'),
  splashBackgroundTopBottom: require('../assets/images/app_images/splash_background_design_topandbottom.png'),
  accountElevationBackground: require('../assets/images/app_images/account_elevation_background.png'),
  accountProfilePhoto: require('../assets/images/app_images/account_profile_photo.png'),
  aboutusPandits: require('../assets/images/app_images/aboutus_pandits.png'),
  navamanshaChart: require('../assets/images/app_images/navamansha_chart.png'),
  laganChart: require('../assets/images/app_images/lagan_chart.png'),
  muhurat_clock: require('../assets/images/app_images/muhurat_clock.png'),
  demon: require('../assets/images/app_images/demon_1.png'),
  aboutimg1: require('../assets/images/app_images/aboutimg1.png'),
  aboutimg2: require('../assets/images/app_images/aboutimg2.png'),
  aboutimg3: require('../assets/images/app_images/aboutimg3.png'),
  panchangbg: require('../assets/images/app_images/panchangbg.png'),
  panchanghome : require('../assets/images/app_images/panchanghome.png'),
  indiaFlag : require('../assets/images/app_images/indiaFlag.png'),
  EnglishFlag : require('../assets/images/app_images/EnglishFlag.png'),


    //hero
  hero: require('../assets/images/app_images/hero.mp4'),

  //logos
  logixLogo: require('../assets/images/logos/logix_logo.png'),
};

export {
  images,

  //Icons
  // ArrowDown,
};
