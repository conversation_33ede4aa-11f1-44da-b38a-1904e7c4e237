import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
  },
  aboutusPandits: {
    ...margins.mT24,
    height: DeviceUiInfo.moderateScale(154),
    width: DeviceUiInfo.moderateScale(135),
    alignSelf: 'center',
  },
  version: {
    ...margins.mT17,
    ...margins.mB18,
    alignSelf: 'center',
    fontSize: fontSize.f14,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  renderItemContanier: {
    ...paddings.pH16,
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
    ...margins.mB18,
    height: DeviceUiInfo.moderateScale(58),
    backgroundColor: colors.white,
    elevation: 1,
    borderRadius: DeviceUiInfo.moderateScale(100),
  },
  renderItemImage: {
    ...margins.mR15,
    height: DeviceUiInfo.moderateScale(26),
    width: DeviceUiInfo.moderateScale(26),
  },
  renderItemText: {
    fontSize: fontSize.f16,
    color: colors.primary,
    fontFamily: fonts.MontserratMedium,
  },
  renderItemArrowContanier: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  renderItemArrow: {
    height: DeviceUiInfo.moderateScale(18),
    width: DeviceUiInfo.moderateScale(18),
    transform: [{rotate: '270deg'}],
  },
  poweredContanier: {
    // borderWidth: 1,
    ...margins.mB50,
    flexDirection: 'column',
  },
  titleContanier: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  LineDimondContanier: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  line: {
    width: DeviceUiInfo.moderateScale(50),
    backgroundColor: colors.primary,
    height: 3,
  },
  Diamond: {
    width: DeviceUiInfo.moderateScale(12),
    height: DeviceUiInfo.moderateScale(12),
    backgroundColor: colors.primary,
    transform: [{rotate: '45deg'}],
  },
  textTitleContanier: {
    width: '40%',
    ...margins.mH15,
  },
  titleText: {
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    textAlign: 'center',
  },
  logixLogo: {
    ...margins.mT16,
    alignSelf: 'center',
    height: DeviceUiInfo.moderateScale(56),
    width: DeviceUiInfo.moderateScale(223),
  },
  headtext:{
    fontFamily:fonts.MontserratMedium,
    color:colors.dividerBlack,
    fontSize: fontSize.f16
  },
  headsubtext:{
    fontFamily:fonts.MontserratRegular,
    color:colors.poweredByTextColor,
    fontSize:fontSize.f14,
    ...margins.mT6,
    ...margins.mB5
  },
  aboutbox: {
    flexDirection: 'column',
    padding: 10,
    borderRadius: 10,
    position: 'relative', 
    ...margins.mB20
  },
  backgroundContainer: {
    position: 'absolute', 
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.aboutusbox, 
    borderRadius: DeviceUiInfo.moderateScale(15), 
  },
  firstline:{
    flexDirection: 'row',
    gap:7,
    ...margins.mB10,
  },
  firstlinetext:{
    fontFamily:fonts.MontserratSemibold,
    fontSize:fontSize.f14,
    alignSelf:'center',
    color:colors.primary
  },
  secondtext:{
    fontFamily:fonts.MontserratRegular,
    fontSize:fontSize.f14,
    color:colors.poweredByTextColor,
  },
  missioncontainer:{
    ...margins.mB20
  },
  mission:{
    fontFamily:fonts.MontserratSemibold,
    fontSize:fontSize.f16,
    color:colors.primary,
    ...margins.mB5
  },
  missionsubtext:{
    fontFamily:fonts.MontserratMedium,
    fontSize: fontSize.f14,
    color:colors.poweredByTextColor
  }
});

export default styles;
