import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  ImageBackground,
  Image,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  useWindowDimensions,
  Linking,
} from 'react-native';
import styles from '../styles/ServiceDetailsScreen.style';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import { images } from '../../../theme/images';
import AppButton from '../../../components/common/AppButton';
import AppRoutes from '../../../constants/AppRoutes';
import { GetServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import RenderHtml from 'react-native-render-html';
import LoginModal from '../../../components/common/LoginModal';
import { getKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import { customAppEvents } from '../../../analytics/AppEvents';
import { translateText } from '../../../services/translation';
import HTMLParser from 'react-native-html-parser';

const ServiceDetailsScreen = ({ navigation, route }) => {
  const api = new Apis();
  const routedata = route?.params;
  const slug = routedata.slug;
  const title = routedata.title;
  const { language } = routedata
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [askForLogin, setAskForLogin] = useState(false);
  const [translatedHtml, setTranslatedHtml] = useState(data.content); // Initial content as is

  const { width } = useWindowDimensions();

  useEffect(() => {
    const checkForItem = async () => {
      await customAppEvents(`looking_for_${slug.replaceAll(" ", "_")}`)
    }
    checkForItem();
  })


  const parser = new HTMLParser.DOMParser();

useEffect(() => {
  const translateHtmlContent = async () => {
    const translateHtml = async (html) => {
      const dom = parser.parseFromString(html, 'text/html');

      // Recursively translate all text nodes
      const translateTextNodes = async (node) => {
        const childNodes = node.childNodes;
        for (let i = 0; i < childNodes.length; i++) {
          const child = childNodes[i];

          if (child.nodeType === 3) {
            // Text node
            const originalText = child.data.trim();
            if (originalText) {
              const translated = await translateText(originalText, language);
              child.data = child.data.replace(originalText, translated);
            }
          } else if (child.childNodes && child.childNodes.length > 0) {
            await translateTextNodes(child);
          }
        }
      };

      await translateTextNodes(dom);

      return dom.toString(); // Convert back to HTML string
    };

    if (data.content && language) {
      const translatedData = await translateHtml(data.content);
      console.log("translatedData >> ", translatedData);
      
      setTranslatedHtml(translatedData);
    }
  };

  translateHtmlContent();
}, [data.content, language]);

  // useEffect(() => {
  //   const translateHtmlContent = async () => {
  //     // Define a simple function to extract and translate text from HTML
  //     const translateHtml = async (htmltext) => {
  //       const regex = /(<([^>]+)>)/ig; // Regular expression to match HTML tags
  //       let translatedContent = htmltext;

  //       // Extract text content (this is a basic method; you may need a more sophisticated parser)
  //       const textContent = htmltext.replace(regex, ""); // Remove tags, get only text

  //       // Translate the text content
  //       const translatedText = await translateText(textContent, language);
  //       console.log("translatedText >> ", translatedText);
        

  //       // Replace the text content with translated text
  //       translatedContent = htmltext.replace(textContent, translatedText);
  //       console.log("translatedContent >> ",translatedContent);
        

  //       return translatedContent;
  //     };

  //     // Translate the content
  //     const translatedData = await translateHtml(data.content);
  //     setTranslatedHtml(translatedData); // Update the state with translated HTML
  //   };

  //   // Trigger translation when content or language changes
  //   if (data.content && language) {
  //     translateHtmlContent();
  //   }
  // }, [data.content, language]);

  useEffect(() => {

    const fetchdetaildata = async () => {
      const payload = {
        url: `${api.getdetaildata}?slug=${slug}&page_type=${title}`,
      };

      await GetServices(payload)
        .then(res => {
          setLoading(false);
          if (res?.data) {
            const detaildata = res?.data;
            setLoading(false);
            setData(detaildata);
          } else {
            console.log('Errort fetching detail screen');
          }
        })
        .catch(error => {
          setLoading(false);
          console.log(error);
        });
    };

    fetchdetaildata();
  }, [slug, title]);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handleBookNow = async () => {
    await customAppEvents("click_booknow")


    const user = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    if (!user) {
      setAskForLogin(true);
      return;
    }
    navigation.navigate(AppRoutes.BOOK_NOW, {
      slug: slug,
      language: language
    });
  };

  const handlewhatsapp = async () => {

    const phoneNumber = '+919724676277';
    const message = 'Hello, I Have A Query';
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          Linking.openURL(url);
        } else {
          console.log('WhatsApp is not installed or the URL is not supported.');
        }
      })
      .catch(err => console.error('An error occurred', err));
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle title={slug} leftIconPress={handleLeftPress} />
      </View>

      {loading
        ? (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        )
        : (
          <ScrollView showsVerticalScrollIndicator={false}>

            {/* main-image */}
            <ImageBackground
              // resizeMode="cover"
              source={{ uri: data.featured_image }}
              style={styles.image}>

            </ImageBackground>

            {/* description */}
            <View style={styles.descriptionContanier}>
              <Text style={styles.descriptionText}>{I18n.t('description')}</Text>
              {data.content ? (
                <RenderHtml
                  tagsStyles={{
                    p: {
                      marginBottom: 10, // Spacing after paragraphs
                      lineHeight: 20,
                      color: 'grey'  // Adjust line height for readability
                    },
                    h1: {
                      marginBottom: 15,
                      color: colors.primary // Spacing after headings
                    },
                    h2: {
                      marginBottom: 12,
                      color: colors.primary// Spacing after subheadings
                    },
                    ul: {
                      marginBottom: 10,
                      color: colors.primary // Add margin for unordered lists
                    },
                    li: {
                      marginBottom: 5,
                      color: colors.primary  // Space between list items
                    },
                  }}
                  contentWidth={width}
                  source={{ html: translatedHtml }}
                />
              ) : (
                <Text>No content available</Text>
              )}
            </View>


            {/* puja-samagri-to-your-booking */}
            {/* <View style={styles.pujaSamagriContanier}>
          <Text style={styles.pujaSamagriText}>
            {I18n.t('puja_samagri_to_your_booking')}
          </Text>
          <View style={styles.horizontalDivider} />         
          {pujaSamagriList.map((item, index) => (
            <Text key={item.id} style={styles.samagriNameTextStyle}>
              {item.id}. {item.samagriName}
            </Text>
          ))}
        </View> */}
          </ScrollView>
        )}

      {/* app-button */}
      <View style={styles.appBtn}>
        <AppButton
          text={I18n.t('book_now')}
          onPressed={handleBookNow}
        />
      </View>

      {/* whatsapp-icon */}
      <TouchableOpacity
        onPress={handlewhatsapp}
        style={styles.whatsappIconContanier}>
        <Image source={images.whatsappIcon} style={styles.whatsappIconStyle} />
      </TouchableOpacity>

      <LoginModal
        visible={askForLogin}
        onClose={() => setAskForLogin(false)}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default ServiceDetailsScreen;
