import {Dimensions, Platform} from 'react-native';

const {width, height} = Dimensions.get('window');

export default class DeviceUiInfo {
  static platform = Platform.OS;
  static screenSize = {width, height};
  static guidelineBaseWidth = 375; // 375

  static getPlatform() {
    return Platform.OS;
  }

  static scale(size) {
    return (this.screenSize.width / this.guidelineBaseWidth) * size;
  }

  static moderateScale(size, factor = 0.5) {
    // return size + (this.scale(size) - size) * factor;
    return Math.floor(size + (this.scale(size) - size) * factor);
  }
}
