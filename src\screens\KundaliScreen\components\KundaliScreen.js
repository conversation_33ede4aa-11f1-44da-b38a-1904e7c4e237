import React, {useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Touchable,
  TouchableOpacity,
} from 'react-native';
import styles from '../styles/KundaliScreen.styles';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import NewKundali from './NewKundali';
import SavedKundali from './SavedKundali';

const KundaliScreen = ({navigation}) => {
  const [selectedTab, setSelectedTab] = useState('New Kundali');

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const handleTabPress = tabName => {
    setSelectedTab(tabName);
  };

  const renderContent = () => {
    if (selectedTab === 'New Kundali') {
      return <NewKundali navigation={navigation}  />;
    } else if (selectedTab === 'Saved Kundali') {
      return <SavedKundali navigation={navigation} />;
    }
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle title={I18n.t('kundali')} leftIconPress={handleLeftPress} />
      </View>

      {/* tab-navigation-button */}
      <View style={styles.tabNavigationContanier}>
        <TouchableOpacity
          onPress={() => handleTabPress('New Kundali')}
          style={[
            selectedTab === 'New Kundali'
              ? styles.activeTab
              : styles.inactiveTab,
          ]}>
          <Text
            style={[
              selectedTab === 'New Kundali'
                ? styles.activeText
                : styles.inactiveText,
            ]}>
            {I18n.t('new_kundali')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleTabPress('Saved Kundali')}
          style={[
            selectedTab === 'Saved Kundali'
              ? styles.activeTab
              : styles.inactiveTab,
          ]}>
          <Text
            style={[
              selectedTab === 'Saved Kundali'
                ? styles.activeText
                : styles.inactiveText,
            ]}>
            {I18n.t('saved_kundali')}
          </Text>
        </TouchableOpacity>
      </View>

      {renderContent()}
    </SafeAreaView>
  );
};

export default KundaliScreen;
