import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import { fontSize } from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB24,
  },
  textfieldContent: {
    ...margins.mT16,
  },
  labelContanier: {
    ...margins.mB2,
    position:'absolute',
    right:0,
    top:0,
    left:56
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  iconImageStyle: {
    position:'absolute',
    right:20,
    top:35,
    height: DeviceUiInfo.moderateScale(22),
    width: DeviceUiInfo.moderateScale(18),
  },
  appbtnContanier: {
    // ...margins.mV13,
    justifyContent: 'flex-end',
    bottom:4
    // flex: 1,
  },
  // dropdownContanierStyle: {
  //   // borderWidth: 1,
  //   zIndex: 1,
  //   position: 'absolute',
  //   bottom: '75%',
  //   left: 0,
  //   right: 0,
  //   borderRadius: DeviceUiInfo.moderateScale(10),
  //   elevation: 1,
  //   backgroundColor: colors.white,
  //   maxHeight: DeviceUiInfo.moderateScale(200),
  //   overflow: 'scroll'
  // },
  countrydropdown:{
    backgroundColor: colors.white,
    maxHeight: DeviceUiInfo.moderateScale(200),
    overflow: 'scroll',
    elevation: 1,
    borderRadius: DeviceUiInfo.moderateScale(10),
  }
});

export default styles;














// import {StyleSheet} from 'react-native';
// import {colors} from '../../../theme/colors';
// import metrics from '../../../theme/metrics';
// import DeviceUiInfo from '../../../utils/DeviceUiInfo';

// const {margins, paddings} = metrics;

// const styles = StyleSheet.create({
//   contanier: {
//     backgroundColor: colors.background,
//     flex: 1,
//     ...paddings.pH24,
//     // ...margins.mB20
//   },
//   appTitleContanier: {
//     ...margins.mT16,
//     ...margins.mB24,
//   },
//   textfieldContext: {
//     ...margins.mT16,
//   },
//   dropdownContanierStyle: {
//     // borderWidth: 1,
//     zIndex: 1,
//     position: 'absolute',
//     bottom: '75%',
//     left: 0,
//     right: 0,
//     borderRadius: DeviceUiInfo.moderateScale(25),
//     elevation: 4,
//     backgroundColor: colors.white,
//     maxHeight: DeviceUiInfo.moderateScale(200),
//   },
//   appbtnContanier: {
//     // ...margins.mT10,
//     bottom:0,
//     alignSelf:'center',
//     // flexDirection:'row',
//     width:'100%',
//     flex:1,
//     position:'absolute',
//     ...margins.mV13,
//   },
// });

// export default styles;
