import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Image,
  TouchableOpacity,
} from 'react-native';
import metrics from '../../theme/metrics';
import {fonts, fontSize} from '../../theme/fonts';
import {colors} from '../../theme/colors';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import {images} from '../../theme/images';
import I18n from '../../utils/language/i18nextConfig';

const DotAppField = ({
  label,
  value,
  name,
  placeholderText,
  placeholderColor,
  onChangeText,
  isEditable,
  containerStyle,
  requirement = false,
  maxSize,
  supportFormat,
  onPress,
}) => {
  return (
    <View style={[styles.contanier, containerStyle]}>
      {label && (
        <View style={styles.labelContanier}>
          <Text style={styles.labelStyle}>{label}</Text>
          {requirement && <Text style={styles.requirementText}>*</Text>}
          <View style={styles.maximumSizeContanier}>
            <Text style={styles.labelStyle}>{I18n.t('maximum_size')}</Text>
            <Text style={styles.labelStyle}> {maxSize}</Text>
          </View>
        </View>
      )}

      <TouchableOpacity onPress={onPress} style={styles.textinputContanier}>
        <Image
          resizeMode="contain"
          style={styles.iconContanier}
          source={images.chooseFilePin}
        />
        <TextInput
          value={value}
          style={styles.textInputStyle}
          placeholder={placeholderText}
          placeholderTextColor={placeholderColor}
          onChangeText={onChangeText}
          editable={isEditable}
        />
      </TouchableOpacity>

      <View style={styles.supportFormatContanier}>
        <Text style={styles.supportFormatText}>
          {I18n.t('supported_formats')}
        </Text>
        <Text style={styles.supportFormatText}> {supportFormat}</Text>
      </View>

      {/* {hasError && <Text style={styles.errorText}>{errors[name]}</Text>} */}
    </View>
  );
};

export default DotAppField;

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {},
  labelContanier: {
    // borderWidth: 1,
    flexDirection: 'row',
    ...margins.mB2,
  },
  labelStyle: {
    // borderWidth: 1,
    fontSize: fontSize.f12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  maximumSizeContanier: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  textinputContanier: {
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: colors.dashedBorderColor,
    ...paddings.pH14,
    flexDirection: 'row',
    // ...margins.mT2,
    width: '100%',
    backgroundColor: colors.white,
    borderRadius: 100,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
    elevation: 4,
    alignItems: 'center',
  },
  textInputStyle: {
    // borderWidth: 2,
    ...paddings.pH8,
    width: '95%',
    ...margins.mT2,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    backgroundColor: colors.white,
    borderRadius: 100,
    elevation: 4,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
  },
  iconContanier: {
    alignItems: 'center',
    justifyContent: 'center',
    height: DeviceUiInfo.moderateScale(18),
    width: DeviceUiInfo.moderateScale(18),
  },
  supportFormatContanier: {
    ...margins.mT5,
    flexDirection: 'row',
  },
  supportFormatText: {
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratRegular,
    fontSize: fontSize.f12,
  },
});
