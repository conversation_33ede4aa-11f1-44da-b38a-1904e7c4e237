import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import {fonts, fontSize} from '../../../theme/fonts';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    // borderWidth: 1,
    ...margins.mT22,
    flex: 1,
    backgroundColor: colors.background,
  },
  headerText: {
    // borderWidth: 1,
    ...margins.mB22,
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
    fontSize: fontSize.f16,
  },
  nosavedkundaliTextContainer:{
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20 
  },
  nosavedkundaliText:{
    fontSize: 16, 
    color: colors.black,
    fontFamily:fonts.MontserratMedium 
  },
  avtarIconContanier: {
    elevation: 3,
    shadowColor: colors.lightGrey,
    justifyContent: 'center',
    alignItems: 'center',
    height: DeviceUiInfo.moderateScale(46),
    width: DeviceUiInfo.moderateScale(46),
    borderRadius: DeviceUiInfo.moderateScale(100),
    backgroundColor: colors.white,
  },
  avtarIconSize: {
    height: DeviceUiInfo.moderateScale(32),
    width: DeviceUiInfo.moderateScale(32),
  },
  nameText: {
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f12,
  },
  subtitleText: {
    ...margins.mT4,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f12,
  },
  placeText: {
    ...margins.mT20,
    ...margins.mL12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f12,

  },
  savedKundaliDivider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV14,
  },
  threeDotContanier: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  threeDotStyle: {
    height: DeviceUiInfo.moderateScale(20),
    width: DeviceUiInfo.moderateScale(20),
  },
});

export default styles;
