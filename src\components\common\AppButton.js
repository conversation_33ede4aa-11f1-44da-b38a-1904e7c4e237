import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {colors} from '../../theme/colors';
import {fonts, fontSize} from '../../theme/fonts';
import metrics from '../../theme/metrics';

const {margins, paddings} = metrics;
const {width, height} = Dimensions.get('window');

const AppButton = ({text, onPressed, disabled, loading = false}) => {
  const isDisabled = disabled || loading;

  return (
    <TouchableOpacity
      onPress={isDisabled ? null : onPressed}
      style={[styles.contanier, isDisabled && styles.disabledButton]}
      activeOpacity={isDisabled ? 1 : 0.8}>
      {loading ? (
        <ActivityIndicator size="small" color={colors.primary} />
      ) : (
        <Text
          style={[styles.text, isDisabled && styles.disabledText]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {text}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default AppButton;

const styles = StyleSheet.create({
  contanier: {
    backgroundColor: colors.primary,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    ...paddings.pV13,
    width:'100%'
  },
  text: {
    color: colors.white,
    fontSize:
      width > 1200
        ? 24
        : width > 1000
        ? 22
        : width > 800
        ? 20
        : width > 600
        ? 18
        : 15,
    fontFamily: fonts.MontserratBold,
    bottom: height > 800 ? 2 : 0,
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  disabledButton: {
    backgroundColor: colors.gray,
  },
  disabledText: {
    color: 'lightgray',
  },
});
