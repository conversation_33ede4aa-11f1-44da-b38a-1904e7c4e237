import {StyleSheet, Text, View, Dimensions} from 'react-native';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;
const {height,width} = Dimensions.get('window')

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background,
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    
  },
  // gifLogo: {
  //   ...margins.mT200,
  //   // borderWidth: 1,
  //   height: '40%',
  //   width: '100%',
  // },

  gifLogo : {
    // ...margins.mT110,
    marginTop: width>=600 ? 220 : 110,
    height: height > 800 ? '50%' : '60%',  // Adjusted for larger screens
    width: width > 1000 ? '60%' : '100%',   // If the width is large (like tablets), use 60%
  },
  welcomeContanier: {
    ...margins.mT5,
    alignSelf: 'center',
    height: DeviceUiInfo.moderateScale(65),
    width: DeviceUiInfo.moderateScale(249),
  },
  welcomeText: {
    alignSelf: 'center',
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f22,
  },
  poweredContanier: {
    // borderWidth: 1,
    ...margins.mB26,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    flex: 1,
  },
  titleContanier: {
    flexDirection: 'row',
    justifyContent: 'center',
    // alignItems: 'center',
    ...margins.mB8,
  },
  LineDimondContanier: {
    // flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  line: {
    width: DeviceUiInfo.moderateScale(50),
    backgroundColor: colors.primary,
    height: 3,
  },
  Diamond: {
    // position: 'absolute',
    // top: DeviceUiInfo.moderateScale(-4),
    width: DeviceUiInfo.moderateScale(12),
    height: DeviceUiInfo.moderateScale(12),
    backgroundColor: colors.primary,
    transform: [{rotate: '45deg'}],
  },
  textTitleContanier: {
    // borderWidth: 1,
    width: '40%',
    // ...paddings.pH24,
    ...margins.mH15,
  },
  titleText: {
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    textAlign: 'center',
    // lineHeight: 25,
  },
  subtitleText: {
    textAlign: 'center',
    color: colors.primary,
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratBold,
  },
});

export default styles;
