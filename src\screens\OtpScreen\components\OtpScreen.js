import React, { useEffect,  useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Alert,
  TouchableOpacity,
  ScrollView,
  Modal,
  Dimensions,
} from 'react-native';
import styles from '../style/OtpScreen.style';
import { colors } from '../../../theme/colors';
import TitleSubtitleWithLines from '../../../components/common/TitleSubtitleWithLines';
import OTPTextView from 'react-native-otp-textinput';
import { fonts, fontSize } from '../../../theme/fonts';
import AppButton from '../../../components/common/AppButton';
import I18n from '../../../utils/language/i18nextConfig';
import AppRoutes from '../../../constants/AppRoutes';
import { saveKey } from '../../../helper/cookies';
import Apis from '../../../services/apiList';
import { PostServicesWithoutToken } from '../../../services/commonApiMethod';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import AppInput from '../../../components/common/AppInput';
import PhoneNumberVerify from '../../../components/common/PhoneNumberVerify';
import Icon from 'react-native-vector-icons/Feather';
import Toast from 'react-native-toast-message';
import { customAppEvents } from '../../../analytics/AppEvents';

const { height, width } = Dimensions.get('window')


const OtpScreen = ({ navigation, route }) => {

  const api = new Apis();

  const [otp, setOtp] = useState('');
  const [timmer, setTimmer] = useState(59);
  const [canResend, setCanResend] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmpassword: '',
  });

  const [loading, setLoading] = useState(false);
  const [sendOtpLoading, setSendOtpLoading] = useState(false);
  const [mobileNumber, setMobileNumber] = useState('');
  const [countryCode, setCountryCode] = useState('91');
  const [isTimerStarted, setIsTimerStarted] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [isVisibleOTP, setIsVisibleOTP] = useState(false);

  const togglePasswordVisibility = () => { setPasswordVisible(!passwordVisible) };
  const toggleConfirmPasswordVisibility = () => {
    setConfirmPasswordVisible(!confirmPasswordVisible);
  };


  const inputhandlechange = (field, value) => {
    setFormData(prevdata => ({
      ...prevdata,
      [field]: value,
    }));
  };

  const validationForm = () => {
    const { email, password, confirmpassword } = formData;

    if (!email || !password || !confirmpassword || !mobileNumber) {
      Alert.alert('Required', 'Please Fill the Details');
      return false;
    }

    const emailRegex = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address');
      return false;
    }

    const passwordRegex = /^(?=.*[A-Z])(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&_*]{8,}$/;
  if (!passwordRegex.test(password)) {
    Alert.alert(
      'Invalid Password',
      'Password must be at least 8 characters long, contain at least one uppercase letter and one special character.'
    );
    return false;
  }
  
    if (password.length < 6) {
      Alert.alert(
        'Invalid Password',
        'Password must be at least 6 characters.',
      );
      return false;
    }

    if (password !== confirmpassword) {
      Alert.alert(
        'Password Mismatch',
        'Password and Confirm Password do not match.',
      );
      return false;
    }

    if (mobileNumber.length < 7 || mobileNumber.length > 10) {
      Alert.alert(
        'Invalid Phone Number',
        'Phone number must be between 7 and 10 digits.',
      );
      return;
    }

    return true;
  };

  useEffect(() => {
    let interval;
    if (isTimerStarted && timmer > 0 && !canResend) {
      interval = setInterval(() => {
        setTimmer(prevTimmer => prevTimmer - 1);
      }, 1000);
    } else if (timmer === 0) {
      Alert.alert('OTP Expired', 'OTP has expired. Please resend OTP.');
      setIsVisibleOTP(false)
      setCanResend(true);
    }

    return () => clearInterval(interval);
  }, [timmer, canResend, isTimerStarted]);

  const handleotp = async () => {
    const mobileNo = countryCode + mobileNumber;

    if (!validationForm()) return;
    setSendOtpLoading(true);

    const rawData = JSON.stringify({
      email: formData.email,
      password: formData.password,
      confirm_password: formData.confirmpassword,
      mobile_no: mobileNo,
    });

    const payload = {
      url: api.submitOTP,
      data: rawData,
    };
    await PostServicesWithoutToken(payload)
      .then(res => {
        setSendOtpLoading(false);
        if (res?.success) {
          Toast.show({
            type: 'success',
            position: 'top',
            text1: 'OTP Verification!',
            text2: 'OTP has been sent to your Email',
            visibilityTime: 3000,
          });

          setIsVisibleOTP(true)

          if (!isTimerStarted) {
            setIsTimerStarted(true);
            setTimmer(59);
          }
        } else {
          console.log("Error : " , res?.error?.message);
          
          Toast.show({
            type: 'error',
            position: 'top',
            text1: 'Note',
            text2: res?.error?.message || 'Something went wrong',
            visibilityTime: 3000,
          });
          setIsTimerStarted(false);
          setTimmer(59);
        }
      })
      .catch(err => {
        console.log('Error fetching OTP', err);
        setSendOtpLoading(false);

        Toast.show({
          type: 'error',
          position: 'top',
          text1: 'Network Error',
          text2: 'Unable to send OTP at the moment',
          visibilityTime: 3000,
        });

        setIsTimerStarted(false);
        setTimmer(59);
      });

    // if (!isTimerStarted) {
    //   setIsTimerStarted(true);
    //   setTimmer(59);
    // }
  };

  const handleSubmit = async () => {
    if (!otp) {
      Alert.alert('Required', 'Please Fill the Details');
      return false;
    }
    if (otp.length === 4) {

      const { email, password, confirmpassword } = formData;
      const mobileNo = countryCode + mobileNumber;

      const verifydata = JSON.stringify({
        email: email,
        password: password,
        confirm_password: confirmpassword,
        otp: otp,
        mobile_no: mobileNo,
      });

      const payload = {
        url: api.verifyOTP,
        data: verifydata,
      };

      setLoading(true);
      await PostServicesWithoutToken(payload)
        .then(async(res) => {
          setLoading(false);
          if (res?.success) {
           
            const tokenuser = res?.data?.token;
           

            if (tokenuser) {
              saveKey(ASYNC_KEY_CONSTANTS.ACCESS_TOEKN, res?.data?.token);
             
            } else {
              console.log('Token Is Missing!!');
            }

            saveKey(
              ASYNC_KEY_CONSTANTS.USER_DETAIL,
              JSON.stringify(res?.data?.user),
            );

          
            if (res?.data?.is_new_user) {

              await customAppEvents("user_signup");
              navigation.replace(AppRoutes.PROFILE_SCREEN, {
                userDetail: res?.data?.user,
              });
            }
          } else {
            Alert.alert('Error', 'Wrong OTP Verified');
            setLoading(false);
            setOtp('');
          }
        })
        .catch(err => {
          console.log('Error fetching the OTP', err);
          setLoading(false);
          setOtp('');
        });
    }
  };

  const handleResend = () => {
    if (canResend) {
      setTimmer(59);
      setIsVisibleOTP(true)
      setCanResend(false);
      setOtp('');
      handleotp();
    }
  };

  return (
    <View style={{ flex: 1 }}>

      <SafeAreaView style={styles.contanier}>
        <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.headerContanier}>
            <TitleSubtitleWithLines
              title={I18n.t('please_create_your_profile')}
              subtitle={I18n.t('please_enter_following_details')}
            />
          </View>

          <View style={{ marginVertical: 14 }}>
            <PhoneNumberVerify
              label={I18n.t('phone_number')}
              onChangeText={(text) => {
                if (text.length <= 10) {
                  setMobileNumber(text);
                }
              }}
              onCountryCodeChange={setCountryCode}
              keyboardType="numeric"
              requirement={true}
              maxLength={10}
              minLength={10}
            />

            <AppInput
              label={I18n.t('email')}
              requirement={true}
              value={formData.email}
              onChangeText={text => inputhandlechange('email', text)}
              containerStyle={{ marginVertical: 14 }}
            />

            <View style={styles.passwordContainer}>
              <AppInput
                label={I18n.t('password')}
                containerStyle={{ marginBottom: 14 }}
                requirement={true}
                value={formData.password}
                onChangeText={text => inputhandlechange('password', text)}
                secureTextEntry={!passwordVisible}
              />
              <TouchableOpacity
                onPress={togglePasswordVisibility}
                style={styles.eyeIconContainer}>
                <Icon
                  name={passwordVisible ? 'eye' : 'eye-off'}
                  size={20}
                  color={colors.black}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.passwordContainer}>
              <AppInput
                label={I18n.t('confirmpassword')}
                containerStyle={{ marginBottom: 14 }}
                requirement={true}
                value={formData.confirmpassword}
                onChangeText={text => inputhandlechange('confirmpassword', text)}
                secureTextEntry={!confirmPasswordVisible}
              />
              <TouchableOpacity
                onPress={toggleConfirmPasswordVisibility}
                style={styles.eyeIconContainer}>
                <Icon
                  name={confirmPasswordVisible ? 'eye' : 'eye-off'}
                  size={20}
                  color={colors.black}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.sendotpcontainer}>
            {/* <View>
              <TouchableOpacity
                style={styles.resendContanier}
                onPress={handleResend}>
                <Text style={{ ...styles.resendText, color: colors.primary }}>
                  Resend Code?{' '}
                </Text>
                <Text style={{ ...styles.resendText, color: colors.purple }}>
                  {/* {`${timmer}s`} }
                  {timmer > 0 ? `${timmer}s` : ''}
                </Text>
              </TouchableOpacity>
            </View> */}
            <View style={{ flex: 1, paddingHorizontal: 50, marginTop: 10 }}>
              <AppButton
                text={timmer === 0 ? I18n.t('resend_otp') : I18n.t('send_otp')}
                onPressed={timmer === 0 ? handleResend : handleotp}
                loading={sendOtpLoading}
              />
            </View>
          </View>

          {/* <View style={styles.appButton}>
            <AppButton
              text={I18n.t('submit')}
              onPressed={() => {
                setIsVisibleOTP(false);
                handleSubmit();
              }}
              loading={loading}
            />
          </View> */}

          <Modal
            visible={isVisibleOTP}
            animationType="fade"
            backdropColor={'rgba(2, 2, 2, 0.81)'}
            style={styles.overlay}
          >
            <View style={styles.OTPModalOverlay}>
              <View style={styles.otpContanier}>
                <Text style={{
                  fontFamily: fonts.MontserratMedium,
                  fontSize: fontSize.f16,
                  marginBottom: 20,
                  color: colors.primary,
                  fontWeight: 'bold',
                  textAlign: 'center'
                }}>Please enter OTP received on your entered email</Text>
                <OTPTextView
                  inputCount={4}
                  textInputStyle={[styles.otpTextinput]}
                  keyboardType="numeric"
                  tintColor="transparent"
                  offTintColor="transparent"
                  handleTextChange={text => setOtp(text)}
                />

                <View style={styles.sendotpcontainer}>
                  <View>
                    <TouchableOpacity
                      style={styles.resendContanier}
                      onPress={handleResend}>
                      <Text style={{ ...styles.resendText, color: colors.primary }}>
                        Resend Code?{' '}
                      </Text>
                      <Text style={{ ...styles.resendText, color: colors.purple }}>
                        {/* {`${timmer}s`} */}
                        {timmer > 0 ? `${timmer}s` : ''}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  
                  {/* <View style={{ flex: 1, paddingHorizontal: 10, marginTop: 20 }}>
                    <AppButton
                      text={timmer === 0 ? I18n.t('resend_otp') : I18n.t('send_otp')}
                      onPressed={timmer === 0 ? handleResend : handleotp}
                      loading={sendOtpLoading}
                    />
                  </View> */}

                </View>

                <View style={{ flex: 1, paddingHorizontal: 10, marginTop: 20 , width : 150 }}>
                  <AppButton
                    text={I18n.t('submit')}
                    onPressed={() => {
                      setIsVisibleOTP(false);
                      handleSubmit();
                    }}
                    loading={loading}
                  />
                </View>
              </View>

            </View>
          </Modal>

        </ScrollView>


      </SafeAreaView>
      <Toast />
    </View>
  );
};

export default OtpScreen;
