import { StyleSheet, Text, View, SafeAreaView,FlatList } from 'react-native'
import React from 'react';
import styles from '../styles/Sampurn.styles';


const Sampurn = ({apidata}) => {
    const SampurnData = [
        {
          id: 1,
          details: apidata.horoscope_overall || ''
        },
      ];
    
      const SampurnRenderItem = ({item, index}) => {
        return (
          <View style={styles.doshaContanier}>
            {item.details && (
              <Text style={styles.itemDetailsStyle}>{item.details}</Text>
            )}
          </View>
        );
      };
    
      return (
        <SafeAreaView style={styles.contanier}>
          <Text style={styles.titleText}>Overall</Text>
    
          <FlatList
            data={SampurnData}
            renderItem={SampurnRenderItem}
            keyExtractor={(item, index) => item.id.toString()}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      );
}

export default Sampurn;