import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StatusBar,
  ImageBackground,
  Image,
  TouchableOpacity,
  FlatList,
  Modal,
  ScrollView,
  BackHandler,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import styles from '../styles/AccountScreen.style';
import { colors } from '../../../theme/colors';
import I18n from '../../../utils/language/i18nextConfig';
import AppRoutes from '../../../constants/AppRoutes';
import { images } from '../../../theme/images';
import metrics from '../../../theme/metrics';
import Apis from '../../../services/apiList';
import { GetServices } from '../../../services/commonApiMethod';
import { getKey, clearKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';
import Share from 'react-native-share';
import { customAppEvents } from '../../../analytics/AppEvents';

const AccountScreen = ({ navigation, route }) => {
  const api = new Apis();
  const { margins, paddings } = metrics;
  const [isLogoutModalVisible, setIsLogoutModalVisible] = useState(false);
  const [name, setName] = useState('');
  const [profileImage, setProfileImage] = useState(null);
  const [languageChanged, setLanguageChanged] = useState(false);

  useEffect(() => {
    const handleLanguageChange = () => {
      setLanguageChanged(prev => !prev);
    };
    I18n.on('languageChanged', handleLanguageChange);

    return () => {
      I18n.off('languageChanged', handleLanguageChange);
    };
  }, []);

  useEffect(() => {
    const handleBackPress = () => {
      // When back button is pressed, navigate to Home
      navigation.navigate(AppRoutes.BOTTOM_NAV_BAR);
      return true; // Prevent the default back action
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => backHandler.remove();

  }, [navigation]);


  const handleLogout = async () => {
    console.log("Asda");
    await clearKey();

    await customAppEvents("user_logout");

    // Reset navigation and go to BOTTOM_NAV_BAR
    navigation.reset({
      index: 0,
      routes: [{ name: AppRoutes.BOTTOM_NAV_BAR }],
    });
  };


  const getname = async () => {
    const userdetail = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    const userdetailname = JSON.parse(userdetail).ID;
    const ID = userdetailname;

    const payload = {
      url: `${api.getuser}${userdetailname}`,
    };

    await GetServices(payload).then(res => {
      const name = res?.data?.data;
      const firstname = name.first_name;
      const lastname = name.last_name;
      const profile = name.profile_image
      const userName = firstname + ' ' + lastname;
      setName(userName);
      setProfileImage(profile)
    });
  };

  useEffect(() => {
    getname();
  }, []);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const shareApp = async () => {
    try {
      const shareOptions = {
        message: I18n.t('share_text') || 'Check out this amazing app!',
        url: 'https://panditsnearme.com/',
        title: 'Pandit Near Me',
      };

      const result = await Share.open(shareOptions);
    } catch (error) {
      if (error.message === 'User did not share') {
        console.log('User canceled the share action.')
      } else {
        console.error('Error sharing app:', error);
      }
    }
  };


  const accountPageData = [
    {
      id: 1,
      icon: images.editIcon,
      pageName: I18n.t('edit_profile'),
      routes: AppRoutes.UPDATE_PROFILE_SCREEN,
    },
    {
      id: 2,
      icon: images.myPujaIcon,
      pageName: I18n.t('my_puja'),
      routes: AppRoutes.VIEW_BOOK_PUJA_LIST,
    },
    {
      id: 3,
      icon: images.aboutUs,
      pageName: I18n.t('about_us'),
      routes: AppRoutes.ABOUT_US,
    },
    {
      id: 4,
      icon: images.privacyPolicyIcon,
      pageName: I18n.t('privacy_policy'),
      routes: AppRoutes.PRIVACY_POLICY_SCREEN,
    },
    {
      id: 5,
      icon: images.termsConditionIcon,
      pageName: I18n.t('terms_conditions'),
      routes: AppRoutes.TERMS_CONDITION_SCREEN,
    },
    {
      id: 6,
      icon: images.refundPolicyIcon,
      pageName: I18n.t('refund_policy'),
      routes: AppRoutes.REFUND_POLICY_SCREEN,
    },
    {
      id: 7,
      icon: images.LanguageSelectIcon,
      pageName: I18n.t('select_language'),
      routes: AppRoutes.LANGUAGE_SELECT_SCREEN,
    },
    {
      id: 8,
      icon: images.contanctUsIcon,
      pageName: I18n.t('contact_us'),
      routes: AppRoutes.CONTACT_US,
    },
    // {
    //   id: 9,
    //   icon: images.rateUsIcon,
    //   pageName: I18n.t('rate_us'),
    //   routes: AppRoutes.ACCOUNT_SCREEN,
    // },
    {
      id: 10,
      icon: images.shareAppIcon,
      pageName: I18n.t('share_app'),
      onPress: shareApp,
    },
    {
      id: 11,
      icon: images.accountLogOutIcon,
      pageName: I18n.t('log_out'),
      onPress: console.log(""),
    },
  ];

  const accountPageRenderItem = ({ item, index }) => {
    return (
      <>
        <TouchableOpacity
          onPress={() => {
            if (item.pageName === I18n.t('log_out')) {
              setIsLogoutModalVisible(true);
            } else if (item.pageName === I18n.t('share_app')) {
              item.onPress();
            } else {
              navigation.navigate(item.routes);
            }
          }}
          style={styles.accountPageRenderContanier}>
          <Image
            resizeMode="contain"
            style={styles.renderIconStyle}
            source={item.icon}
          />
          <Text
            style={[
              styles.renderPagename,
              {
                color:
                  item.pageName === I18n.t('log_out')
                    ? colors.purple
                    : colors.dividerBlack,
              },
            ]}>
            {item.pageName}
          </Text>
          <View style={styles.arrowViewContanier}>
            <View>
              {index < accountPageData.length - 1 && (
                <Image source={images.arrowDown} style={styles.renderArrow} />
              )}
            </View>
          </View>
        </TouchableOpacity>
        {index < accountPageData.length - 1 && (
          <View style={styles.accountDivider} />
        )}
      </>
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.updatedFirstName && route.params?.updatedLastName) {
        setName(
          `${route.params.updatedFirstName} ${route.params.updatedLastName}`,
        );
      } else {
        getname();
      }
    }, [route.params]),
  );


  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        {/* <AppTitle title={I18n.t('profile')} leftIconPress={handleLeftPress} /> */}
      </View>

      <ScrollView
        style={{ ...margins.mB10 }}
        showsVerticalScrollIndicator={false}>
        {/* account-photo */}
        <View style={styles.accountPhotoContanier}>
          <ImageBackground
            style={styles.elevationBackground}
            resizeMode="contain"
            source={images.accountElevationBackground}>
            <Image
              style={styles.accountPhoto}
              resizeMode="cover"
              source={profileImage ? { uri: profileImage } : images.accountProfilePhoto}
            // source={require('../../../assets/images/app_images/kark.png')} 
            // source={profileImage ? { uri: profileImage } : images.accountProfilePhoto}
            />
            {/* <TouchableOpacity
            onPress={() =>
              navigation.navigate(AppRoutes.UPDATE_PROFILE_SCREEN)
            }>
            <Image
              style={styles.accountEditIcon}
              resizeMode="contain"
              source={images.accountEditIcon}
            />
          </TouchableOpacity> */}
          </ImageBackground>
          <Text style={styles.accountName}>{name}</Text>
        </View>

        <View style={{ marginTop: 16 }}>
          <FlatList
            data={accountPageData}
            renderItem={accountPageRenderItem}
            keyExtractor={(item, index) => item.id.toString()}
            showsVerticalScrollIndicator={false}
          // ListHeaderComponent={accountPageRenderItem}
          />
        </View>
      </ScrollView>

      {/* logout-modal */}
      <Modal
        visible={isLogoutModalVisible}
        transparent={true}
        onRequestClose={() => setIsLogoutModalVisible(false)}>
        <View style={styles.logoutBackground}>
          <View style={styles.logoutModalContanier}>
            <Text style={styles.logoutText}>{I18n.t('log_out')}</Text>
            <Text style={styles.logoutSubText}>
              {I18n.t('are_you_sure_logout')}
            </Text>

            <View style={{ flexDirection: 'row', ...margins.mT22 }}>
              <TouchableOpacity
                style={styles.cancelContanier}
                onPress={() => setIsLogoutModalVisible(false)}>
                <Text style={styles.cancelText}>{I18n.t('cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.yesLogoutContanier}
                onPress={() => handleLogout()}>
                <Text style={styles.yesLogoutText}>{I18n.t('yes_logout')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default AccountScreen;
