import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';
import { Dimensions } from 'react-native';

const {margins, paddings} = metrics;
const {height,width} = Dimensions.get('window')

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    padding:15,
  },
  loaderContainer: {
    flex: 1, 
    justifyContent: 'center',  
    alignItems: 'center',      
    position: 'absolute',      
    top: 0,                    
    left: 0,                   
    right: 0,                  
    bottom: 0,                 
    zIndex: 999,               
  },
  // footerLoaderContainer: {
  //   flex: 1,
  //   position: 'absolute',      
  //   justifyContent: 'center',
  //   alignItems: 'center',    
  //   marginVertical: 20,   
  // },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB20,
  },
  card: {
    height: DeviceUiInfo.moderateScale(160),
    // width: DeviceUiInfo.moderateScale(173),
    width: width > 700 ? width * 0.295 : width * 0.435,
    marginRight: width > 700 ? width * 0.025 : width * 0.025
    //  overflow: 'hidden'
  },
  serviceImg: {
    width: '100%',
    height: '100%',
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  serviceRightArrowContanier: {
    position: 'absolute',
    right: DeviceUiInfo.moderateScale(9),
    top: DeviceUiInfo.moderateScale(9),
  },
  serviceRightArrowStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },

  gradientBackground: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(0),
    left: 0,
    right: 0,
    paddingVertical: DeviceUiInfo.moderateScale(60),
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  serviceText: {
    color: colors.white,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(6),
    alignSelf: 'center',
    paddingHorizontal: DeviceUiInfo.moderateScale(8),
    zIndex: 1,
  },
  whatsappIconContanier: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(12),
    right: DeviceUiInfo.moderateScale(20),
  },
  whatsappIconStyle: {
    height: DeviceUiInfo.moderateScale(64),
    width: DeviceUiInfo.moderateScale(64),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: 'black',
  },
  flatlistContainer: {
    paddingHorizontal: 6, //prev it was 10...recently changed to centered design
  },
});

export default styles;
