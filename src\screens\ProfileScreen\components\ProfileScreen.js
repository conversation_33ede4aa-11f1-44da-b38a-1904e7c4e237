import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Image,
  Dimensions,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import styles from '../styles/ProfileScreen.style';
import {colors} from '../../../theme/colors';
import TitleSubtitleWithLines from '../../../components/common/TitleSubtitleWithLines';
import AppInput from '../../../components/common/AppInput';
import PhoneNumberVerify from '../../../components/common/PhoneNumberVerify';
import {images} from '../../../theme/images';
import AppButton from '../../../components/common/AppButton';
import I18n from '../../../utils/language/i18nextConfig';
import AppRoutes from '../../../constants/AppRoutes';
import DropdownTextfield from '../../../components/common/DropdownTextfield';
import Apis from '../../../services/apiList';
import {PostServices} from '../../../services/commonApiMethod';
import {getKey, saveKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';
import { fonts } from '../../../theme/fonts';

const {height: screenHeight} = Dimensions.get('window');

const ProfileScreen = ({navigation, route}) => {
  const api = new Apis();
  const {message} = route?.params 

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    location: '',
    gender: '',
  });
  const [isGenderDropdownOpen, setIsGenderDropdownOpen] = useState(false);

  const getPaddingBottom = () => {
    if (screenHeight < 600) {
      return 140;
    } else if (screenHeight >= 600 && screenHeight < 800) {
      return 130;
    } else {
      return 200;
    }
  };

  const paddingBottom = getPaddingBottom();

  const handleGenderChange = selectedgender => {
    setFormData(prev => ({...prev, gender: selectedgender}));
  };

  const handleGenderDropdownToggle = () => {
    setIsGenderDropdownOpen(prevState => !prevState);
  };

  useEffect(() => {
    const getNumber = async () => {
      const number = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);

      let phoneNumber = JSON.parse(number).phone || '';

      let cleanNumber = phoneNumber.startsWith('91') ? phoneNumber.slice(2) : phoneNumber;

      setFormData(prevState => ({
        ...prevState,
        phone: cleanNumber
      }));

      const userEmail = JSON.parse(number)?.email;
      setFormData(prevState => ({
        ...prevState,
        email: userEmail || '', 
      }));
      
    };

    getNumber();
  }, []);

  useEffect(() => {
    console.log('Updated phone number: ', formData.phone);
  }, [formData.phone]);


  const handleSubmit = async () => {
    const {firstName, lastName, email, phone, location, gender} = formData;

    if (!firstName || !lastName || !email || !phone || !location || !gender) {
      Alert.alert('Note', 'All fields are required!');
      return;
    }

    // Email validation
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    // Phone validation
    if (phone.length < 7 || phone.length > 15) {
      Alert.alert(
        'Invalid Phone Number',
        'Phone number must be between 7 and 15 digits.',
      );
      return;
    }

    const userDetail = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    const userId = JSON.parse(userDetail).ID;

    var userData = new FormData();
    userData.append('first_name', formData.firstName);
    userData.append('last_name', formData.lastName);
    userData.append('email', formData.email);
    userData.append('phone', formData.phone);
    userData.append('location', formData.location);
    userData.append('gender', formData.gender);
    userData.append(
      'profile_image',
      'https://plus.unsplash.com/premium_photo-1683865776032-07bf70b0add1?q=80&w=1332&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    );

    const payload = {
      url: `${api.userprofile}/${userId}`,
      data: userData,
      type: 'formData',
    };
    await PostServices(payload)
      .then(async response => {

        if (response.success) {
          const updatedUserDetail = {
            ...JSON.parse(userDetail),
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData?.email,
            phone: formData?.phone,
            location: formData?.location,
            gender: formData?.gender
          };
          await saveKey(ASYNC_KEY_CONSTANTS.IS_PROFILE_COMPLETE,response?.data?.profile_created);
           
          await saveKey(
            ASYNC_KEY_CONSTANTS.USER_DETAIL,
            JSON.stringify(updatedUserDetail),
          );
          Alert.alert(response?.message, response?.data?.message);

          // navigation.navigate(AppRoutes.CONGRATULATION_SCREEN);
          navigation.reset({
            index: 0,
            routes: [{name: AppRoutes.CONGRATULATION_SCREEN}],
          });
        } else {
          Alert.alert('Error', 'Error fetching response from API<<<');
        }
      })
      .catch(error => {
        console.log('Error fetching the API', error);
      });
  };

  const handleChange = field => value => {
    setFormData(prev => ({...prev, [field]: value}));
  };

  const dropdownOption = () => ['Male', 'Female'];

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      <ScrollView
        contentContainerStyle={{paddingBottom: paddingBottom}}
        showsVerticalScrollIndicator={false}>
        <View style={styles.titleContanier}>
          <TitleSubtitleWithLines
            title={I18n.t('get_started_now')}
            subtitle={I18n.t('create_your_profile')}
          />
        </View>

        <View style={styles.formContanier}>
          <View style={styles.formContanier}>
            <View style={styles.nameRow}>
              <AppInput
                label={I18n.t('first_name')}
                value={formData.firstName}
                placeholderText="Lois"
                onChangeText={handleChange('firstName')}
                containerStyle={styles.halfWidth}
                requirement={true}
              />

              <AppInput
                label={I18n.t('last_name')}
                value={formData.lastName}
                placeholderText="becket"
                onChangeText={handleChange('lastName')}
                containerStyle={styles.halfWidth}
                requirement={true}
              />
            </View>
          </View>

          <View style={styles.textfieldContent}>
            <AppInput
              label={I18n.t('email')}
              value={formData.email}
              placeholderText="<EMAIL>"
              onChangeText={handleChange('email')}
              requirement={true}
              isEditable={false}
            />
          </View>

            <View style={styles.textfieldContent}>
              <PhoneNumberVerify
                label={I18n.t('phone_number')}
                value={formData.phone}
                onChangeText={handleChange('phone')}
                keyboardType="numeric"
                requirement={true}
                isEditable={false}
              />
            </View>


          <View style={styles.textfieldContent}>
            <AppInput
              label={I18n.t('locations')}
              leftIcon={<Image source={images.location} />}
              onChangeText={handleChange('location')}
              requirement={true}
            />
          </View>

          <View style={styles.textfieldContent}>
            <DropdownTextfield
              label={I18n.t('gender')}
              requirement={true}
              options={dropdownOption()}
              onSelect={handleGenderChange}
              isDropdownOpen={isGenderDropdownOpen}
              onDropdownToggle={handleGenderDropdownToggle}
              value={formData.gender}
            />
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity
      onPress={()=> navigation.navigate(AppRoutes.BOTTOM_NAV_BAR)
      }
      style={{alignSelf:'center',marginBottom:20}}>
        <Text style={{
          fontFamily:fonts.MontserratMedium,
          fontSize:fonts.f14
        }}>Skip</Text>
      </TouchableOpacity>
      <View style={styles.appbtnContanier}>
        <AppButton
          text={I18n.t('create_profile')}
          onPressed={() => handleSubmit()}
        />
      </View>
    </SafeAreaView>
  );
};

export default ProfileScreen;

//validation code
// if (!firstName || !lastName || !email || !phone || !location || !gender) {
//   Alert.alert('Note', 'All fields are required!');
//   return;
// }

// const emailRegex = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
// if (!emailRegex.test(email)) {
//   Alert.alert('Invalid Email', 'Please enter a valid email address.');
//   return;
// }

// if (phone.length < 7 || phone.length > 12) {
//   Alert.alert(
//     'Invalid Phone Number',
//     'Phone number must be between 7 and 12 digits.',
//   );
//   return;
// }
//----------
