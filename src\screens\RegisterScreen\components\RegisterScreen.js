import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Alert,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import styles from '../styles/RegisterScreen.style';
import {colors} from '../../../theme/colors';
import TitleSubtitleWithLines from '../../../components/common/TitleSubtitleWithLines';
import AppButton from '../../../components/common/AppButton';
import I18n from '../../../utils/language/i18nextConfig';
import AppRoutes from '../../../constants/AppRoutes';
import Apis from '../../../services/apiList';
import {
  PostServicesWithoutToken,
} from '../../../services/commonApiMethod';
import {getKey, saveKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';
import AppInput from '../../../components/common/AppInput';
import Icon from 'react-native-vector-icons/Feather';
import { customAppEvents } from '../../../analytics/AppEvents';

const RegisterScreen = ({navigation, route}) => {
  const api = new Apis();

  const formikRef = useRef(null);
  const [mobileNumber, setMobileNumber] = useState('');
  const [countryCode, setCountryCode] = useState('91');
  const [callingCode, setCallingCode] = useState('1');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);

  // Handle password visibility toggle

  const togglePasswordVisibility = () => {
    setPasswordVisible(!passwordVisible);
  };

  const handlelogin = async () => {
    if (!email) {
      Alert.alert('Required', 'Please enter a email');
      return false;
    }

    const emailRegex = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address');
      return false;
    }

    if (!password) {
      Alert.alert('Required', 'Please enter a password');
      return false;
    }

    setLoading(true);

    const rawlogin = JSON.stringify({
      email: email,
      password: password,
    });

    const payload = {
      url: api.login,
      data: rawlogin,
    };

    await PostServicesWithoutToken(payload)
      .then(async res => {
        if (res?.success) {
          // Alert.alert('Success', 'Login Sucessfully');
          await saveKey(ASYNC_KEY_CONSTANTS.ISNEWUSER, res?.data?.is_new_user);
          await saveKey(ASYNC_KEY_CONSTANTS.ACCESS_TOEKN, res?.data?.token);
         

          const users = res?.data?.user;
          await saveKey(ASYNC_KEY_CONSTANTS.USER_DETAIL, JSON.stringify(users));
           await customAppEvents("user_login");
          setLoading(false);

          if (!users?.first_name || !users?.last_name) {
            navigation.replace(AppRoutes.PROFILE_SCREEN, {
              message: I18n.t('create_profile_message'),
            });
          } else {
            navigation.replace(AppRoutes.BOTTOM_NAV_BAR);
          }
        } else {
          setLoading(false);
          Alert.alert('Note', 'Invalid email or password');
        }
      })
      .catch(err => {
        console.log('Error Login in user', err);
        setLoading(false);
      });
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{marginBottom: 12}}>
        <View style={styles.titleContanier}>
          <TitleSubtitleWithLines
            title={I18n.t('login')}
            subtitle={I18n.t('')}
          />
        </View>

        {/* <Formik
        initialValues={userDetail}
        validationSchema={phoneNumberValidationSchema}>
       
      </Formik> */}
      
        <View style={styles.phoneNumberContanier}>
          <AppInput
            label={I18n.t('email')}
            requirement={true}
            containerStyle={{marginBottom: 16}}
            value={email}
            onChangeText={setEmail}
          />
          <View>
            <AppInput
              label={I18n.t('password')}
              requirement={true}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!passwordVisible}
            />
            <TouchableOpacity
              onPress={togglePasswordVisibility}
              style={styles.eyeIconContainer}>
              <Icon
                name={passwordVisible ? 'eye' : 'eye-off'}
                size={20}
                color={colors.black}
              />
            </TouchableOpacity>
          </View>
          {/* <PhoneNumberVerify
            label={I18n.t('phone_number')}
            onChangeText={setMobileNumber}
            onCountryCodeChange={setCountryCode}
            keyboardType="numeric"
          /> */}
          <TouchableOpacity
            style={styles.forgotpassword}
            onPress={() => navigation.navigate(AppRoutes.FORGOTPASSWORD)}>
            <Text style={styles.forgottext}>Forgot Password?</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.appButtonContanier}>
          <AppButton
            text={I18n.t('login')}
            onPressed={handlelogin}
            loading={loading}
          />
        </View>

        <View
          style={{
            flex: 1,
            alignItems: 'center',
            marginTop: 20,
            flexDirection: 'row',
            justifyContent: 'center',
            gap: 5,
          }}>
          <Text>Dont have an account?</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate(AppRoutes.OTP_SCREEN)}>
            <Text style={{color: colors.black}}>Sign Up Here</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default RegisterScreen;
