
import {StyleSheet} from 'react-native';
import {fonts, fontSize} from '../../theme/fonts';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import {colors} from '../../theme/colors';
import metrics from '../../theme/metrics';

const {margins, paddings} = metrics;

export default StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 10,
    padding: 20,
    width: '90%',
    alignItems: 'center',
  },
  closeIconContainer: {
    backgroundColor:'#fff',
    position:'absolute',
    top:-20,
    right:-10,
    borderRadius:100,
    padding:5,
    elevation: 10,
    shadowColor: colors.lightOrange,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 3.84,
  },
  modalText: {
    fontFamily: fonts.MontserratBold,
    fontSize: fontSize.f18,
    color: colors.black,
    textAlign: 'center',
    marginHorizontal:10
  },
  modalDescription: {
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
    color: 'grey',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButtonsContainer: {
    flexDirection:'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...margins.mB5,
    alignSelf:'center',
    gap:10
  },
  btnContainer: {
   height: DeviceUiInfo.moderateScale(32),
    paddingLeft:10,
    paddingRight:15,
    backgroundColor: colors.lightOrange,
    flexDirection:'row',
    borderRadius: DeviceUiInfo.moderateScale(68),
    elevation: 10,
    shadowColor: colors.lightOrange,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    alignItems: 'center',
    justifyContent: 'center',
    ...margins.mB5,
    alignSelf:'center'
  },
}); 