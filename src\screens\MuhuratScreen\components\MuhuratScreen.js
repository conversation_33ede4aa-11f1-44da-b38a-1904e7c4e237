import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StatusBar,
  Image,
  TouchableOpacity,
  Animated,
  FlatList,
  Alert,
  ActivityIndicator,
  Dimensions,
  ScrollView,
} from 'react-native';
import styles from '../styles/MuhuratScreen.styles';
import {colors} from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import AppInput from '../../../components/common/AppInput';
import {images} from '../../../theme/images';
import metrics from '../../../theme/metrics';
import Apis from '../../../services/apiList';
import {PostServicesWithoutToken} from '../../../services/commonApiMethod';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import { getKey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';

const map_Access_Token =
  'pk.eyJ1IjoicGFydGgwMiIsImEiOiJjbTd4NWZ0bzcwMWxkMnFwZXRmeWgxbGZnIn0.y2KRmr1T5TBt938N9PVTHg';

const MuhuratScreen = ({navigation, route}) => {
  const {language} = route?.params;
  const api = new Apis();
  const [isDayMode, setIsDayMode] = useState(true);
  const [date, setDate] = useState(new Date());
  const [open, setOpen] = useState(false);
  const {margins, paddings} = metrics;
  const togglePosition = useRef(new Animated.Value(0)).current;
  const [muhurats, setMuhurat] = useState([]);
  const [days, setDays] = useState([]);
  const [nights, setNights] = useState([]);
  const [loading, setLoading] = useState(true);
  const [locationText, setLocationText] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const dayfirsttime = days[0]?.time.split('-')[0];
  const dayendtime = days[days.length - 1]?.time.split('-')[1];
  const nightstarttime = nights[0]?.time.split('-')[0];
  const nightendtime = nights[nights.length - 1]?.time.split('-')[1];
  const [cityName, setCityName] = useState('');
  const [countryName, setCountryName] = useState('');
  const [lat, setLat] = useState('');
  const [long, setLong] = useState('');
  const [shortCodes, setShortCodes] = useState('');

  const [selected, setSelected] = useState('12');
  const [currentIndex, setCurrentIndex] = useState(0);
  const {width} = Dimensions.get('window');
  const dynamicMarginLeft = width * 0.070

    useEffect(() => {
      const storedlatlong = async () => {
        const locationlat = JSON.parse(await getKey(ASYNC_KEY_CONSTANTS.LOCATIONLAT))
        const locationlong = JSON.parse(await getKey(ASYNC_KEY_CONSTANTS.LOCATIONLONG))
        setLat(locationlat?.lat)
        setLong(locationlong?.long)
        if (locationlat?.lat && locationlong?.long) {
          fetchReverseGeocoding(locationlat.lat, locationlong.long);
        }else{
          Alert.alert("Note","Make sure your location is enabled")
        }
      }
  
      storedlatlong()
    },[]) 

  const fetchReverseGeocoding = async (latitude, longitude) => {
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${map_Access_Token}`;

    try {
      const response = await fetch(url);
      const data = await response.json();

      // Optionally, extract the address or place name from the response
      if (data.features && data.features.length > 0) {
        let city = '';
        let country = '';
        let short_codes = '';

        data.features[0].context.forEach(contextItem => {
          // Check for locality or place which indicates the city
          if (
            contextItem.id.includes('locality') ||
            contextItem.id.includes('place')
          ) {
            city = contextItem.text; // Extract city name
          }
          // Check for country
          if (contextItem.id.includes('country')) {
            country = contextItem.text; // Extract country name
          }

          if (contextItem.short_code) {
            short_codes = contextItem.short_code; // Extract country short code
          } else {
            short_codes = 'No short code available'; // Handle missing short code
          }
        });


        setCityName(city);
        setCountryName(country);
        setShortCodes(short_codes);
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching reverse geocoding data:', error);
    }
  };

  const locationmerge = () => {
    const mergeLocation = `${cityName}, ${countryName}`;
    setLocationText(mergeLocation);
  };

  useEffect(() => {
    if (cityName && countryName) {
      locationmerge();
    }
  }, [cityName, countryName]);

  const convert24HourFormat = timeString => {
    const [hours, minutes] = timeString.split(':').map(Number);

    // If the hour is greater than or equal to 24, subtract 24 to bring it to the correct time
    if (hours >= 24) {
      const newHour = hours - 24;
      return `${String(newHour).padStart(2, '0')}:${String(minutes).padStart(
        2,
        '0',
      )}`;
    }

    // Otherwise, return the time as is
    return timeString;
  };

  const getNextDateAndMonth = (
    timeString,
    is24HourFormat,
    selecteddate,
    language
  ) => {
 
    const selectedMoment = moment(selecteddate);
    const time = moment(timeString, is24HourFormat ? 'HH:mm' : 'hh:mm A');



    // Handle cases where time crosses midnight (00:00) and after
    if (time.hours() >= 0 && time.hours() <= 8) {
      const nextDay = selectedMoment.add(1, 'day');
      const nextDate = nextDay.format('DD');
      const monthName = nextDay.format('MMMM');
      return `${monthName} ${nextDate}`;
    }

    return null;
  };

  const toggleDatePicker = () => {
    setOpen(!open);
  };

  const handleConfirm = date => {
    setDate(date);
    toggleDatePicker();
  };

  const formatDate = date => {
    if (date) {
      return date.toLocaleDateString('hi-IN', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    }
    return 'Select a date';
  };

  // Function to handle toggle selection
  const handleToggle = value => {
    setSelected(value);
    // Getmuharatdata(value);
  };

  // const muhuratTimmingRenderitem = ({item, index}) => {
  //    return (
  //       <>
  //       <View style={{flexDirection: 'row'}}>
  //         <Text style={[styles.muhuratText, {color: item.muhuratTextColor}]}>
  //           {item.muhurta}
  //         </Text>
  //         <View style={styles.muhuratRenderTimeContanier}>
  //           <Text style={styles.muhuratStartEndTime}>
  //             {/* {item.muhuratTimeStart} */}
  //             {item.time.split('-')[0]}
  //             {/* {convertTo12HourFormat(item.time.split('-')[0])} */}

  //           </Text>
  //           <Text style={styles.muhuratTO}>to</Text>
  //           <Text style={styles.muhuratStartEndTime}>
  //             {item.time.split('-')[1]}
  //           </Text>
  //         </View>
  //       </View>
  //       {isDayMode ? muhurats.days && index < muhurats.days.length - 1 && (
  //         <View style={styles.muhuratInsideDivider} />
  //       ) :
  //        muhurats.nights && index < muhurats.nights.length - 1 && (
  //         <View style={styles.muhuratInsideDivider} />
  //        )
  //       }
  //     </>
  //    );
  // };

  // const muhuratTimmingRenderitem = ({item, index}) => {
  //   const [startTime, endTime] = item.time.split(' - ');

  //   const startDateMonth = getNextDateAndMonth(startTime);
  //   const endDateMonth = getNextDateAndMonth(endTime);

  //   return (
  //     <>
  //       <View style={{flexDirection: 'row'}}>
  //         <Text style={[styles.muhuratText, {color: item.muhuratTextColor}]}>
  //           {item.muhurta}
  //         </Text>
  //         <View style={styles.muhuratRenderTimeContanier}>
  //           <Text style={styles.muhuratStartEndTime}>
  //             {/* Convert start time to 12-hour format */}
  //             {convertTo12HourFormat(startTime)}
  //             {startDateMonth && `, ${startDateMonth}`}
  //           </Text>
  //           <Text style={styles.muhuratTO}>to</Text>
  //           <Text style={styles.muhuratStartEndTime}>
  //             {/* Convert end time to 12-hour format */}
  //             {convertTo12HourFormat(endTime)}
  //             {endDateMonth && `, ${endDateMonth}`}
  //           </Text>
  //         </View>
  //       </View>
  //       {isDayMode
  //         ? muhurats.days &&
  //           index < muhurats.days.length - 1 && (
  //             <View style={styles.muhuratInsideDivider} />
  //           )
  //         : muhurats.nights &&
  //           index < muhurats.nights.length - 1 && (
  //             <View style={styles.muhuratInsideDivider} />
  //           )}
  //     </>
  //   );
  // };

  // const muhuratTimmingRenderitem = ({item, index}) => {
  //   const [startTime, endTime] = item.time.split(' - ');

  //   const startDateMonth = getNextDateAndMonth(startTime, selected === '24');
  //   const endDateMonth = getNextDateAndMonth(endTime, selected === '24');
  //   return (
  //     <>
  //       <View style={{flexDirection: 'row'}}>
  //         <Text style={[styles.muhuratText, {color: item.muhuratTextColor}]}>
  //           {item.muhurta}
  //         </Text>
  //         <View style={styles.muhuratRenderTimeContanier}>
  //           <Text style={styles.muhuratStartEndTime}>
  //             {/* Convert start time based on selected format */}
  //             {startTime}
  //             {startDateMonth && `, ${startDateMonth}`}
  //           </Text>
  //           <Text style={styles.muhuratTO}>to</Text>
  //           <Text style={styles.muhuratStartEndTime}>
  //             {/* Convert end time based on selected format */}
  //             {endTime}
  //             {endDateMonth && `, ${endDateMonth}`}
  //           </Text>
  //         </View>
  //       </View>
  //       {isDayMode
  //         ? muhurats.days &&
  //           index < muhurats.days.length - 1 && (
  //             <View style={styles.muhuratInsideDivider} />
  //           )
  //         : muhurats.nights &&
  //           index < muhurats.nights.length - 1 && (
  //             <View style={styles.muhuratInsideDivider} />
  //           )}
  //     </>
  //   );
  // };

  const getMuhurtaTextColor = muhurta => {
    switch (muhurta) {
      case 'काल':
        return colors.muhuratText3;
      case 'शुभ':
        return colors.muhuratText2;
      case 'लाभ':
        return colors.muhuratText2;
      case 'रोग':
        return colors.muhuratText3;
      case 'राहु काल':
        return 'darkred';
      case 'उद्वेग':
        return colors.muhuratText3;
      case 'चर':
        return colors.muhuratText4;
      case 'अमृत':
        return colors.muhuratText1;
      default:
        return colors.muhuratText4;
    }
  };

  const muhuratTimmingRenderitem = ({item, index,isNightData }) => {
    console.log('item', item , 'index', index ,   'isNightData', isNightData);
    const [startTime, endTime] = item.time.split(' - ');
    const {language} = route?.params
    //Added recently
    // Apply conversion for times that exceed 24
    const convertedStartTime = convert24HourFormat(startTime);
    const convertedEndTime = convert24HourFormat(endTime);

    const startDateMonth = getNextDateAndMonth(
      convertedStartTime,
      selected === '24',
      date,
      language,
      isNightData 
    );

    let endDateMonth = null;
    if (isNightData) {
    endDateMonth = getNextDateAndMonth(
      convertedEndTime,
      selected === '24',
      date,
      language,
      isNightData
    );
  }

    return (
      <>
        <View style={{flexDirection: 'row'}}>
          {/* <Text style={[styles.muhuratText, {color:  getMuhurtaTextColor(item.muhurat)}]}> */}
          <Text
            style={[
              styles.muhuratText,
              {color: getMuhurtaTextColor(item.muhurta)},
            ]}>
            {item.muhurta}
          </Text>

          {item.muhurta === 'काल' && (
            <Image
              style={[styles.iconImageStyle, styles.demon]}
              resizeMode="contain"
              source={images.demon}
            />
          )}

          <View style={styles.muhuratRenderTimeContanier}>
            <Text style={styles.muhuratStartEndTime}>
              {/* Display start time and next date if applicable */}
              {/* {startTime} */}
              {convertedStartTime}
              {/* {startDateMonth && `, ${startDateMonth}`} */}
            </Text>
            <Text style={styles.muhuratTO}>{I18n.t('to')}</Text>
            <Text style={styles.muhuratStartEndTime}>
              {/* Display end time and next date if applicable */}
              {/* {endTime} */}
              {convertedEndTime}
              {endDateMonth && `, ${endDateMonth}`}
            </Text>
          </View>
        </View>
        {isDayMode
          ? muhurats.days &&
            index < muhurats.days.length - 1 && (
              <View style={styles.muhuratInsideDivider} />
            )
          : muhurats.nights &&
            index < muhurats.nights.length - 1 && (
              <View style={styles.muhuratInsideDivider} />
            )}
      </>
    );
  };

  const Getmuharatdata = async selected => {
    const formattedDate = date.toISOString().split('T')[0];
    // const formattedDateForApi = date.toISOString().split('T')[0]; // 'YYYY-MM-DD'

    const raw = JSON.stringify({
      city_name: `${cityName}, ${shortCodes}`,
      date: formattedDate,
      latitude: lat,
      longitude: long,
      language: 'hi',
    });

    const payload = {
      url: `${api.muharat}?format=${selected}`,
      data: raw,
    };


    await PostServicesWithoutToken(payload)
      .then(res => {
        if (res) {
          const days = res?.data?.days.map(item => ({
            time: item.time,
            muhurta: item.muhurta,
          }));
          const nights = res?.data?.nights.map(item => ({
            time: item.time,
            muhurata: item.muhurta,
          }));

          setMuhurat(res?.data);
          setDays(days);
          setNights(nights);

        } else {
          console.log('Getting error fetching response');
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  useEffect(() => {
    Getmuharatdata(selected);
  }, [cityName, shortCodes, date, selected]);

  // Function to handle the right arrow click
  const handleRightArrowClick = () => {
    setCurrentIndex(prevIndex => {
      const newIndex = prevIndex + 1;

      // Switch between Day and Night mode if the index is even/odd
      if (newIndex % 2 === 0) {
        setIsDayMode(true);
      } else {
        setIsDayMode(false);
      }

      return newIndex;
    });
  };

  // Handle the left arrow click
  const handleLeftArrowClick = () => {
    setCurrentIndex(prevIndex => {
      const newIndex = Math.max(prevIndex - 1, 0);

      if (newIndex % 2 === 0) {
        setIsDayMode(true);
      } else {
        setIsDayMode(false);
      }

      return newIndex;
    });
  };

  const changeDate = direction => {
    const newDate = new Date(date);

    if (direction === 'next') {
      newDate.setDate(newDate.getDate() + 1);
    } else if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 1);
    }

    // Update the state with the new date
    setDate(newDate);
  };

  return (
    <View style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
      {/* app-title */}
      <View style={styles.apptitle}>
        <AppTitle
          title={I18n.t('muhurat')}
          leftIconPress={() => navigation.goBack()}
          // isLanguageIcon={true}
          isDropdownVisible={dropdownVisible}
          setDropdownVisible={setDropdownVisible}
          LanguageIconPress={() => console.log('language-icon-click')}
        />
      </View>

      {/* Calendar Input */}

      <TouchableOpacity
        style={{marginHorizontal: width > 700 ? 150 : 80}}
        onPress={toggleDatePicker}>
        <AppInput
          isEditable={false}
          leftIcon={
            <Image
              style={[styles.iconImageStyle,{right: width * 0.003}]}
              resizeMode="contain"
              source={images.calendarBlue}
            />
          }
          placeholderText={language === 'hi' ? formatDate(date) : moment(date).format('Do MMM YYYY')}
          placeholderColor={colors.dividerBlack}
        />
      </TouchableOpacity>

      {/* prev-day & next-day */}
      <View style={styles.prevNextContanier}>
        <TouchableOpacity
          style={styles.prevTouchable}
          onPress={() => changeDate('prev')}>
          <Image source={images.prevDay} style={styles.prevIconImage} />
          <Text
            style={[styles.prevNextText, {color: colors.poweredByTextColor}]}>
            {I18n.t('prev_day')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.prevTouchable}
          onPress={() => changeDate('next')}>
          <Text style={[styles.prevNextText, {color: colors.primary}]}>
            {I18n.t('next_day')}
          </Text>
          <Image
            resizeMode="contain"
            source={images.nextDay}
            style={styles.nextIconImage}
          />
        </TouchableOpacity>
      </View>

      {/* location */}
      <View style={{...margins.mT10}}>
        <AppInput
          isEditable={false}
          leftIcon={
            <Image
              style={styles.iconImageStyle}
              resizeMode="contain"
              source={images.locationBlue}
            />
          }
          placeholderText={
            loading
              ? language === 'hi'
                ? 'स्थान प्राप्त कर रहे हैं...'
                : 'Fetching Location...'
              : locationText
          }
          placeholderColor={colors.dividerBlack}
        />
      </View>

      {/* day-choghadiya */}
      <View style={styles.dayChoghadiyaContanier}>
        <Text style={styles.dayChoghaadiyaText}>
          {I18n.t('day_choghadiya')}
        </Text>

        {/* <View style={{marginLeft: width > 700 ? width * 0.300 : width < 400 ? width * 0.075 :  width * 0.026 }}> */}
        <View style={{
          marginLeft: width > 700
            ? width * 0.300  
            : width >= 400 && width <= 700
            ? width * 0.150  
            : width >= 375 && width < 400
            ? width * 0.030
            : width * 0.022  
          }}>
          <Image
            style={[
              styles.iconImageStyle,
              styles.muhuratclock,
              // {marginLeft: dynamicMarginLeft},
            ]}
            resizeMode="contain"
            source={images.muhurat_clock}
          />
        </View>

        <View style={styles.ToggleContanier}>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              selected === '12' ? styles.selected : styles.unselected,
            ]}
            onPress={() => handleToggle('12')}>
            <Text
              style={[
                styles.toggleText,
                selected === '12' ? styles.selectedText : styles.unselectedText,
              ]}>
              {I18n.t('12_hour')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.toggleButton,
              selected === '24' ? styles.selected : styles.unselected,
            ]}
            onPress={() => handleToggle('24')}>
            <Text
              style={[
                styles.toggleText,
                selected === '24' ? styles.selectedText : styles.unselectedText,
              ]}>
              {I18n.t('24_hour')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* divider */}
      <View style={styles.divider} />

      {/* color-symbol */}
      <View style={styles.colorSymbolContanier}>
        <View style={styles.singalSymbolContanier}>
          <Image
            resizeMode="contain"
            source={images.auspiciousColor}
            style={styles.symbolImg}
          />
          <Text style={styles.symbolText}>{I18n.t('auspicious')}</Text>
        </View>
        <View style={[styles.singalSymbolContanier,]}>
          <Image
            resizeMode="contain"
            source={images.normalColor}
            style={styles.symbolImg}
          />
          <Text style={styles.symbolText}>{I18n.t('normal')}</Text>
        </View>
        <View style={[styles.singalSymbolContanier]}>
          <Image
            resizeMode="contain"
            source={images.inauspiciousColor}
            style={styles.symbolImg}
          />
          <Text style={styles.symbolText}>{I18n.t('inauspicious')}</Text>
        </View>
        <View style={[styles.singalSymbolContanier]}>
          <Image
            resizeMode="contain"
            source={images.rahuKalaIcon}
            style={styles.symbolImg}
          />
          <Text style={styles.symbolText}>{I18n.t('rahu_kala')}</Text>
        </View>
      </View>

      {/* Slider card */}
      {/* <View
        style={{
          flexDirection: 'row',
          // backgroundColor:'gray',
          justifyContent: 'space-between',
          marginBottom: 5,
        }}>
        <TouchableOpacity
          onPress={handleLeftArrowClick}
          style={{
            height: 20,
            width: 20,
            backgroundColor: 'lightgray',
            borderRadius: 30,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image
            style={[styles.iconImageStyleArrow]}
            resizeMode="contain"
            source={images.backArrowBlack}
          />
        </TouchableOpacity>
        
        <Text style={[styles.dayNightText]}>
          {currentIndex % 2 === 0 ? 'Day' : 'Night'}
        </Text>

        <TouchableOpacity
          onPress={handleRightArrowClick}
          style={{
            height: 20,
            width: 20,
            backgroundColor: 'lightgray',
            borderRadius: 30,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image
            style={[styles.iconImageStyleArrowright]}
            resizeMode="contain"
            source={images.right_arrow_black}
          />
        </TouchableOpacity>
      </View> */}

      <View style={styles.muhuratdaynightcontainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.muhuratTimmingContanierscrollview}>
          {/* took this surya code inside scrollview which was outside the main view */}
          {loading ? (
            <View>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : (
            // {/* suryaimage - timming */}
            <View style={[styles.timmingContanier, {marginBottom: 5}]}>
              <Image
                source={images.surya}
                style={[
                  styles.suryaImageStyle,
                  {transform: isDayMode ? [] : [{rotate: '180deg'}]},
                ]}
              />

              {/* {isDayMode ? ( */}
              <Text style={styles.timeText}>
                {dayfirsttime} {I18n.t('to')} {dayendtime}
              </Text>
              {/* ) : ( */}
              {/* <Text style={styles.timeText}>
                    {nightstarttime} to {nightendtime}
                  </Text>
                )} */}
            </View>
          )}
          <View style={{gap: 5}}>
            <View style={styles.cardContainer}>
              {/* Day Card */}
              <View style={styles.card}>
                <Text style={styles.cardTitle}>{I18n.t('day')}</Text>
                <FlatList
                  data={muhurats.days}
                  // renderItem={muhuratTimmingRenderitem}
                  renderItem={({ item, index }) =>
                    muhuratTimmingRenderitem({ item, index, isNightData: false }) 
                   }
                  keyExtractor={(item, index) => index.toString()}
                />
              </View>
            </View>

            {/* sun set code with their night time data */}
            {loading ? (
              <View>
                <ActivityIndicator size="small" color={colors.primary} />
              </View>
            ) : (
              // {/* suryaimage - timming */}
              <View style={[styles.timmingContanier, {marginTop: 10}]}>
                <Image
                  source={images.surya}
                  style={[
                    styles.suryaImageStyle,
                    {transform: [{rotate: '180deg'}]},
                  ]}
                />
                <Text style={styles.timeText}>
                  {nightstarttime} {I18n.t('to')} {nightendtime}
                </Text>
              </View>
            )}

            <View style={[styles.cardContainer, {marginBottom: 20}]}>
              {/* Night Card */}
              <View style={styles.card}>
                <Text style={styles.cardTitle}>{I18n.t('night')}</Text>
                <FlatList
                  data={muhurats.nights}
                  // renderItem={muhuratTimmingRenderitem}
                  renderItem={({ item, index }) =>
                    muhuratTimmingRenderitem({ item, index, isNightData: true }) 
                   }
                  keyExtractor={(item, index) => index.toString()}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>

      {/* muhurat-timming-contanier */}
      {/* <View style={styles.muhuratTimmingContanier}>
        <FlatList
          data={isDayMode ? muhurats.days : muhurats.nights}
          renderItem={muhuratTimmingRenderitem}
          keyExtractor={(item, index) => index.toString()}
        />
      </View> */}

      <DatePicker
        modal
        mode="date"
        open={open}
        date={date}
        onConfirm={handleConfirm}
        onCancel={toggleDatePicker}
      />
    </View>
  );
};

export default MuhuratScreen;
