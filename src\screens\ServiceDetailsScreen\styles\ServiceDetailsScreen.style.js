import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mV16,
  },
  image: {
    height: DeviceUiInfo.moderateScale(203),
    overflow: 'hidden',
    borderRadius: DeviceUiInfo.moderateScale(15),
    // position: 'relative',
  },

  descriptionContanier: {
    ...margins.mT16,
    // borderWidth: 1,
  },
  descriptionText: {
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  descriptionSubtext: {
    ...margins.mT4,
    color: colors.poweredByTextColor,
    fontSize: fontSize.f15,
    fontFamily: fonts.MontserratMedium,
  },
  pujaSamagriContanier: {
    ...paddings.pV17,
    ...paddings.pH16,
    ...margins.mT16,
    // height: DeviceUiInfo.moderateScale(424),
    // backgroundColor: '#6C727814', //last two characters 14 represent 8% opacity
    backgroundColor: 'rgba(108, 114, 120, 0.08)',
    borderRadius: 20,
  },
  pujaSamagriText: {
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f16,
  },
  horizontalDivider: {
    backgroundColor: colors.dividerLightGrey,
    height: 1,
    ...margins.mV10,
  },
  samagriNameTextStyle: {
    ...margins.mV6,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f15,
    color: colors.poweredByTextColor,
  },
  whatsappIconContanier: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(75),
    right: DeviceUiInfo.moderateScale(20),
  },
  whatsappIconStyle: {
    height: DeviceUiInfo.moderateScale(64),
    width: DeviceUiInfo.moderateScale(64),
  },
  appBtn: {
    ...margins.mV13,
  },
});

export default styles;
