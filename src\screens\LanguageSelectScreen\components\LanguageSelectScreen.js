import React, {useState,useEffect} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  FlatList,
  TouchableOpacity,
  Image,
  Linking,
} from 'react-native';
import styles from '../styles/LanguageSelectScreen.style';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import {colors} from '../../../theme/colors';
import {images} from '../../../theme/images';
import AppRoutes from '../../../constants/AppRoutes';
import {getKey, saveKey} from '../../../helper/cookies';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';
import LoginModal from '../../../components/common/LoginModal';
import { customAppEvents } from '../../../analytics/AppEvents';

const LanguageSelectScreen = ({navigation}) => {
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [askForLogin, setAskForLogin] = useState(false);

  useEffect(() => {
    const fetchLanguage = async () => {
      try {
        const storedLanguage = await getKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE);
        if (storedLanguage) {
          const selectedLang = storedLanguage === 'en' ? I18n.t('english') : I18n.t('hindi');
          setSelectedLanguage(selectedLang);
          I18n.changeLanguage(storedLanguage);
        }else {
          setSelectedLanguage(I18n.t('english'));
          I18n.changeLanguage('en');
        }
      } catch (error) {
        console.error('Error loading language from AsyncStorage:', error);
      }
    };

    fetchLanguage();
  }, []);

  const handleLeftPress = () => {
    navigation.goBack();
  };

  const languageData = [
    {
      id: 1,
      language: I18n.t('english'),
      label : 'en'
    },
    {
      id: 2,
      language: I18n.t('hindi'),
      label: 'hi'
    },
  ];

  const LanguageRender = ({item}) => {
    return (
      <>
        <TouchableOpacity
          onPress={() => handleLanguageSelect(item.language)}
          style={styles.renderContanier}>
          <Text style={styles.renderText}>{item.language}</Text>
          <View style={styles.renderTickContanier}>
            <Image
              style={styles.renderTickStyle}
              resizeMode="contain"
              source={
                selectedLanguage === item.language
                  ? images.languageSelectTick
                  : images.languageNonselectTick
              }
            />
          </View>
        </TouchableOpacity>
        <View style={styles.divider} />
      </>
    );
  };

  const handleLanguageSelect = async (language) => {
 
    console.log('selected language in language screen222', language);
    let languageCode;
    if (language === 'English' || language === 'अंग्रेजी') {
      languageCode = 'en';
    } else if (language === 'Hindi' || language === 'हिंदी') {
      languageCode = 'hi';
    } else {
      console.log('Invalid language selected:', language);
      return;
    }

    setSelectedLanguage(languageCode);
    await saveKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE, languageCode);
    console.log('Language stored in AsyncStorage:', languageCode);
    I18n.changeLanguage(languageCode); 
    navigation.replace(AppRoutes.BOTTOM_NAV_BAR);
  };

  const handlewhatsapp = async () => {
 
     const phoneNumber = '+919724676277';
     const message = 'Hello, I Have A Query';
     const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
 
     Linking.canOpenURL(url)
       .then(supported => {
         if (supported) {
           Linking.openURL(url);
         } else {
           console.log('WhatsApp is not installed or the URL is not supported.');
         }
       })
       .catch(err => console.error('An error occurred', err));
   };

  return (
    <SafeAreaView style={styles.contanier}>
      <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

      {/* app-title */}
      <View style={styles.appTitleContanier}>
        <AppTitle title={I18n.t('language')} leftIconPress={handleLeftPress} />
      </View>

      {/* <FlatList
        data={languageData}
        renderItem={languageRender}
        keyExtractor={(item, index) => item.id.toString()}
      /> */}

      
      {/* FlatList for rendering language options */}
      <FlatList
        data={languageData}
        renderItem={({ item }) => <LanguageRender item={item} />}
        keyExtractor={(item) => item.id.toString()}
      />

      {/* {languageData.map((item, index) => {
        return <LanguageRender key={index} item={item} index={index}  />;
      })} */}

      {/* whatsapp-icon */}
      <TouchableOpacity
        onPress={handlewhatsapp}
        style={styles.whatsappIconContanier}>
        <Image source={images.whatsappIcon} style={styles.whatsappIconStyle} />
      </TouchableOpacity>

      <LoginModal 
        visible={askForLogin}
        onClose={() => setAskForLogin(false)}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default LanguageSelectScreen;