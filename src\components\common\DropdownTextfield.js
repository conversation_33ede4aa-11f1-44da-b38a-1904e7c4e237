import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Image,
  TouchableOpacity,
  FlatList,
  ScrollView,
} from 'react-native';
import {fonts, fontSize} from '../../theme/fonts';
import {colors} from '../../theme/colors';
import metrics from '../../theme/metrics';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import {images} from '../../theme/images';

const {margins, paddings} = metrics;

const DropdownTextfield = ({
  label,
  requirement = false,
  options,
  dropdownContanierStyle,
  value,
  onSelect,
  placeholderText,
  placeholderColor,
  isDropdownOpen,
  onDropdownToggle,
}) => {
  // const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // const handleDropdownToggle = useCallback(() => {
  //   setIsDropdownOpen(!isDropdownOpen);
  // }, [isDropdownOpen]);

  const searchInputRef = useRef(null);

  useEffect(() => {
    if (isDropdownOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isDropdownOpen]);

  const [searchQuery, setSearchQuery] = useState('');

  const handleSearchChange = text => {
    setSearchQuery(text);
  };

  const filteredOptions = options.filter(item =>
    item.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleDropdownToggle = () => {
    if (isDropdownOpen) {
      setSearchQuery('');
    }
    onDropdownToggle();
  };

  const handleSelectItem = useCallback(
    item => {
      onSelect(item);
      // setIsDropdownOpen(false);
      onDropdownToggle();
      setSearchQuery('');
    },
    [onSelect],
  );

  const renderItemFun = useCallback(
    ({item}) => {
      return (
        <TouchableOpacity
          onPress={() => handleSelectItem(item)}
          style={styles.dropdownItem}>
          <Text style={styles.dropdownItemText}>{item}</Text>
        </TouchableOpacity>
      );
    },
    [handleSelectItem],
  );

  return (
    <View style={styles.contanier}>
      <View style={styles.labelContanier}>
        <Text style={styles.labelStyle}>{label}</Text>
        {requirement && <Text style={styles.requirementText}>*</Text>}
      </View>

      <TouchableOpacity
        onPress={handleDropdownToggle}
        style={styles.textinputContanier}>
        <TextInput
          value={value}
          style={styles.textInputStyle}
          placeholder={placeholderText}
          placeholderTextColor={
            placeholderColor ? placeholderColor : colors.dividerBlack
          }
          editable={false}
        />
        <View style={styles.iconContanier}>
          <Image style={styles.iconStyle} source={images.arrowDown} />
        </View>
      </TouchableOpacity>

      {isDropdownOpen && (
        <View
          style={[
            dropdownContanierStyle
              ? dropdownContanierStyle
              : styles.dropdownContanier,
          ]}>
          {/* Add search input inside the dropdown */}
          <TextInput
            ref={searchInputRef}
            style={styles.searchInput}
            placeholder="Search..."
            placeholderTextColor={colors.dividerBlack}
            value={searchQuery}
            onChangeText={handleSearchChange}
          />
          <FlatList
            data={filteredOptions}
            renderItem={renderItemFun}
            keyExtractor={(item, index) => index.toString()}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled={true}
          />
        </View>
      )}
    </View>
  );
};

export default DropdownTextfield;

const styles = StyleSheet.create({
  contanier: {},
  labelContanier: {
    flexDirection: 'row',
  },
  labelStyle: {
    fontSize: fontSize.f12,
    color: colors.poweredByTextColor,
    fontFamily: fonts.MontserratMedium,
  },
  requirementText: {
    color: colors.requireRedColor,
    fontSize: fontSize.f12,
    ...margins.mL2,
  },
  textinputContanier: {
    ...margins.mT2,
    // borderWidth: 1,
    flexDirection: 'row',
    width: '100%',
    backgroundColor: colors.white,
    borderRadius: 100,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
    elevation: 4,
    alignItems: 'center',
  },
  textInputStyle: {
    // borderWidth: 2,
    ...paddings.pH14,
    flex: 1,
    flexDirection: 'row',
    // width: '100%',
    ...margins.mT2,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    backgroundColor: colors.white,
    borderRadius: 100,
    elevation: 4,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
  },
  iconContanier: {
    // borderWidth: 1,
    ...margins.mL5,
    ...margins.mR14,
  },
  iconStyle: {
    height: DeviceUiInfo.moderateScale(14),
    width: DeviceUiInfo.moderateScale(14),
  },
  dropdownContanier: {
    // borderWidth: 1,
    zIndex: 1,
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    borderRadius: DeviceUiInfo.moderateScale(20),
    // elevation: 4,
    backgroundColor: colors.white,
    maxHeight: DeviceUiInfo.moderateScale(200),
  },
  dropdownItem: {
    ...paddings.p10,
    // borderBottomWidth: 1,
    borderBottomColor: colors.dividerLightGrey,
  },
  dropdownItemText: {
    fontSize: fontSize.f14,
    fontFamily: fonts.MontserratMedium,
    color: colors.dividerBlack,
  },
  searchInput: {
    ...paddings.pH14,
    backgroundColor: colors.white,
    fontSize: fontSize.f14,
    color: colors.dividerBlack,
    borderTopLeftRadius: DeviceUiInfo.moderateScale(20),
    borderTopRightRadius: DeviceUiInfo.moderateScale(20),
  },
});
