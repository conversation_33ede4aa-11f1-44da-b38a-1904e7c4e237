import React, { useEffect, useState } from 'react';
import {
    View,
    StatusBar,
    FlatList,
    Image,
    Text,
    TouchableOpacity,
    ActivityIndicator,
    Dimensions,
} from 'react-native';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import styles from '../styles/BlogScreen.style';
import { GetServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import HTML from 'react-native-render-html';
import AppRoutes from '../../../constants/AppRoutes';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const { width } = Dimensions.get('window');

const BlogScreen = ({ navigation, route , isHorizontal , isShowTitle }) => {
    var api = new Apis();
    const [blogs, setBlogs] = useState([]);
    const [loading, setLoading] = useState(true);

    console.log('isHorizontal:', isHorizontal);

    // if ishowtitle doesn't exist, set it to true by default
    isShowTitle = isShowTitle === undefined ? true : isShowTitle;
    isHorizontal = isHorizontal === undefined ? false : isHorizontal;

    useEffect(() => {
        fetchBlogs();
    }, []);

    const fetchBlogs = async () => {
        try {
            console.log('Fetching blogs...');
            const payload = {
                url: `${api.posts}?_embed`,
            };
            console.log('API URL:', payload.url);
            const response = await GetServices(payload);
            console.log('Raw API Response:', response);

            if (response?.success && response?.data) {
                console.log('Setting blogs data:', response.data);
                setBlogs(response.data);
            } else {
                console.log('No data received from API or API call failed');
                console.log('Response:', response);
            }
        } catch (error) {
            console.error('Error fetching blogs:', error);
        } finally {
            setLoading(false);
        }
    };

    const renderBlogItem = ({ item }) => {
        console.log('Rendering blog item:', item);
        // Extract the first paragraph from content
        const content = item.content?.rendered || '';
        const excerpt = item.excerpt?.rendered || '';

        const imageUrl = item._embedded?.['wp:featuredmedia']?.[0]?.source_url;
        console.log('Image URL:', imageUrl);
        // If is horizontal then show only image else show as
        return (
            <View
                style={[
                    styles.blogItem,
                    isHorizontal && styles.blogItemHorizontal
                ]}
            >
                {imageUrl ? (
                    <Image
                        source={{ uri: imageUrl }}
                        style={[
                            styles.blogImage,
                            isHorizontal && styles.blogImageHorizontal
                        ]}
                        resizeMode="cover"
                    />
                ) : (
                    <View style={[
                        styles.blogImage,
                        isHorizontal && styles.blogImageHorizontal,
                        { backgroundColor: colors.grey }
                    ]} />
                )}
                <View style={[
                    styles.blogContent,
                    isHorizontal && styles.blogContentHorizontal
                ]}>
                    <Text style={[
                        styles.blogTitle,
                        isHorizontal && styles.blogTitleHorizontal
                    ]} numberOfLines={isHorizontal ? 1 : null}>
                        {item.title?.rendered?.replace(/&#8211;/g, '-')}
                    </Text>
                    <HTML
                        source={{ html: excerpt }}
                        contentWidth={width - 40}
                        tagsStyles={{
                            p: styles.blogExcerpt
                        }}
                    />
                    <View style={{
                        flexDirection: 'row',
                        justifyContent: isHorizontal ? 'center' : 'space-between',
                        flex: 1,
                        alignItems: isHorizontal ? 'flex-end' : 'center'
                    }}>
                        <Text style={styles.blogDate}>
                            {new Date(item.date).toLocaleDateString()}
                        </Text>
                        <TouchableOpacity
                            onPress={() => navigation.navigate(AppRoutes.BLOG_DETAIL_SCREEN, { blog: item })}>
                            <Text style={[
                                styles.readmore,
                                isHorizontal && styles.readmoreHorizontal
                            ]}>
                                {I18n.t('read_more')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
            {isShowTitle && (
                <View style={styles.apptitle}>
                    <AppTitle
                        title={I18n.t('our_blogs')}
                        leftIconPress={() => navigation.goBack()}
                    />
                </View>
            )}

            {loading ? (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                </View>
            ) : blogs.length > 0 ? (
                <FlatList
                    data={blogs}
                    style={{marginHorizontal:20}}
                    renderItem={renderBlogItem}
                    keyExtractor={(item) => item.id.toString()}
                    contentContainerStyle={[
                        styles.blogList,
                        isHorizontal && styles.blogListHorizontal
                    ]}
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    horizontal={isHorizontal}
                    ItemSeparatorComponent={isHorizontal ? () => (
                        <View style={{height: DeviceUiInfo.moderateScale(10)}} />
                    ) : null}
                />
            ) : (
                <View style={styles.loadingContainer}>
                    <Text>No blogs found</Text>
                </View>
            )}
        </View>
    );
};

export default BlogScreen;
