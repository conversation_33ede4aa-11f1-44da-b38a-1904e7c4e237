import React, { useEffect, useState } from 'react';
import {
    View,
    StatusBar,
    FlatList,
    Image,
    Text,
    TouchableOpacity,
    ActivityIndicator,
    Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import styles from '../styles/BlogScreen.style';
import { GetServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import HTML from 'react-native-render-html';
import AppRoutes from '../../../constants/AppRoutes';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const { width } = Dimensions.get('window');

const BlogScreen = ({ navigation, route, isHorizontal, isShowTitle }) => {
    const navigationHook = useNavigation();
    const nav = navigation || navigationHook; // Use prop navigation or hook as fallback

    var api = new Apis();
    const [blogs, setBlogs] = useState([]);
    const [loading, setLoading] = useState(true);

    // Get parameters from route (similar to ServiceListScreen pattern)
    const routeParams = route?.params || {};
    const {
        isHorizontal: routeIsHorizontal,
        isShowTitle: routeIsShowTitle,
        blogList: routeBlogList,
        title: routeTitle
    } = routeParams;

    // Use route params if available, otherwise use props, otherwise use defaults
    const finalIsHorizontal = routeIsHorizontal !== undefined ? routeIsHorizontal : (isHorizontal !== undefined ? isHorizontal : false);
    const finalIsShowTitle = routeIsShowTitle !== undefined ? routeIsShowTitle : (isShowTitle !== undefined ? isShowTitle : true);
    const finalTitle = routeTitle || I18n.t('our_blogs') || 'Our Blogs';

    console.log('isHorizontal:', finalIsHorizontal);
    console.log('Route params:', routeParams);

    useEffect(() => {
        fetchBlogs();
    }, []);

    const fetchBlogs = async () => {
        try {
            console.log('Fetching blogs...');
            const payload = {
                url: `${api.posts}?_embed`,
            };
            console.log('API URL:', payload.url);
            const response = await GetServices(payload);
            console.log('Raw API Response:', response);

            if (response?.success && response?.data) {
                console.log('Setting blogs data:', response.data);
                setBlogs(response.data);
            } else {
                console.log('No data received from API or API call failed');
                console.log('Response:', response);
            }
        } catch (error) {
            console.error('Error fetching blogs:', error);
        } finally {
            setLoading(false);
        }
    };

    const renderBlogItem = ({ item }) => {
        console.log('Rendering blog item:', item);

        // Check if this is the "See More" item
        if (item.isSeeMore) {
            return (
                <View style={{ marginRight: DeviceUiInfo.moderateScale(16) }}>
                    <TouchableOpacity
                        onPress={() => nav.navigate(AppRoutes.BLOGSCREEN, {
                            isHorizontal: false,
                            isShowTitle: true,
                            blogList: blogs,
                            title: I18n.t('our_blogs') || 'Our Blogs'
                        })}
                        style={[
                            styles.blogImageHorizontal,
                            {
                                width: DeviceUiInfo.moderateScale(160),
                                backgroundColor: colors.primary,
                                justifyContent: 'center',
                                alignItems: 'center',
                                borderRadius: 12
                            }
                        ]}>
                        <Text style={{
                            color: colors.white,
                            fontSize: 16,
                            fontWeight: 'bold',
                            textAlign: 'center'
                        }}>
                            {I18n.t('see_more') || 'See More'}
                        </Text>
                    </TouchableOpacity>
                </View>
            );
        }

        // Extract the first paragraph from content
        const content = item.content?.rendered || '';
        const excerpt = item.excerpt?.rendered || '';

        const imageUrl = item._embedded?.['wp:featuredmedia']?.[0]?.source_url;
        console.log('Image URL:', imageUrl);
        return finalIsHorizontal ? (
            // Show only image when horizontal with spacing
            <View style={{ marginRight: DeviceUiInfo.moderateScale(16) }}>
                {imageUrl ? (
                    <TouchableOpacity
                        onPress={() => nav.navigate(AppRoutes.BLOG_DETAIL_SCREEN, { blog: item })}>
                        <Image
                            source={{ uri: imageUrl }}
                            style={[
                                styles.blogImageHorizontal,
                                { width: DeviceUiInfo.moderateScale(240), height: DeviceUiInfo.moderateScale(160) }
                            ]}
                            resizeMode="cover"
                        />
                    </TouchableOpacity>
                ) : (
                    <View style={[
                        styles.blogImageHorizontal,
                        {
                            backgroundColor: colors.grey,
                            width: DeviceUiInfo.moderateScale(160)
                        }
                    ]} />
                )}
            </View>
        ) : (
            <View style={styles.blogItem}>
                {/* // Show everything when not horizontal */}
                <>
                    {imageUrl ? (
                        <Image
                            source={{ uri: imageUrl }}
                            style={styles.blogImage}
                            resizeMode="cover"
                        />
                    ) : (
                        <View style={[
                            styles.blogImage,
                            { backgroundColor: colors.grey }
                        ]} />
                    )}
                    <View style={styles.blogContent}>
                        <Text style={styles.blogTitle}>
                            {item.title?.rendered?.replace(/&#8211;/g, '-')}
                        </Text>
                        <HTML
                            source={{ html: excerpt }}
                            contentWidth={width - 40}
                            tagsStyles={{
                                p: styles.blogExcerpt
                            }}
                        />
                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            flex: 1,
                            alignItems: 'center'
                        }}>
                            <Text style={styles.blogDate}>
                                {new Date(item.date).toLocaleDateString()}
                            </Text>
                            <TouchableOpacity
                                onPress={() => nav.navigate(AppRoutes.BLOG_DETAIL_SCREEN, { blog: item })}>
                                <Text style={styles.readmore}>
                                    {I18n.t('read_more')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </>
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
            {finalIsShowTitle && (
                <View style={styles.apptitle}>
                    <AppTitle
                        title={finalTitle}
                        leftIconPress={() => nav.goBack()}
                    />
                </View>
            )}

            {loading ? (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                </View>
            ) : blogs.length > 0 ? (
                <FlatList
                    data={finalIsHorizontal ? [...blogs.slice(0, 4), { isSeeMore: true, id: 'see-more' }] : blogs}
                    style={{ marginHorizontal: 20 }}
                    // if horizontal, show 4 blog items + 1 "See More" item
                    renderItem={renderBlogItem}
                    keyExtractor={(item) => item.id.toString()}
                    contentContainerStyle={[
                        styles.blogList,
                        finalIsHorizontal && styles.blogListHorizontal
                    ]}
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    horizontal={finalIsHorizontal}
                    ItemSeparatorComponent={finalIsHorizontal ? () => (
                        <View style={{ height: DeviceUiInfo.moderateScale(10) }} />
                    ) : null}
                />
            ) : (
                <View style={styles.loadingContainer}>
                    <Text>No blogs found</Text>
                </View>
            )}
        </View>
    );
};

export default BlogScreen;
