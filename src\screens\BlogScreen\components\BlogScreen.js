import React, { useEffect, useState } from 'react';
import {
    View,
    StatusBar,
    FlatList,
    Image,
    Text,
    TouchableOpacity,
    ActivityIndicator,
    Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import styles from '../styles/BlogScreen.style';
import { GetServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import HTML from 'react-native-render-html';
import AppRoutes from '../../../constants/AppRoutes';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const { width } = Dimensions.get('window');

const BlogScreen = ({ navigation, route , isHorizontal , isShowTitle }) => {
    const navigationHook = useNavigation();
    const nav = navigation || navigationHook; // Use prop navigation or hook as fallback

    var api = new Apis();
    const [blogs, setBlogs] = useState([]);
    const [loading, setLoading] = useState(true);

    console.log('isHorizontal:', isHorizontal);

    // if ishowtitle doesn't exist, set it to true by default
    isShowTitle = isShowTitle === undefined ? true : isShowTitle;
    isHorizontal = isHorizontal === undefined ? false : isHorizontal;

    useEffect(() => {
        fetchBlogs();
    }, []);

    const fetchBlogs = async () => {
        try {
            console.log('Fetching blogs...');
            const payload = {
                url: `${api.posts}?_embed`,
            };
            console.log('API URL:', payload.url);
            const response = await GetServices(payload);
            console.log('Raw API Response:', response);

            if (response?.success && response?.data) {
                console.log('Setting blogs data:', response.data);
                setBlogs(response.data);
            } else {
                console.log('No data received from API or API call failed');
                console.log('Response:', response);
            }
        } catch (error) {
            console.error('Error fetching blogs:', error);
        } finally {
            setLoading(false);
        }
    };

    const renderBlogItem = ({ item }) => {
        console.log('Rendering blog item:', item);
        // Extract the first paragraph from content
        const content = item.content?.rendered || '';
        const excerpt = item.excerpt?.rendered || '';

        const imageUrl = item._embedded?.['wp:featuredmedia']?.[0]?.source_url;
        console.log('Image URL:', imageUrl);
        return isHorizontal ? (
            // Show only image when horizontal - no container wrapper
            imageUrl ? (
                <TouchableOpacity
                    onPress={() => nav.navigate(AppRoutes.BLOG_DETAIL_SCREEN, { blog: item })}>
                <Image
                    source={{ uri: imageUrl }}
                    style={[
                        styles.blogImageHorizontal,
                        { width: DeviceUiInfo.moderateScale(160) }
                    ]}
                    resizeMode="cover"
                />
                </TouchableOpacity>
            ) : (
                <View style={[
                    styles.blogImageHorizontal,
                    {
                        backgroundColor: colors.grey,
                        width: DeviceUiInfo.moderateScale(160)
                    }
                ]} />
            )
        ) : (
            <View style={styles.blogItem}>
                    {/* // Show everything when not horizontal */}
                    <>
                        {imageUrl ? (
                            <Image
                                source={{ uri: imageUrl }}
                                style={styles.blogImage}
                                resizeMode="cover"
                            />
                        ) : (
                            <View style={[
                                styles.blogImage,
                                { backgroundColor: colors.grey }
                            ]} />
                        )}
                        <View style={styles.blogContent}>
                            <Text style={styles.blogTitle}>
                                {item.title?.rendered?.replace(/&#8211;/g, '-')}
                            </Text>
                            <HTML
                                source={{ html: excerpt }}
                                contentWidth={width - 40}
                                tagsStyles={{
                                    p: styles.blogExcerpt
                                }}
                            />
                            <View style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                flex: 1,
                                alignItems: 'center'
                            }}>
                                <Text style={styles.blogDate}>
                                    {new Date(item.date).toLocaleDateString()}
                                </Text>
                                <TouchableOpacity
                                    onPress={() => nav.navigate(AppRoutes.BLOG_DETAIL_SCREEN, { blog: item })}>
                                    <Text style={styles.readmore}>
                                        {I18n.t('read_more')}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </>
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
            {isShowTitle && (
                <View style={styles.apptitle}>
                    <AppTitle
                        title={I18n.t('our_blogs')}
                        leftIconPress={() => nav.goBack()}
                    />
                </View>
            )}

            {loading ? (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                </View>
            ) : blogs.length > 0 ? (
                <FlatList
                    data={blogs}
                    style={{marginHorizontal:20}}
                    renderItem={renderBlogItem}
                    keyExtractor={(item) => item.id.toString()}
                    contentContainerStyle={[
                        styles.blogList,
                        isHorizontal && styles.blogListHorizontal
                    ]}
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    horizontal={isHorizontal}
                    ItemSeparatorComponent={isHorizontal ? () => (
                        <View style={{height: DeviceUiInfo.moderateScale(10)}} />
                    ) : null}
                />
            ) : (
                <View style={styles.loadingContainer}>
                    <Text>No blogs found</Text>
                </View>
            )}
        </View>
    );
};

export default BlogScreen;
