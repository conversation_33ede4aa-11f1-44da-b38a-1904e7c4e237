// If any value requirement, then pass true and pass the onPressed method.

import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions, Alert } from 'react-native';
import { images } from '../../theme/images';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import { fonts, fontSize } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import metrics from '../../theme/metrics';
import Icon from 'react-native-vector-icons/Entypo';
import { getKey } from '../../helper/cookies';
import App from '../../../App';
import { ASYNC_KEY_CONSTANTS } from '../../constants/AppConstants';
import { translateText } from '../../services/translation';
import AsyncStorage from '@react-native-async-storage/async-storage';


const { margins, paddings } = metrics;
const { height, width } = Dimensions.get('window')

const AppTitle = ({
  title,
  isLeftIcon = true,
  leftIconPress,
  isSearchIcon = false,
  isLanguageIcon = false,
  isDownloadIcon = false,
  isCalendarIcon = false,
  searchIconPress,
  LanguageIconPress,
  DownloadIconPress,
  CalendarIconPress,
  selectedLanguage,
  isDropdownVisible,
  setDropdownVisible,
  onLanguageSelect,
}) => {
  const handleLanguageSelection = (languageCode) => {
    const fullLanguageName = languageCode === 'EN' ? 'English' : 'Hindi';
    onLanguageSelect(fullLanguageName);
    setDropdownVisible(false);
  };

  const [translatedTitle, setTranslatedTitle] = useState("");

  useEffect(()=>{
    const translate = async() =>{
      const newtitle = await translateText(title);
      setTranslatedTitle(title)
    }
    translate();

  })

  return (
    <View style={styles.contanier}>
      {isLeftIcon && (
        <TouchableOpacity
          onPress={leftIconPress}
          style={styles.backArrowContanier}>
          <Image source={images.backArrow} style={styles.backArrowStyle} />
        </TouchableOpacity>
      )}

      {/* title */}
      <Text style={styles.titleStyle}>{translatedTitle}</Text>

      {/* search */}
      {isSearchIcon && (
        <TouchableOpacity
          onPress={searchIconPress}
          style={styles.searchIconContanier}>
          <Image source={images.SearchIcon} style={styles.searchIconStyle} />
        </TouchableOpacity>
      )}

      {/*calende & language*/}
      {/*language & download*/}
      {/*language */}
      {isCalendarIcon && isLanguageIcon ? (
        <View
          style={{ flexDirection: 'row', justifyContent: 'flex-end', flex: 1 }}>
          <TouchableOpacity
            style={[styles.languageContanier, { ...margins.mR10 }]}
            onPress={CalendarIconPress}>
            <Image
              source={images.CalendarIconGrey}
              style={styles.headerIconStyle}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.languageContanier}
            onPress={LanguageIconPress}>
            <Image
              source={images.LanguageSelectIconGrey}
              style={styles.headerIconStyle}
            />
            <Text style={styles.languageText}>EN</Text>
            <Icon
              name="chevron-down"
              size={DeviceUiInfo.moderateScale(12)}
              color={colors.arrowDown}
            />
          </TouchableOpacity>
        </View>
      ) : isLanguageIcon && isDownloadIcon ? (
        <View
          style={{ flexDirection: 'row', justifyContent: 'flex-end', flex: 1 }}>
          <TouchableOpacity
            style={styles.languageContanier}
            onPress={LanguageIconPress}>
            <Image
              source={images.LanguageSelectIconGrey}
              style={styles.headerIconStyle}
            />
            <Text style={styles.languageText}>EN</Text>
            <Icon
              name="chevron-down"
              size={DeviceUiInfo.moderateScale(12)}
              color={colors.arrowDown}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.languageContanier, { ...margins.mL10 }]}
            onPress={DownloadIconPress}>
            <Image
              resizeMode="contain"
              source={images.downloadIcon}
              style={styles.headerIconStyle}
            />
          </TouchableOpacity>
        </View>
      ) : (
        isLanguageIcon && (
          <View
            style={{ flexDirection: 'row', justifyContent: 'flex-end', flex: 1 }}>
            <TouchableOpacity
              style={styles.languageContanier}
              onPress={() => setDropdownVisible(!isDropdownVisible)}
            // onPress={LanguageIconPress}
            >
              <Image
                source={images.LanguageSelectIconGrey}
                style={styles.headerIconStyle}
              />
              {/* <Text style={styles.languageText}>{selectedLanguage}</Text> */}
              <Text style={styles.languageText}>{selectedLanguage === 'English' ? 'EN' : 'HI'}</Text>
              <Icon
                name="chevron-down"
                size={DeviceUiInfo.moderateScale(12)}
                color={colors.arrowDown}
              />
            </TouchableOpacity>

            {/* Dropdown */}
            {isDropdownVisible && (
              <View style={styles.languageDropdown}>
                <TouchableOpacity onPress={() => handleLanguageSelection('HI')}>
                  <Text style={styles.languageOption}>Hindi</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => handleLanguageSelection('EN')}>
                  <Text style={styles.languageOption}>English</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )
      )}
    </View>
  );
};

export default AppTitle;

const styles = StyleSheet.create({
  contanier: {
    flexDirection: 'row',
    alignItems: 'center',
    // borderWidth: 1,
  },
  backArrowContanier: {
    // borderWidth: 1,
    ...margins.mR10,
  },
  backArrowStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  titleStyle: {
    fontSize: fontSize.f20,
    fontFamily: fonts.MontserratSemibold,
    color: colors.primary,
  },
  searchIconContanier: {
    // borderWidth: 1,
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  searchIconStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  languageContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'center',
    // borderWidth: 1,
    // ...margins.mR10,
  },
  headerIconStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  languageText: {
    fontSize: fontSize.f12,
    color: colors.primary,
    fontFamily: fonts.MontserratMedium,
    ...margins.mR2,
    ...margins.mL5,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  languageDropdown: {
    borderRadius: 5,
    borderWidth: 1,
    borderColor: colors.primary,
    position: 'absolute',
    top: 28,
    right: 0,
    zIndex: 999, //recent change did from 999 to 1 for panchang screen
    left: width * 0.33,
    backgroundColor: colors.background,
    width: width * 0.20
  },
  languageOption: {
    padding: 10,
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.MontserratRegular,
  },
});
