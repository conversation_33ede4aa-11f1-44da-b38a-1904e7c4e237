import React from 'react';
import {<PERSON>, Text, <PERSON><PERSON>rea<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rollView} from 'react-native';
import styles from '../styles/NakstrVivrunTab.styles';
import metrics from '../../../theme/metrics';

const NakstrVivrunTab = ({navigation,apidata}) => {
  const {margins, paddings} = metrics;

  const planetsData = apidata?.planets || [];

  const formatKey = key => {
    return key;
  };

  const renderTableHeader = dataItem => {
    return (
      <View style={styles.headerRow}>
        <View style={[styles.headerCell, {...paddings.pH3}]}>
          <Text style={styles.headerText}>Planet</Text>
        </View>
        <View style={styles.columnDivider} />
        <View style={[styles.headerCell, {...paddings.pH3}]}>
          <Text style={styles.headerText}>Sign</Text>
        </View>
        <View style={styles.columnDivider} />
        <View style={[styles.headerCell, {...paddings.pH3}]}>
          <Text style={styles.headerText}>Sign Lord</Text>
        </View>
        <View style={styles.columnDivider} />
        <View style={[styles.headerCell, {...paddings.pH33}]}>
          <Text style={styles.headerText}>Degree</Text>
        </View>
        <View style={styles.columnDivider} />
        <View style={[styles.headerCell, {...paddings.pH3}]}>
          <Text style={styles.headerText}>Nakshatra</Text>
        </View>
        <View style={styles.columnDivider} />
        <View style={[styles.headerCell, {...margins.pH3}]}>
          <Text style={styles.headerText}>House</Text>
        </View>
      </View>
    );
  };

  const renderTableRow = (planet, index, isLast) => {
    return (
      <View key={index}>
        <View style={styles.tableRow}>
          <View style={[styles.tableCell, {...paddings.pH14}]}>
            <Text style={styles.cellText}>{planet.name}</Text>
          </View>
          <View style={styles.columnDivider} />
          <View style={[styles.tableCell, {...paddings.pH10}]}>
            <Text style={styles.cellText}>{planet.sign}</Text>
          </View>
          <View style={styles.columnDivider} />
          <View style={[styles.tableCell, {...paddings.pH195}]}>
            <Text style={styles.cellText}>{planet.signLord}</Text>
          </View>
          <View style={styles.columnDivider} />
          <View style={[styles.tableCell, {...paddings.pH12}]}>
            <Text style={styles.cellText}>{planet.fullDegree}</Text>
          </View>
          <View style={styles.columnDivider} />
          <View style={[styles.tableCell, {...paddings.pH20}]}>
          <Text style={styles.cellText}>{planet.nakshatra}</Text>
          </View>
          <View style={styles.columnDivider} />
          <View style={[styles.tableCell, {...paddings.pH14}]}>
          <Text style={styles.cellText}>{planet.house}</Text>
          </View>
        </View>
        {!isLast && <View style={styles.divider} />}
      </View>
    );
  };

  const nakstrVivrunRender = ({item, index}) => {
    const chartData = planetsData;

    return (
      <View style={styles.chartContanier}>
        <Text style={styles.titleText}>Your Horoscope chart</Text>
        <View style={styles.tableContanier}>
          {renderTableHeader()}
          <ScrollView horizontal={true}>
            <View>
              {chartData.map((planet, index) =>
                renderTableRow(planet, index, index === chartData.length - 1),
              )}
            </View>
          </ScrollView>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.contanier}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={[{ title: 'Your Horoscope chart', chartData: planetsData }]}
        renderItem={nakstrVivrunRender}
        keyExtractor={(item, index) => index.toString()}
      />
    </SafeAreaView>
  );
};

export default NakstrVivrunTab;































// package code

// import React from 'react';
// import { View, Text, SafeAreaView, FlatList, ScrollView } from 'react-native';
// import { Table, TableWrapper, Row, Rows } from 'react-native-table-component'; // Import table components
// import styles from '../styles/NakstrVivrunTab.styles';
// import metrics from '../../../theme/metrics';

// const NakstrVivrunTab = ({ apidata }) => {
//   const { margins, paddings } = metrics;

//   // The planets data from apidata, destructuring it from apidata
//   const planetsData = apidata?.planets || [];

//   // Prepare the table header and data
//   const tableHead = ['Planet', 'Sign', 'Sign Lord', 'Degree', 'Nakshatra', 'House'];
  
//   // Mapping the planets data to table rows
//   const tableData = planetsData.map(planet => [
//     planet.name, 
//     planet.sign, 
//     planet.signLord, 
//     planet.fullDegree, 
//     planet.nakshatra, 
//     planet.house
//   ]);

//   // Function to render the Nakshatra Vivrun (Horoscope) table content
//   const nakstrVivrunRender = () => {
//     return (
//       <View style={styles.chartContanier}>
//         <Text style={styles.titleText}>Your Horoscope chart</Text>
//         <View style={styles.tableContanier}>
//           {/* Render the table */}
//           <Table borderStyle={{ borderWidth: 1, borderColor: '#ccc' }}>
//             {/* Table Header */}
//             <Row 
//               data={tableHead} 
//               flexArr={[2, 2, 2, 2, 2, 2]} 
//               style={styles.headerRow} 
//               textStyle={styles.headerText} 
//             />
//             {/* Table Rows */}
//             <TableWrapper style={styles.wrapper}>
//               <Rows 
//                 data={tableData} 
//                 flexArr={[2, 2, 2, 2, 2, 2]} 
//                 style={styles.row} 
//                 textStyle={styles.cellText} 
//               />
//             </TableWrapper>
//           </Table>
//         </View>
//       </View>
//     );
//   };

//   return (
//     <SafeAreaView style={styles.contanier}>
//       <FlatList
//         showsVerticalScrollIndicator={false}
//         data={[{ title: 'Your Horoscope chart', chartData: planetsData }]}
//         renderItem={nakstrVivrunRender}
//         keyExtractor={(item, index) => index.toString()}
//       />
//     </SafeAreaView>
//   );
// };

// export default NakstrVivrunTab;


