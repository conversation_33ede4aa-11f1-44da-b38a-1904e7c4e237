import { StyleSheet,Dimensions } from 'react-native'
import { colors } from '../../../theme/colors';
import metrics from '../../../theme/metrics';

const {height, width } = Dimensions.get('window');
const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    paddingHorizontal: width > 400 ? 24 : 10,
  },
  titleContanier: {
    ...margins.mT173,
  },
  phoneNumberContanier: {
    ...margins.mT34,
    ...paddings.pH8
  },
  appButtonContanier: {
    marginTop: height > 800 ? 50 : 35, 
    width: '100%',
    height: height > 800 ? 70 : 50,
    paddingHorizontal: width > 400 ? 30 : 15, 
  },
  
appTitleContanier: {
  ...margins.mT14,
  ...margins.mB16,
  left:5
}
})

export default styles;