import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import { Dimensions } from 'react-native';
import { fonts, fontSize } from '../../../theme/fonts';

const {width,height} = Dimensions.get('window');

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    paddingHorizontal: width > 400 ? 24 : 10,
    // ...paddings.pH24,
  },
  titleContanier: {
    ...margins.mT173,
  },
  phoneNumberContanier: {
    ...margins.mT34,
    ...paddings.pH8,
    marginBottom:2
  },
  appButtonContanier: {
    // ...margins.mT34,
    marginTop: height > 800 ? 50 : 35, 
    width: '100%',
    height: height > 800 ? 70 : 50,
    paddingHorizontal: width > 400 ? 30 : 15, 
  },
  forgotpassword:{
    ...paddings.p8,
    position:'absolute',
    right:'3%',
    top:'100%'    
  },
  forgottext:{
    fontSize:fontSize.f14,
    color: colors.black,
    fontFamily: fonts.MontserratMedium
  },
  eyeIconContainer: {
    position: 'absolute',
    right: 10,
    top: '50%', 
    transform: [{ translateY: -5 }], 
    padding: 5,
  },
});

export default styles;
