import {
  Image,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import styles from '../styles/Panchang.style';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import AppInput from '../../../components/common/AppInput';
import { images } from '../../../theme/images';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import Apis from '../../../services/apiList';
import axios from 'axios';
import GetLocation from 'react-native-get-location';
import { PostServicesWithoutToken } from '../../../services/commonApiMethod';
import {
  check,
  openSettings,
  PERMISSIONS,
  RESULTS,
} from 'react-native-permissions';
import { getKey, save<PERSON>ey } from '../../../helper/cookies';
import { ASYNC_KEY_CONSTANTS } from '../../../constants/AppConstants';

const { height, width } = Dimensions.get('window');
const URL = 'https://json.astrologyapi.com/';

const map_Access_Token =
  'pk.eyJ1IjoicGFydGgwMiIsImEiOiJjbTd4NWZ0bzcwMWxkMnFwZXRmeWgxbGZnIn0.y2KRmr1T5TBt938N9PVTHg';

const Panchang = ({ navigation, route }) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [selectLanguage, setSelectLanguage] = useState(language === 'en' ? 'English' : 'Hindi');
  const [open, setOpen] = useState(false);
  const currentDate = new Date();
  const [date, setDate] = useState(currentDate);
  const [isFetchingLocation, setIsFetchingLocation] = useState(true);
  const [isLoadingPanchang, setIsLoadingPanchang] = useState(true);
  const { language } = route?.params;

  //api body state
  const [day, setDay] = useState(moment(currentDate).format('DD'));
  const [month, setMonth] = useState(moment(currentDate).format('M'));
  const [year, setYear] = useState(moment(currentDate).format('YYYY'));
  const [lat, setLat] = useState(null);
  const [long, setLong] = useState(null);
  const [cityName, setCityName] = useState('');
  const [countryName, setCountryName] = useState('');
  const [shortCodes, setShortCodes] = useState('');
  const [tZone, setTZone] = useState(null);
  const [panchang, setPanchang] = useState([]);

  const api = new Apis();

  useEffect(() => {
    if (language) {
      I18n.changeLanguage(language).then(() => {
        setSelectLanguage(language === 'en' ? 'English' : 'Hindi');
      }).catch(error => {
        console.error('Error changing language: ', error);
      });
    }
  }, [language]);

  // ********************** Ui Data Via API ***********************************
  const leftData = [
    { question: I18n.t('ayana'), answer: panchang.ayana },
    { question: I18n.t('ritu'), answer: panchang.ritu },
    { question: I18n.t('sun_sign'), answer: panchang.sun_sign },
  ];

  const rightData = [
    { question: I18n.t('sunrise'), answer: panchang.vedic_sunrise },
    { question: I18n.t('sunset'), answer: panchang.vedic_sunset },
    { question: I18n.t('moon_sign'), answer: panchang.moon_sign },
  ];

  const cardData = [
    { question: I18n.t('vikram_samvat'), answer: `${panchang?.vikram_samvat} upto ${panchang?.vkram_samvat_name}` },
    { question: I18n.t('shaka_samvat'), answer: `${panchang?.shaka_samvat} upto ${panchang?.shaka_samvat_name}` },
    { question: I18n.t('paksha'), answer: panchang.paksha },
    { question: I18n.t('ayana'), answer: panchang.ayana },
    { question: I18n.t('purnimanta'), answer: panchang?.hindu_maah?.purnimanta },
    { question: I18n.t('amanta'), answer: panchang?.hindu_maah?.amanta },
    { question: I18n.t('sun_sign'), answer: panchang.sun_sign },
    { question: I18n.t('moon_sign'), answer: panchang.moon_sign },
  ];

  // const formattedTithiTime = moment({
  //   hour: panchang?.tithi?.end_time?.hour || 0,
  //   minute: panchang?.tithi?.end_time?.minute || 0,
  //   second: panchang?.tithi?.end_time?.second || 0,
  // }).format('hh:mm:ss A');

  const formattedTithiTime = moment(panchang?.tithi?.end_time_ms || 0).format('hh:mm:ss A');
  const formattedNakshatraTime = moment(panchang?.nakshatra?.end_time_ms || 0).format('hh:mm:ss A');
  const formattedYogTime = moment(panchang?.yog?.end_time_ms || 0).format('hh:mm:ss A');
  const formattedKaranTime = moment(panchang?.karan?.end_time_ms || 0).format('hh:mm:ss A');

  // const formattedNakshatraTime = moment({
  //   hour: panchang?.nakshatra?.end_time?.hour || 0,
  //   minute: panchang?.nakshatra?.end_time?.minute || 0,
  //   second: panchang?.nakshatra?.end_time?.second || 0,
  // }).format('hh:mm:ss A');

  // const formattedYogTime = moment({
  //   hour: panchang?.yog?.end_time?.hour || 0,
  //   minute: panchang?.yog?.end_time?.minute || 0,
  //   second: panchang?.yog?.end_time?.second || 0,
  // }).format('hh:mm:ss A');

  // const formattedKaranTime = moment({
  //   hour: panchang?.karan?.end_time?.hour || 0,
  //   minute: panchang?.karan?.end_time?.minute || 0,
  //   second: panchang?.karan?.end_time?.second || 0,
  // }).format('hh:mm:ss A');

  const formattedTithiData = moment(panchang?.tithi?.end_time_ms).format(
    'YYYY-MM-DD',
  );
  const formattedNakshatraData = moment(
    panchang?.nakshatra?.end_time_ms,
  ).format('YYYY-MM-DD');
  const formattedYogDate = moment(panchang?.yog?.end_time_ms).format(
    'YYYY-MM-DD',
  );
  const formattedKaranDate = moment(panchang?.karan?.end_time_ms).format(
    'YYYY-MM-DD',
  );

  const panchangdetails = [
    {
      question: I18n.t('tithi'),
      answer: `${panchang?.tithi?.details?.tithi_name} up to ${formattedTithiData} ${formattedTithiTime}`,
    },
    {
      question: I18n.t('nakshatra'),
      answer: `${panchang?.nakshatra?.details?.nak_name} up to ${formattedNakshatraData} ${formattedNakshatraTime}`,
    },
    {
      question: I18n.t('yog'),
      answer: `${panchang?.yog?.details?.yog_name} up to ${formattedYogDate} ${formattedYogTime}`,
    },
    {
      question: I18n.t('karan'),
      answer: `${panchang?.karan?.details?.karan_name} up to ${formattedKaranDate} ${formattedKaranTime}`,
    },
  ];

  const otheryogdetails = [
    { question: I18n.t('panchang_yog'), answer: panchang?.panchang_yog },
    { question: I18n.t('disha_shool'), answer: panchang?.disha_shool },
    { question: I18n.t('moon_nivas'), answer: panchang?.moon_nivas },
    { question: I18n.t('nakshatra_shool'), answer: panchang?.disha_shool },
    // {question: 'Nakshatra Shool', answer: panchang?.nak_shool?.direction || 'N/A'},
  ];
  // **********************************************************

  // ************************ language selection code **********
  const handleLanguageSelect = async language => {
    setDropdownVisible(false);
    setSelectLanguage(language);
  };

  // ****************************
  //************************ Date code ********************
  const toggleDatePicker = () => {
    setOpen(!open);
  };

  const handleConfirm = date => {
    setDate(date);

    const formattedDay = moment(date).format('DD');
    const formattedMonth = moment(date).format('M');
    const formattedYear = moment(date).format('YYYY');

    setDay(formattedDay);
    setMonth(formattedMonth);
    setYear(formattedYear);

    toggleDatePicker();
  };

  const changeDate = direction => {
    const newDate = new Date(date);

    if (direction === 'next') {
      newDate.setDate(newDate.getDate() + 1);
    } else if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 1);
    }

    // Update the state with the new date
    setDate(newDate);

    const updatedFormattedDay = moment(newDate).format('DD');
    const updatedFormattedMonth = moment(newDate).format('M');
    const updatedFormattedYear = moment(newDate).format('YYYY');


    setDay(updatedFormattedDay);
    setMonth(updatedFormattedMonth);
    setYear(updatedFormattedYear);
  };

  useEffect(() => {
    const fetchStoredLatLong = async () => {
      try {
        const locationLat = JSON.parse(await getKey(ASYNC_KEY_CONSTANTS.LOCATIONLAT));
        const locationLong = JSON.parse(await getKey(ASYNC_KEY_CONSTANTS.LOCATIONLONG));
  
        const lat = locationLat?.lat;
        const long = locationLong?.long;
  
        setLat(lat);
        setLong(long);
  
        if (lat && long) {
          await fetchReverseGeocoding(lat, long);
        } else {
          await requestPermission();
        }
      } catch (error) {
        console.error("Failed to retrieve location from storage:", error);
        await requestPermission();
      } finally {
        setIsFetchingLocation(false);
      }
    };
  
    fetchStoredLatLong();
  }, []);

  const requestPermission = async () => {
    try {
      const result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      console.log("Permission check result:", result);
      
      if (result === RESULTS.DENIED) {
        Alert.alert(
          'Location Permission Required',
          'Please enable location access to get accurate Panchang details.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Open Settings', 
              onPress: async () => {
                await openSettings();
                // Check permission again after returning from settings
                const result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
                if (result === RESULTS.GRANTED) {
                  await fetchLocation();
                }
              }
            }
          ]
        );
      } else if (result === RESULTS.BLOCKED) {
        Alert.alert(
          'Location Permission Blocked',
          'Location permission is blocked. Please enable it from Settings to get accurate Panchang details.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Open Settings', 
              onPress: async () => {
                await openSettings();
                // Check permission again after returning from settings
                const result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
                if (result === RESULTS.GRANTED) {
                  await fetchLocation();
                }
              }
            }
          ]
        );
      } else if (result === RESULTS.GRANTED) {
        await fetchLocation();
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      Alert.alert('Error', 'Failed to request location permission');
    }
  };

  const fetchLocation = async () => {
    try {

      console.log("fetching location.. ");

      const location = await GetLocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 60000,
      });

      if (lat !== location.latitude || long !== location.longitude) {
        setLat(location.latitude);
        setLong(location.longitude);
        
        await saveKey(ASYNC_KEY_CONSTANTS.LOCATIONLAT, JSON.stringify({ lat: location.latitude }));
        await saveKey(ASYNC_KEY_CONSTANTS.LOCATIONLONG, JSON.stringify({ long: location.longitude }));
        
        await fetchReverseGeocoding(location.latitude, location.longitude);
      }
    } catch (error) {
      console.error('Error fetching location:', error);
    }
  };


  const fetchReverseGeocoding = async (latitude, longitude) => {
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${map_Access_Token}`;

    try {
      const response = await fetch(url);
      const data = await response.json();

      if (data.features && data.features.length > 0) {
        let city = '';
        let country = '';
        // let short_codes = '';

        data.features[0].context.forEach(contextItem => {
          if (
            contextItem.id.includes('locality') ||
            contextItem.id.includes('place')
          ) {
            city = contextItem.text;
          }
          // Check for country
          if (contextItem.id.includes('country')) {
            country = contextItem.text;
          }
        });
        setCityName(city);
        setCountryName(country);
        setIsFetchingLocation(false);
      }
    } catch (error) {
      console.error('Error fetching reverse geocoding data:', error);
    }
  };

  useEffect(() => {
    if (lat && long) {
      fetchReverseGeocoding(lat, long);

      const tzoneAPI = async () => {
        var latlondata = new FormData();
        latlondata.append('latitude', lat);
        latlondata.append('longitude', long);

        const payload = {
          url: api.timezone,
          data: latlondata,
          type: 'formData',
        };

        await PostServicesWithoutToken(payload)
          .then(res => {
            if (res) {
              const tzone = res?.data?.data?.gmtOffset;
              setTZone(tzone);
            }
          })
          .catch(err => {
            console.log('Something is wrong', err);
          });
      };

      tzoneAPI();
    }
  }, [lat, long]);

  // ****************************

  // api calling panchang
  useEffect(() => {
    if (lat && long && tZone !== null) {
      const panchangAPI = async () => {
        setIsLoadingPanchang(true);
        const basicAuthKey =
          'Bearer NjEyNTExOmEzYTAxNGVlMzVmMTljN2M4ZGRmNDJiZDFiNzk3MmJi';

        const today = moment().startOf('day');
        const selectedDay = moment(date).startOf('day');

        const hour = selectedDay.isSame(today) ? moment().hour() : 0;
        const min = selectedDay.isSame(today) ? moment().minute() : 0;



        try {
          const response = await axios.post(
            `${URL}${api.panchang}`,
            {
              day: day,
              month: month,
              year: year,
              hour: hour,
              min: min,
              tzone: tZone,
              lat: Number(lat),
              lon: Number(long),
            },
            {
              headers: {
                Authorization: `${basicAuthKey}`,
                'Content-Type': 'application/json',
              },
            },
          );
          setPanchang(response?.data);
        } catch (error) {
          console.error(
            'Panchang API Error:',
            error.response?.data || error.message,
          );
        } finally {
          setIsLoadingPanchang(false);
        }
      };
      panchangAPI();
    }
  }, [date, tZone, lat, long, day, month, year]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', async () => {
      const result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      if (result === RESULTS.GRANTED) {
        await fetchLocation();
      }
    });

    return unsubscribe;
  }, [navigation]);

  return (
    <>
      {isLoadingPanchang ? (
        <View style={styles.indicator}>
          <ActivityIndicator size="large" color={colors.primary} />
          {/* <Text style={{marginTop:5, color:'#000', fontFamily: fonts.MontserratSemibold}}>{I18n.t('fetching_location')}</Text> */}
        </View>
      ) : (
        <ScrollView
          style={styles.container}
          contentContainerStyle={{ paddingBottom: 20 }}>
          <StatusBar backgroundColor={colors.background} barStyle="dark-content" />

          {/* title */}
          <View style={styles.apptitle}>
            <AppTitle
              title={I18n.t('panchang')}
              leftIconPress={() => navigation.goBack()}
              // isLanguageIcon={true}
              LanguageIconPress={() => console.log('language-icon-click')}
              isDropdownVisible={dropdownVisible}
              setDropdownVisible={setDropdownVisible}
              selectedLanguage={selectLanguage}
              onLanguageSelect={handleLanguageSelect}
            />
          </View>

          {/* location */}
          <View style={{ marginTop: 10 }}>
            <AppInput
              isEditable={false}
              leftIcon={
                <Image
                  style={styles.iconImageStyle}
                  resizeMode="cover"
                  source={images.locationyellow}
                />
              }
              //   placeholderText={
              //     loading
              //       ? language === 'hi'
              //         ? 'स्थान प्राप्त कर रहे हैं...'
              //         : 'Fetching Location...'
              //       : locationText
              //   }
              placeholderText={
                isFetchingLocation
                  ? I18n.t('fetching_location')
                  : `${cityName}, ${countryName}`
              }
              placeholderColor={colors.dividerBlack}
            />
          </View>

          {/* Header secondone */}
          <View style={styles.secondheader}>
            <View>
              <TouchableOpacity
                style={styles.prevcontainer}
                onPress={() => changeDate('prev')}>
                <Image
                  source={images.prevDay}
                  style={styles.prevIconImage}
                  resizeMode="contain"
                />
                <Text
                  style={[styles.prevNextText, { color: colors.poweredByTextColor }]}>
                  {I18n.t('prev_day')}
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity style={styles.dateinput} onPress={toggleDatePicker}>
              <AppInput
                isEditable={false}
                leftIcon={
                  <Image
                    resizeMode="contain"
                    style={[styles.iconImageStyle]}
                    source={images.Calendar}
                  />
                }
                placeholderText={moment(date).format('Do MMMM YYYY')}
                placeholderColor={colors.dividerBlack}
              />
            </TouchableOpacity>

            <DatePicker
              modal
              mode="date"
              open={open}
              date={date}
              onConfirm={handleConfirm}
              onCancel={toggleDatePicker}
            />

            <View>
              <TouchableOpacity
                style={styles.prevcontainer}
                onPress={() => changeDate('next')}>
                <Text style={[styles.prevNextText, { color: colors.primary }]}>
                  {I18n.t('next_day')}
                </Text>
                <Image
                  source={images.nextDay}
                  style={styles.nextIconImage}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* black box  */}
          <View style={styles.blackboxcontainer}>
            {/* backg image */}
            <Image
              source={images.panchangbg}
              style={styles.image}
              resizeMode="cover"
            />
            {/* transparent black */}
            <View style={styles.overlay} />

            {/* content of first 2 heading */}
            <View style={styles.contentOverlay}>
              <Text style={styles.datetext}>
                {moment(date).format('D MMMM YYYY')}
              </Text>

              <View style={styles.secondlinecontainer}>
                <View style={styles.firsttextcontent}>
                  <Text style={styles.secondtext}>{panchang?.hindu_maah?.purnimanta}, </Text>
                  <Text style={styles.secondtext}>{panchang?.paksha}</Text>
                </View>
                <Text style={[styles.secondtext, { marginRight: 2 }]}>-</Text>

                <View style={styles.firsttextcontent}>
                  <Text style={styles.secondtext}>{panchang?.tithi?.details?.tithi_name?.split(' ')[1]},</Text>
                  <Text style={styles.secondtext}> {panchang?.day}</Text>
                </View>
              </View>
              {/* gray divider */}
              <View style={styles.dividergray} />

              {/* box content details */}
              <View style={styles.detaincontainer}>
                <View>
                  {leftData.map((item, index) => (
                    <Text key={index} style={styles.rowtext}>
                      {item.question} -{' '}
                      <Text style={styles.answertext}>{item.answer}</Text>
                    </Text>
                  ))}
                </View>

                <View>
                  {rightData.map((item, index) => (
                    <Text key={index} style={styles.rowtext}>
                      {item.question} -{' '}
                      <Text style={styles.answertext}>{item.answer}</Text>
                    </Text>
                  ))}
                </View>
              </View>
            </View>
          </View>

          {/* below content */}

          <View style={styles.belowcontent}>
            {/* Hindu month and calender */}
            <View>
              <Text style={styles.calendertext}>{I18n.t('hindu_month_year')}</Text>
              <View style={styles.cardContainer}>
                {cardData.map((item, index) => (
                  <View key={index}>
                    <View style={styles.rowContainer}>
                      <Text style={styles.questionText}>{item.question}</Text>
                      <Text style={styles.answerText}>{item.answer}</Text>
                    </View>

                    {/* Divider after every row except last */}
                    {index < cardData.length - 1 && <View style={styles.divider} />}
                  </View>
                ))}
              </View>
            </View>

            {/* panchang details */}
            <View style={styles.containermargin}>
              <Text style={styles.calendertext}>{I18n.t('panchang_details')}</Text>
              <View style={styles.cardContainer}>
                {panchangdetails.map((item, index) => (
                  <View key={index}>
                    <View style={styles.rowContainerpanchang}>
                      <Text style={[styles.questionText, { marginBottom: 3 }]}>
                        {item.question}
                      </Text>
                      <Text style={styles.answerText}>{item.answer}</Text>
                    </View>

                    {/* Divider after every row except last */}
                    {index < panchangdetails.length - 1 && (
                      <View style={styles.divider} />
                    )}
                  </View>
                ))}
              </View>
            </View>

            {/* other yoga */}
            <View style={styles.containermargin}>
              <Text style={styles.calendertext}>{I18n.t('other_yoga')}</Text>
              <View style={styles.cardContainer}>
                {otheryogdetails.map((item, index) => (
                  <View key={index}>
                    <View style={styles.rowContainer}>
                      <Text style={styles.questionText}>{item.question}</Text>
                      <Text style={styles.answerText}>{item.answer}</Text>
                    </View>

                    {/* Divider after every row except last */}
                    {index < otheryogdetails.length - 1 && (
                      <View style={styles.divider} />
                    )}
                  </View>
                ))}
              </View>
            </View>
          </View>
        </ScrollView>
      )}
    </>
  );
};

export default Panchang;
