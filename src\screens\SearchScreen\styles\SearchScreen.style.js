import {StyleSheet} from 'react-native';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {fonts, fontSize} from '../../../theme/fonts';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  apptitleContanier: {
    ...margins.mV16,
  },
  textinputContanier: {
    // borderWidth: 1,
    ...paddings.pL14,
    ...paddings.pR65,
    ...margins.mB20,
    flexDirection: 'row',
    width: '100%',
    backgroundColor: colors.white,
    borderRadius: 100,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10,
    elevation: 4,
    alignItems: 'center',
  },
  textInputStyle: {
    // borderWidth: 1,
    ...paddings.pL10,
    width: '100%',
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
    backgroundColor: colors.white,
    borderRadius: 100,
    elevation: 4,
    shadowColor: colors.lightGrey,
    shadowOpacity: 10
  },
  searchDivider: {
    backgroundColor: colors.arrowDown,
    height: '50%',
    width: 1,
    ...margins.mH10,
  },
  iconContanier: {
    height: DeviceUiInfo.moderateScale(17),
    width: DeviceUiInfo.moderateScale(14),
  },
  topSearchesContanierText: {
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f18,
    color: colors.dividerBlack,
    ...margins.mB16,
  },
  contentContanier: {
    // borderWidth: 1,
    ...margins.mT24,
    // flex: 1,
  },
  card: {
    height: DeviceUiInfo.moderateScale(160),
    width: DeviceUiInfo.moderateScale(173),
    ...margins.mR10,
  },
  serviceImg: {
    width: '100%',
    height: '100%',
    borderRadius: DeviceUiInfo.moderateScale(15),
  },
  serviceRightArrowContanier: {
    position: 'absolute',
    right: DeviceUiInfo.moderateScale(9),
    top: DeviceUiInfo.moderateScale(9),
  },
  serviceRightArrowStyle: {
    height: DeviceUiInfo.moderateScale(24),
    width: DeviceUiInfo.moderateScale(24),
  },
  serviceText: {
    color: colors.white,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f12,
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(6),
    alignSelf: 'center',
  },
  sectionContanier: {
    ...margins.mT20,
  },
  noResultsText: {
    textAlign:'center',
    fontSize: 20,
    ...margins.mT44,
    color:colors.Grey,
    fontFamily:fonts.MontserratMedium
  },
  whatsappIconContanier: {
    position: 'absolute',
    bottom: DeviceUiInfo.moderateScale(12),
    right: DeviceUiInfo.moderateScale(20),
  },
  whatsappIconStyle: {
    height: DeviceUiInfo.moderateScale(64),
    width: DeviceUiInfo.moderateScale(64),
  },
  listeningOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // semi-transparent background
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  listeningText: {
    fontSize: 18,
    color: 'white',
    marginBottom: 10,
  },
});

export default styles;
