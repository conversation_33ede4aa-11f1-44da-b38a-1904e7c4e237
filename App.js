import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import AppNavigation from './src/navigation/AppNavigation';
import analytics from '@react-native-firebase/analytics';
import firebase from '@react-native-firebase/app';
import { getMessaging } from '@react-native-firebase/messaging';
import { getLocales } from 'react-native-localize'

import * as Clarity from '@microsoft/react-native-clarity';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveKey } from './src/helper/cookies';
import { ASYNC_KEY_CONSTANTS } from './src/constants/AppConstants';
import I18n from './src/utils/language/i18nextConfig';


const App = () => {

  const firebaseConfig = {
    apiKey: "AIzaSyCklEDyODA9RL_4i8Osy9go9LHJULY0cwc",
    authDomain: "panditsnearme-e07a3.firebaseapp.com",
    projectId: "panditsnearme-e07a3",
    storageBucket: "panditsnearme-e07a3.firebasestorage.app",
    messagingSenderId: "1054293479281",
    appId: "1:1054293479281:web:658ac224e0c697485af0d0",
    measurementId: "G-1JCVW3VKK4"
  };

  const routeNameRef = React.useRef();
  const navigationRef = React.useRef();

  useEffect(() => {
    if (!firebase.apps.length) {
      firebase.initializeApp(firebaseConfig);
    }

    const getToken = async () => {
      const token = await getMessaging().getToken();
      console.log("FCM Token:", token);
    };

    getToken();
  }, []);

  useEffect(() => {
    Clarity.initialize('rwpz9doz6r', {
      logLevel: Clarity.LogLevel.None, // Note: Use "LogLevel.Verbose" value while testing to debug initialization issues.
    });
  }, [])

  useEffect(() => {

    const getDeviceLanguage = async () => {
      try {
        const language = await getLocales();
        console.log("Device Language:", language[0].languageCode);
        await saveKey(ASYNC_KEY_CONSTANTS.LANGUAGESTORE, language[0].languageCode);
        I18n.changeLanguage(ASYNC_KEY_CONSTANTS.LANGUAGESTORE)
      } catch (error) {
        console.error("Error fetching device language:", error);
      }
    };

    getDeviceLanguage();

  }, [])

  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={() => {
        routeNameRef.current = navigationRef.current.getCurrentRoute().name;
      }}
      onStateChange={async () => {
        const previousRouteName = routeNameRef.current;
        const currentRouteName = navigationRef.current.getCurrentRoute().name;

        if (previousRouteName !== currentRouteName) {
          await analytics().logScreenView({
            screen_name: currentRouteName,
            screen_class: currentRouteName,
          });
        }
        routeNameRef.current = currentRouteName;
      }}
    >
      <AppNavigation />
    </NavigationContainer>
  );
};

export default App;
