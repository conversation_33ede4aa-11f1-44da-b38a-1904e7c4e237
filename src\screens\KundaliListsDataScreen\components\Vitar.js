import { View, Text, SafeAreaView, FlatList} from 'react-native'
import React from 'react'
import styles from '../styles/Vitar.styles'

const Vitar = ({apidata}) => {
    const VitarData = [
        {
          id: 1,
          details: apidata.horoscope_finance || ''
        },
      ];
    
      const VitarRenderItem = ({item, index}) => {
        return (
          <View style={styles.doshaContanier}>
            {item.details && (
              <Text style={styles.itemDetailsStyle}>{item.details}</Text>
            )}
          </View>
        );
      };
    
      return (
        <SafeAreaView style={styles.contanier}>
          <Text style={styles.titleText}>Finance</Text>
    
          <FlatList
            data={VitarData}
            renderItem={VitarRenderItem}
            keyExtractor={(item, index) => item.id.toString()}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      );
}

export default Vitar