import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';
import { Dimensions } from 'react-native';

const {margins, paddings} = metrics;
const {height,width} = Dimensions.get('window')

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    ...margins.mB20,
  },
  howCanHelpContanier: {
    justifyContent: 'space-between',
    height: DeviceUiInfo.moderateScale(74),
    // borderWidth: 1,
    ...margins.mB20,
  },
  howCanHelpTitleText: {
    fontSize: fontSize.f18,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
  },
  howCanHelpSubtitleText: {
    fontSize: fontSize.f14,
    color: colors.arrowDown,
    fontFamily: fonts.MontserratMedium,
  },
  renderItemContanier: {
    // borderWidth: 1,
    ...paddings.pV17,
    ...paddings.pH20,
    shadowColor: colors.lightGrey,
    elevation: 4,
    backgroundColor: colors.white,
    ...margins.mB16,
    borderRadius: DeviceUiInfo.moderateScale(30),
    // height: DeviceUiInfo.moderateScale(108),
    height: height * 0.155 ,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    paddingBottom: height * 0.03
  },
  TextContanier: {
    flexDirection: 'column',
    height: DeviceUiInfo.moderateScale(74),
    // borderWidth: 1,
  },
  renderNameText: {
    fontSize: fontSize.f16,
    color: colors.dividerBlack,
    fontFamily: fonts.MontserratSemibold,
  },
  renderSubText: {
    ...margins.mT4,
    color: colors.arrowDown,
    fontFamily: fonts.MontserratMedium,
    fontSize: fontSize.f14,
  },
  renderContactText: {
    ...margins.mT6,
    color: colors.purple,
    fontFamily: fonts.MontserratSemibold,
    fontSize: fontSize.f14,
  },
  renderArrowContanier: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flex: 1,
  },
  renderArrow: {
    height: DeviceUiInfo.moderateScale(18),
    width: DeviceUiInfo.moderateScale(18),
    transform: [{rotate: '270deg'}],
  },
});

export default styles;
