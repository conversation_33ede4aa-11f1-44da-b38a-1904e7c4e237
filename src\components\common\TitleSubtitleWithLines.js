import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors} from '../../theme/colors';
import {fonts, fontSize} from '../../theme/fonts';
import metrics from '../../theme/metrics';
import DeviceUiInfo from '../../utils/DeviceUiInfo';
import { Dimensions } from 'react-native';

const {paddings, margins} = metrics;
const {height,width} = Dimensions.get('window')

const TitleSubtitleWithLines = ({title, subtitle}) => {

  return (
    <View style={styles.contanier}>
      <View style={styles.titleContanier}>
        <View style={[styles.LineDimondContanier]}>
          <View style={styles.line} />
          <View style={[styles.Diamond]} />
        </View>

        <View style={styles.textTitleContanier}>
          <Text style={styles.titleText}>{title}</Text>
        </View>

        <View style={styles.LineDimondContanier}>
          <View style={[styles.Diamond]} />
          <View style={styles.line} />
        </View>
      </View>

      {subtitle && <Text style={styles.subtitleText}>{subtitle}</Text>}
    </View>
  );
};

export default TitleSubtitleWithLines;

const styles = StyleSheet.create({
  contanier: {
    // flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
  },
  titleContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    ...margins.mB10,
    // ...paddings.pH24,
  },
  LineDimondContanier: {
    // flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  line: {
    width: DeviceUiInfo.moderateScale(50),
    backgroundColor: colors.primary,
    height: 3,
  },
  Diamond: {
    // position: 'absolute',
    // top: DeviceUiInfo.moderateScale(-4),
    width: DeviceUiInfo.moderateScale(12),
    height: DeviceUiInfo.moderateScale(12),
    backgroundColor: colors.primary,
    transform: [{rotate: '45deg'}],
  },
  textTitleContanier: {
    width: '55%',
    // ...paddings.pH24,
    ...margins.mH15,
  },
  titleText: {
    color: colors.primary,
    fontFamily: fonts.MontserratBold,
    fontSize: fontSize.f22,
    textAlign: 'center',
    lineHeight: height > 800 ? 35 : 25,
  },
  subtitleText: {
    textAlign: 'center',
    color: colors.poweredByTextColor,
    fontSize: fontSize.f12,
    fontFamily: fonts.MontserratRegular,
  },
});
