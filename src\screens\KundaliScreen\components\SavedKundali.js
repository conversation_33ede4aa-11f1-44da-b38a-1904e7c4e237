import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import styles from '../styles/SavedKundali.styles';
import I18n from '../../../utils/language/i18nextConfig';
import {colors} from '../../../theme/colors';
import metrics from '../../../theme/metrics';
import {getKey} from '../../../helper/cookies';
import Apis from '../../../services/apiList';
import {ASYNC_KEY_CONSTANTS} from '../../../constants/AppConstants';
import {GetServices} from '../../../services/commonApiMethod';
import AppRoutes from '../../../constants/AppRoutes';

const SavedKundali = ({navigation}) => {
  const {margins, paddings} = metrics;
  const [apiData, setApiData] = useState([]);
  const [loading, setLoading ] = useState(false);

  const api = new Apis();

  useEffect(() => {
    kundlilist();
  }, []);

  const kundlilist = async () => {
    setLoading(true);
    const user_id_Obj = await getKey(ASYNC_KEY_CONSTANTS.USER_DETAIL);
    const user_id = JSON.parse(user_id_Obj).ID;
    const payload = {
      url: `${api.kundlilist}?user_id=${user_id}`,
    };

    await GetServices(payload)
      .then(res => {
        if (res) {
          setApiData(res?.data?.data || []);
          setLoading(false)
        } else {
          setApiData([]); 
          setLoading(false)
          Alert.alert('You have no list of Kundli');
        }
      })
      .catch(err => {
        Alert.alert('Error', 'Please try again later');
      });
  };

  const savedKundaliRenderdata = ({item, index}) => {
    return (
      <>
        <TouchableOpacity
          onPress={() => {
            navigation.navigate(AppRoutes.KUNDALI_LIST_DATA_SCREEN, {
              userId: item.id,
              name: item.name,
              createdAt: item.created_at || 'N/A',
              place: item.place || 'Unknown',
              apidata: item
            });
          }}
          style={{flexDirection: 'row', alignItems: 'center'}}>
          <View style={styles.avtarIconContanier}>
            <Image
              style={styles.avtarIconSize}
              source={item.avtarIcon}
              resizeMode="contain"
            />
          </View>
          <View style={{flexDirection: 'column', marginLeft: 10}}>
            <Text style={styles.nameText}>{item.name}</Text>
            <Text style={styles.subtitleText}>{item.created_at}</Text>
          </View>
          <Text style={styles.placeText}>{item.place}</Text>
          {/* <View style={styles.threeDotContanier}>
            <Image
              style={styles.threeDotStyle}
              resizeMode="contain"
              source={images.threeDotIcon}
            />
          </View> */}
        </TouchableOpacity>
        {index < apiData.length - 1 && (
          <View style={styles.savedKundaliDivider} />
        )}
      </>
    );
  };

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.contanier}>
      {/* recent-kundali-header */}
      <Text style={styles.headerText}>{I18n.t('saved_kundali')}</Text>

      <View>
        {loading ? (
            <>
            <View style={{flex:1, justifyContent:'center',alignItems:'center'}}>
              <ActivityIndicator size='small' color={colors.primary}/>
            </View>
            </>
        ) : apiData.length === 0 ? (
            <>
              <View style={styles.nosavedkundaliTextContainer}>
                <Text style={styles.nosavedkundaliText}> {I18n.t('no_saved_kundali')}</Text>
              </View>
            </>
        ) : (
          <>
          <FlatList
          data={apiData}
          renderItem={savedKundaliRenderdata}
          keyExtractor={(item, index) => index.toString()}
          />
          </>
        )}
      </View>

      {/* Display the formData, lat, and long if available
      {formData && (
        <View style={{marginTop: 20}}>
          <Text style={{fontSize: 18, fontWeight: 'bold'}}>Form Data:</Text>
          <Text>Name: {formData.name}</Text>
          <Text>Place: {formData.birthPlace}</Text>
          <Text>Latitude: {lat}</Text>
          <Text>Longitude: {long}</Text>
        </View>
      )} */}
    </ScrollView>
  );
};

export default SavedKundali;
