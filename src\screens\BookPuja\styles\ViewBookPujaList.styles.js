import {StyleSheet} from 'react-native';
import metrics from '../../../theme/metrics';
import {colors} from '../../../theme/colors';
import DeviceUiInfo from '../../../utils/DeviceUiInfo';
import {fonts, fontSize} from '../../../theme/fonts';
import { Dimensions } from 'react-native';

const {height,width} = Dimensions.get('window')

const {margins, paddings} = metrics;

const styles = StyleSheet.create({
  contanier: {
    flex: 1,
    backgroundColor: colors.background,
    ...paddings.pH24,
  },
  appTitleContanier: {
    ...margins.mT16,
    // borderWidth: 1,
  },
  renderItemContanier: {
    alignItems: 'center',
    ...paddings.p10,
    elevation: 0,
    borderRadius: DeviceUiInfo.moderateScale(10),
    backgroundColor: colors.white,
    flexDirection: 'row',
    flex: 1,
  },
  renderIcon: {
    height: DeviceUiInfo.moderateScale(45),
    width: DeviceUiInfo.moderateScale(45),
    borderRadius: DeviceUiInfo.moderateScale(13)
  },
  textContainer: {
    ...margins.mL10,
    flexDirection: 'column',
    // maxWidth: DeviceUiInfo.moderateScale(230),
  },
  renderPujaText: {
    fontFamily: fonts.MontserratSemibold,
    color: colors.dividerBlack,
    fontSize: fontSize.f16,
    width: width/3
  },
  subDetailsText: {
    ...margins.mT5,
    fontFamily: fonts.MontserratMedium,
    color: colors.poweredByTextColor,
    fontSize: fontSize.f10,
    maxWidth: DeviceUiInfo.moderateScale(100),
  },
  buttonStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flex: 1,
    alignItems:"center"
  },
  viewDetailsStyle: {
    borderRadius: DeviceUiInfo.moderateScale(70),
    backgroundColor: colors.primary,
    ...paddings.pV6,
    ...paddings.pH12,
  },
  viewDetailsText: {
    fontFamily: fonts.MontserratMedium,
    color: colors.white,
    fontSize: fontSize.f10,
  },
  tickcircle:{
    height: height * 0.025,
    backgroundColor: colors.white,
    width: width * 0.05,
    borderWidth: 1.5,
    borderRadius: (height * 0.06) / 2, 
    ...margins.mL4,
    alignItems:'center',
    justifyContent:'center'
  },
  dateAndStateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...margins.mT6
  },
  noDataContainer:{
    justifyContent: 'center',
    alignItems: 'center',
    ...margins.mT24
  },
  noDataText: {
    fontSize: fontSize.f16,
    color: colors.textGray,
    textAlign: 'center',
    paddingHorizontal: 20,
    fontFamily: fonts.MontserratSemibold
  },
  
});

export default styles;
